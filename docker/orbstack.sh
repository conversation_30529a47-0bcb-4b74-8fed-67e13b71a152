#!/bin/sh

DOMAIN='app.calibra2020.orb.local'

TMP_DOCKER_COMPOSE_OVERRIDE_FILE="docker-compose.override.yml.tmp"
DOCKER_COMPOSE_OVERRIDE_FILE="docker-compose.override.yml"
TMP_CONFIG_LOCAL_NEON_FILE="app/config/config.local.neon.tmp"
CONFIG_LOCAL_NEON_FILE="app/config/config.local.neon"

# Check if docker-compose.override.yml.tmp exists
if [ -f $TMP_DOCKER_COMPOSE_OVERRIDE_FILE ]; then
  # Copy docker-compose.override.yml.tmp to docker-compose.override.yml
  cp $TMP_DOCKER_COMPOSE_OVERRIDE_FILE $DOCKER_COMPOSE_OVERRIDE_FILE
else
  echo "File docker-compose.override.yml.tmp does not exist."
  exit 1
fi

# Check if app/config/config.local.neon.tmp exists
if [ -f $TMP_CONFIG_LOCAL_NEON_FILE ]; then
  # Copy app/config/config.local.neon.tmp to app/config/config.local.neon
  cp $TMP_CONFIG_LOCAL_NEON_FILE $CONFIG_LOCAL_NEON_FILE
else
  echo "File app/config/config.local.neon.tmp does not exist."
  exit 1
fi

# Replace the domain in the config.local.neon file by the DOMAIN environment variable
sed -i '' "s|domain: .*|domain: https://${DOMAIN}|" $CONFIG_LOCAL_NEON_FILE

# Set includes to environment.docker.neon
sed -i '' "s|includes:.*|includes:\n\t- environment.docker.neon|" $CONFIG_LOCAL_NEON_FILE
