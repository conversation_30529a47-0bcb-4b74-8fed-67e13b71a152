#!/bin/bash

source ./tunnel.sh onlyVariables

CONFIG_PATH="/var/www/calibra-stage.vs3.superkoderi.cz/shared/app/config/config.local.neon"
CONTAINER_NAME="db-init"

echo 'Creating tunnel...'

# run tunnel and continue with s<PERSON>rip<PERSON> without user input
./tunnel.sh &

echo "Connection to $REMOTE_USER_HOST established..."

# Uložení PID procesu tunelu
TUNNEL_PID=$!

extract_config_value() {
    local key=$1
    # First get the entire line
    local raw_value=$(ssh -o ConnectTimeout=10 "${REMOTE_USER_HOST}" "grep -A 3 'database:' $CONFIG_PATH | grep '$key:' | cut -d: -f2")

    # Clean both quoted and unquoted values
    local value=$(echo "$raw_value" | sed -e "s/^[[:space:]]*'//" -e "s/'[[:space:]]*$//" -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//' | tr -d '\r\n')

    if [ $? -ne 0 ] || [ -z "$value" ]; then
        echo "Error retrieving $key or no $key found."
        exit 1
    fi
    printf '%s' "$value"
}

DB_PASSWORD=$(extract_config_value "password")
DB_NAME=$(extract_config_value "database")
DB_USER=$(extract_config_value "user")
DB_HOST=$(extract_config_value "host")


echo "Data successfully extracted from remote config..."
echo "Database Name: ${DB_NAME}"

ssh "${REMOTE_USER_HOST}" "mysqldump -h 127.0.0.1 -P 3306 -u ${DB_USER} -p'${DB_PASSWORD}' ${DB_NAME}" > docker/db/dump.sql

echo 'Dump created...'
docker cp docker/db/dump.sql "${CONTAINER_NAME}:/docker/db/dump.sql"

echo 'Dump DONE...'
