all: up
.PHONY: all up stop install install-php install-front build-front migrate migrations-reset xdebug-on xdebug-off

ROOT_DIR := $(strip $(shell dirname "$(realpath $(firstword $(MAKEFILE_LIST)))"))


start:
	sh ./docker/db/make-dump.sh
	make up
	make install
	make build

up:
	docker compose up -d

stop:
	docker compose stop

install: install-php install-front

install-php: up
	docker compose run --rm -it app php composer.phar install  --no-dev --classmap-authoritative --ignore-platform-req=ext-soap --ignore-platform-req=ext-zip

install-front: up
	docker compose run --rm -it front npm install
	docker compose run --rm -it front npm rebuild node-sass

#install-admin: src/admin/package.json src/admin/package-lock.json
#	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:10 npm install

#install-admin-new: up
#	docker compose run --rm -it admin npm install

build: build-front

build-front: install-front
	docker compose run --rm -it front npm run build

#build-admin: install-admin
#	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:10 npx gulp

#build-admin-new: install-admin-new
#	docker compose run --rm -it admin npm run build

migrate: install-php
	docker compose run --rm -it app php bin/console migrations:continue

migrations-reset: install-php
	docker compose run --rm -it app php bin/console migrations:reset

populate-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:purge -f
	docker compose run --rm -it app php bin/console elastic:index:create -psc

xdebug-on:
	SUPERADMIN_XDEBUG=on docker compose up --force-recreate --no-deps -d app

xdebug-off:
	SUPERADMIN_XDEBUG=off docker compose up --force-recreate --no-deps -d app

rebuild:
	docker compose up --build

orbstack:
	sh ./docker/orbstack.sh
	make start

docker-start:
	sh ./docker/docker.sh
	make start

diff:
	sh ./docker/db/make-dump.sh
	sh ./docker/db/compare-db.sh

drop-db:
	sh ./docker/db/drop-database.sh

phpstan:
	docker compose run --rm -it app php composer.phar run-script phpstan

subscription-order:
	docker compose run --rm -it app php bin/console subscription:order
	#docker compose run --rm -it app php bin/console noviko:order:export

subscription-report:
	docker compose run --rm -it app php bin/console subscription:report

send-retry-payment-mail:
	docker compose run --rm -it app php bin/console SendRetryPaymentMail

storno-order-after-retry-payment:
	docker compose run --rm -it app php bin/console StornoOrderAfterRetryPayment

noviko-order-export:
	docker compose run --rm -it app php bin/console noviko:order:export

price-update:
	docker compose run --rm -it app php bin/console eshop:variant:prices

eshop-logs–init:
	docker compose run --rm -it app php bin/console eshop:logs:init

noviko-price:
	docker compose run --rm -it app php bin/console noviko:product:priceHD

generate-order-invoice-number:
	docker compose run --rm -it app php bin/console GenerateOrderInvoiceNumber

gopay-payment-status:
	docker compose run --rm -it app php bin/console gopay:payment-tester -p XXXXXXX

sub-order-as-paid:
	docker compose run --rm -it app php bin/console subscription:change-order-as-paid -s 922

product-user-score:
	docker compose run --rm -it app php bin/console product:user:score

task-command:
	docker compose run --rm -it app php bin/console task

