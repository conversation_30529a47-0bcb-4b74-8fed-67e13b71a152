<?php declare(strict_types = 1);

namespace SuperKoderi\GoPay;

use App\Model\CreditNote;
use App\Model\Order;
use App\Model\OrderItem;
use App\Model\OrderModel;
use App\Model\Orm;
use App\Model\Subscription;
use App\Model\SubscriptionOrder;
use App\Model\SubscriptionPaymentModel;
use Exception;
use GoPay\Http\Response;
use GoPay\Payments;
use Sentry\Severity;
use SuperKoderi\ConfigService;
use SuperKoderi\LogicException;
use SuperKoderi\SkLinkGenerator;
use Throwable;
use Tracy\Debugger;
use Tracy\Dumper;
use Tracy\ILogger;
use function GoPay\payments;
use function is_object;
use function round;
use function json_encode;
use function Sentry\captureMessage;

class Client
{

	private string $goid;

	private string $clientId;

	private string $clientSecret;

	private string $language;

	private bool $isProduction;

	public function __construct(
		string $langCode,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
		private readonly OrderModel $orderModel,
		private readonly SubscriptionPaymentModel $subscriptionPaymentModel,
		private readonly SkLinkGenerator $skLinkGenerator,
	)
	{
		//      $urlDomain = $this->configService->getParam('domainUrl');
		$gopayConfig = $this->configService->getParam('gopay', $langCode);

		$this->goid = $gopayConfig['goid'];
		$this->clientId = $gopayConfig['clientId'];
		$this->clientSecret = $gopayConfig['clientSecret'];
		$this->language = $gopayConfig['language'];
		//      $this->webUrl = preg_replace('/\/$/', '', $urlDomain);

		$this->isProduction = $gopayConfig['isProduction'];
	}

	public function getClient(): Payments
	{
		return payments([
			'goid' => $this->goid,
			'clientId' => $this->clientId,
			'clientSecret' => $this->clientSecret,
			'isProductionMode' => $this->isProduction,
			'language' => $this->language,
		//          'cache' => new PrimitiveFileCache()
		]);
	}

	public function getStatus(string $paymentId): Response|null
	{
		$client = $this->getClient();

		$res = $client->getStatus($paymentId);
		if (is_object($res) && $res::class == 'GoPay\Http\Response' && $res->statusCode == 200) {
			return $res;
		}

		return null;
	}

	public function createPayment(Order $order, bool $retriedPayment = false, string|null $paymentInstrument = null): string|null
	{
		$client = $this->getClient();

		$mutation = $order->mutation;

		$items = [];
		foreach ($order->items as $item) {

			if ($item->type == OrderItem::TYPE_PAYMENT) {
				continue;
			}

			if ($item->type == OrderItem::TYPE_TRANSPORT) {
				$type = 'DELIVERY';// ITEM, DELIVERY, DISCOUNT
				$url = '';
			} else {
				$type = 'ITEM';

				$url = isset($item->variant) && $item->variant !== false
					? $this->skLinkGenerator->linkFromAlias($item->variant->alias, $this->orm->getMutation()?->langCode)
					: '';
			}

			$items[] = [
				'type' => $type,
				'product_url' => $url,
				'ean' => $item->id,
				'count' => $item->amount,
				'name' => $item->name,
				'amount' => round($item->totalPriceDPH, $mutation->roundPositionsExtended) * 100,
				'vat_rate' => '21',
			];
		}

		$responseUrl = $this->orm->tree->getByUid('gopay-response');
		if (!$responseUrl->id) {
			throw new Exception('Page with uid gopay-response missing');
		}

		$callback = [
			'return_url' => $this->skLinkGenerator->linkFromAlias($responseUrl->alias, $this->orm->getMutation()?->langCode, ['active' => 1]),
			'notification_url' => $this->skLinkGenerator->linkFromAlias($responseUrl->alias, $this->orm->getMutation()?->langCode, ['active' => 0]),
		];

		$allowedInstruments = ['PAYMENT_CARD', 'BANK_ACCOUNT', 'GPAY'];
		if ($paymentInstrument === 'APPLE_PAY') {
			$allowedInstruments[] = 'APPLE_PAY';
		}

		$payer = [
			'allowed_payment_instruments' => ($order->subscriptionOrder instanceof SubscriptionOrder) ? ['PAYMENT_CARD', 'GPAY', 'APPLE_PAY'] : $allowedInstruments,
			'default_payment_instrument' => 'PAYMENT_CARD',
			'contact' => [
				'email' => $order->email,
			],
		];

		if ($paymentInstrument) {
			$payer['default_payment_instrument'] = $paymentInstrument;
		}

		Debugger::log(json_encode($payer), 'gopay-chosen-payment-instrument');

		// needs to be double round for SUPCAL-583 -> 4079.************* instead of 40.80
		$amount = round($order->totalPriceDPH, $mutation->roundPositionsExtended);
		$amount = round($amount * 100);

		$payment = [
			'amount' => $amount,
			'payer' => $payer,
			'currency' => $mutation->currency ?? 'EUR',
			'order_number' => $order->number,
			'items' => $items,
			'callback' => $callback,
			'lang' => $this->language,
		];

		if ($order->subscription instanceof Subscription) {
			$payment['recurrence'] = [
				'recurrence_cycle' => 'ON_DEMAND',
				'recurrence_date_to' => '2099-12-31',
			];
		}

		set_error_handler(function ($err_severity, $err_msg, $err_file, $err_line)
		{
			Debugger::log($err_msg, ILogger::ERROR);
		}, E_WARNING);

		$res = $client->createPayment($payment);
		restore_error_handler();

		if (is_object($res) && $res::class == 'GoPay\Http\Response' && $res->statusCode == 200) {

			$this->orderModel->savePaymentCreation($order, $res->json, false, $retriedPayment);

			if ($order->subscription instanceof Subscription) {
				$this->subscriptionPaymentModel->createPayment($order, $res->json);
			}

			if ($res->json['gw_url'] === '') {
				// logging of invalid gw_url
				try {
					Debugger::log($res->statusCode, ILogger::ERROR);
					Debugger::log($res->json, ILogger::ERROR);
				} catch (Throwable $t) {
					Debugger::log($t, ILogger::ERROR);
				}
			}

			return $res->json['gw_url'];
		} else {
			// logging in case of other status code or invalid response
			try {
				$message = sprintf('Creation of on-line payment failed, status=%d', $res->statusCode);
				captureMessage($message, Severity::fatal());
				Debugger::log($res->statusCode, ILogger::ERROR);
				Debugger::log($res->json, ILogger::ERROR);
			} catch (Throwable $t) {
				Debugger::log($t, ILogger::ERROR);
			}
		}

		return null;
	}

	public function createRecurrencePayment(Order $order): string|null
	{
		// payment for subscription for order to be paid automaticaly
		$client = $this->getClient();

		$mutation = $order->mutation;

		$responseUrl = $this->orm->tree->getByUid('gopay-response');
		if (!$responseUrl->id) {
			throw new Exception('Page with uid gopay-response missing');
		}

		$amount = round($order->totalPriceDPH, $mutation->roundPositionsExtended);
		$amount = round($amount * 100);

		if ($order->subscriptionOrder instanceof SubscriptionOrder) {
			$payment = [
				'amount' => $amount,
				'currency' => $mutation->currency ?? 'EUR',
				'order_number' => $order->number,
				'order_description' => 'payment subs: ' . $order->subscription->name . ', seq: ' . $order->subscriptionOrder?->sequence,
			];
		} else {
			throw new Exception('createRecurrencePayment called for order without subscription: orderId' . $order->id);
		}

		$res = $client->createRecurrence($order->subscription->recurringPayment->paymentId, $payment);

		if (is_object($res) && $res::class == 'GoPay\Http\Response' && $res->statusCode == 200) {
			$this->orderModel->savePaymentCreation($order, $res->json);
			$this->subscriptionPaymentModel->createPayment($order, $res->json);

			if ($res->json['gw_url'] === '') {
				// logging of invalid gw_url
				try {
					Debugger::log($res->statusCode, ILogger::ERROR);
					Debugger::log($res->json, ILogger::ERROR);
				} catch (Throwable $t) {
					Debugger::log($t, ILogger::ERROR);
				}
			}

			return $res->json['gw_url'];
		} else {
			// logging in case of other status code or invalid response
			try {
				$this->subscriptionPaymentModel->logPaymentError($order->subscription, json_encode($res->json));
				Debugger::log($res->statusCode, ILogger::ERROR);
				Debugger::log($res->json, ILogger::ERROR);
			} catch (Throwable $t) {
				$this->subscriptionPaymentModel->logPaymentError($order->subscription, $t->getMessage());
				Debugger::log($t, ILogger::ERROR);
			}
		}

		return null;
	}

	/**
	 * https://help.gopay.com/cs/tema/integrace-platebni-brany/technicky-popis-integrace-platebni-brany/refundace
	 * https://doc.gopay.com/cs/#refundace-platby
	 *
	 *
	 * GoPay\Http\Response
	 * ----------------------------
	 *    rawBody private => "{"id":3114914478,"result":"FINISHED"}"
	 *    statusCode => "200"
	 *    json => array
	 *        id => 3114914478
	 *        result => "FINISHED"
	 *
	 * @throws LogicException
	 */
	public function refundPayment(Order $order, CreditNote $creditNote): bool
	{
		if ($this->isPayed($order)) {
			$client = $this->getClient();

			$data = [
				'amount' => $creditNote->totalPriceDPH * 100,
			];
			$res = $client->refundPayment($order->paymentId, $data);

			if (is_object($res) && $res::class == 'GoPay\Http\Response' && $res->statusCode == 200) {
				//Debugger::log(Dumper::toText($res), 'gopay-refund');
				return true;
			} else {
				Debugger::log(Dumper::toText($res), 'gopay-refund');

				throw new LogicException('GoPay refundPayment failed');
			}
		} else {
			return false;
		}
	}

	public function isPayed(Order $order): bool
	{
		$client = $this->getClient();
		$res = $client->getStatus($order->paymentId);
		if (is_object($res) && $res::class == 'GoPay\Http\Response' && $res->statusCode == 200) {
			if (isset($res->json['state']) && $res->json['state'] == 'PAID') {
				return true;
			}
		}

		return false;
	}

	public function refreshPaymentStatus(Order $order, string $paymentId = null): bool
	{
		$client = $this->getClient();
		$res = $client->getStatus($paymentId ?? $order->paymentId);

		if (is_object($res) && $res::class == 'GoPay\Http\Response' && $res->statusCode == 200) {
			$this->orderModel->savePaymentCreation($order, $res->json);

			return true;
		}

		return false;
	}

	public function cancelRecurringPayments(string $paymentId): bool
	{
		$client = $this->getClient();
		$res = $client->voidRecurrence($paymentId);

		if (is_object($res) && $res::class == 'GoPay\Http\Response' && $res->statusCode == 200) {
			if ($res->json['id'] == $paymentId && $res->json['result'] == 'FINISHED') {
				return true;

			}
		}

		return false;
	}

}


interface IClientFactory
{

	function create(string $langCode): Client;

}
