<?php declare(strict_types=1);

namespace SuperKoderi\Model\PPL;

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use SuperKoderi\Dpd\WsException;

class PplApiClient
{

	CONST FORWARD_TO_PARTNER_CODE = 720;
	CONST DELIVERED_CODE = 450;

	protected $soap;

	protected Cache $cache;

	public function __construct(
		protected readonly string $apiUrl,
		protected readonly array $apiKeys,
		protected readonly Storage $cacheStorage
	)
	{
		$this->cache = new Cache($this->cacheStorage, 'ppl_api');
	}

	public function getSoapClient(): \SoapClient
	{
		if ( ! $this->soap) {
			$this->soap = new \SoapClient($this->apiUrl);
		}

		return $this->soap;
	}

	/**
	 * Get available API keys
	 */
	public function getApiKeys(): array
	{
		return $this->apiKeys;
	}

	public function getPackages(string $langCode, string|array $packNumber): array
	{
		$token = $this->login($langCode);

		$soap = $this->getSoapClient();
		$result = $soap->GetPackages([
			'Auth' => [
				'AuthToken' => $token
			],
			'Filter' => [
				'PackNumbers' => is_array($packNumber) ? $packNumber : [$packNumber]
			]
		]);

		$data = $result->GetPackagesResult->ResultData->MyApiPackageOut ?? [];
		if ( ! $data) {
			$packNumber = is_array($packNumber) ? implode(', ', $packNumber) : $packNumber;
			throw new PacketNotFoundException("No data found for package number {$packNumber}");
		}

		return is_array($data) ? $data : [$data];
	}

	public function getPartnerLink(string $langCode, string $packNumber):? string
	{
		$package = $this->getPackages($langCode, $packNumber);

		if (is_array($package)) {
			$package = $package[0];
		}

		$link = null;
		foreach ($package->PackageStatuses->MyApiPackageOutStatus ?? [] as $status) {
			if ($status->StaID ?? null === self::FORWARD_TO_PARTNER_CODE) {
				if (preg_match('/<a\s+href="([^"]+)"/', $status->StatusName, $matches)) {
					$link = $matches[1];
					break;
				}
			}
		}

		return $link;
	}


	public function login(string $lang): string {
		if (! $token = $this->cache->load("ppl_api_token_{$lang}")) {
			$soap = $this->getSoapClient();
			$result = $soap->Login([
				'Auth' => [
					'CustId' => $this->apiKeys[$lang]['CustId'],
					'UserName' => $this->apiKeys[$lang]['UserName'],
					'Password' => $this->apiKeys[$lang]['Password']
				]
			]);

			$token = $result->LoginResult->AuthToken;

			$this->cache->save("ppl_api_token_{$lang}", $token, [
				Cache::EXPIRE => '20 minutes',
			]);
		}

		return $token;
	}

	/**
	 * Check if parcel shop with given code exists
	 * Based on PPL API WSDL, GetParcelShops requires Filter object with ParcelShopFilter structure
	 */
	public function parcelShopExists(string $langCode, string $parcelCode): bool
	{
		try {
			$token = $this->login($langCode);
			$soap = $this->getSoapClient();

			// Method 1: Try to get specific parcel shop by code using correct Filter structure
			try {
				$result = $soap->GetParcelShops([
					'Auth' => [
						'AuthToken' => $token
					],
					'Filter' => [
						'Code' => $parcelCode
					]
				]);

				// Check if we got any results
				$data = $result->GetParcelShopsResult->ResultData->MyApiParcelShop ??
						$result->GetParcelShopsResult->ResultData ??
						null;

				if ($data && !empty($data)) {
					// Verify the returned shop actually matches our code
					$shops = is_array($data) ? $data : [$data];
					foreach ($shops as $shop) {
						// Check the ParcelShopCode property (based on WSDL structure)
						$shopCode = $shop->ParcelShopCode ?? $shop->Code ?? null;
						if ($shopCode) {
							// Compare codes - handle both with and without KM prefix
							$cleanShopCode = str_starts_with($shopCode, 'KM') ? substr($shopCode, 2) : $shopCode;
							$cleanParcelCode = str_starts_with($parcelCode, 'KM') ? substr($parcelCode, 2) : $parcelCode;

							// Match if either the full codes match or the cleaned codes match
							if ($shopCode === $parcelCode ||
								$cleanShopCode === $cleanParcelCode ||
								$shopCode === ('KM' . $cleanParcelCode) ||
								('KM' . $cleanShopCode) === $parcelCode) {
								return true;
							}
						}
					}
				}
			} catch (\SoapFault $e) {
				// If specific code search fails, the parcel shop likely doesn't exist
				if (strpos($e->getMessage(), 'not found') !== false ||
					strpos($e->getMessage(), 'does not exist') !== false ||
					strpos($e->getMessage(), 'invalid') !== false ||
					strpos($e->getMessage(), 'Unknown') !== false) {
					return false;
				}
			}

		} catch (\Exception $e) {
			error_log('PPL parcelShopExists error for code ' . $parcelCode . ': ' . $e->getMessage());
		}

		// If we can't validate or find the parcel shop, assume it doesn't exist
		return false;
	}

	/**
	 * Get country code for language code
	 */
	private function getCountryCodeForLang(string $langCode): ?string
	{
		$mapping = [
			'cs' => 'CZ',
			'sk' => 'SK',
			'pl' => 'PL'
		];

		return $mapping[$langCode] ?? null;
	}

}
