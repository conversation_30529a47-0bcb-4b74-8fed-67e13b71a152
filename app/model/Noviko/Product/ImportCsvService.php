<?php

namespace SuperKoderi\Noviko\Product;

use App\Model\Mutation;
use App\Model\Orm;
use App\Model\Parameter;
use App\Model\ParameterValue;
use App\Model\ParameterValueModel;
use App\Model\Product;
use App\Model\ProductImage;
use App\Model\ProductModel;
use App\Model\ProductVariant;
use League\Csv\CharsetConverter;
use League\Csv\Reader;
use Nette\Utils\Strings;
use SuperKoderi\LogicException;
use SuperKoderi\StringHelper;
use Throwable;
use Tracy\Debugger;
use function in_array;
use function Composer\Autoload\includeFile;

/**
 * @package SuperKoderi\Noviko\Product
 *
 * dogSize => 78
 * productLine => 113
 * animalType => 116
 * protein => 117
 * ageDog => 118
 * specialMark => 119
 * productType => 120
 * feedTypeDog => 121
 * feedTypeCat => 122
 * ageCat => 124
 */
class ImportCsvService
{
	/** @var Orm */
	protected $orm;

	/** @var ParameterValueModel */
	protected $parameterValueModel;

	/** @var  ProductModel */
	protected $productModel;

	/**
	 *    UID =>
	 *        parameter => App\Model\Parameter
	 *        values =>
	 *            Pes => App\Model\ParameterValue
	 *            Kočka => App\Model\ParameterValue
	 *
	 * @var array
	 */
	protected $parameters = [];

	/**
	 * UID => CSV_COLNAME
	 *
	 * A = hodnota patří parametru podle typu zvířete
	 *
	 * @var array
	 */
	private $csvParameters = [
		'productLine' => 'CALIBRA_PRODUCTS_LINE', // produktová řada
		'animalType' => 'CALIBRA_ANIMAL', // zvíře
		'ageDog' => 'CALIBRA_AGE_CATEGORY', // věk psa, A
		'ageCat' => 'CALIBRA_AGE_CATEGORY', // věk kočky, A
		'productType' => 'CALIBRA_CATEGORY', // typ produktu
		'protein' => 'CALIBRA_MAIN_PROTEIN', // preferovaný protein
		'feedTypeDog' => 'SPECIFICKE_URCENI_KRMIVA', // Zaměření krmiva pro psy, A
		'feedTypeCat' => 'SPECIFICKE_URCENI_KRMIVA', // Zaměření krmiva pro kočky, A
		'dogSize' => 'CALIBRA_BODY_SIZE', // Velikost plemene psa
	];

	public function __construct(Orm $orm, ParameterValueModel $parameterValueModel, ProductModel $productModel)
	{
		$this->orm = $orm;
		$this->parameterValueModel = $parameterValueModel;
		$this->productModel = $productModel;
		$parameters = $orm->parameter->findBy(['category' => 'param_category_product'])->fetchPairs('uid');

		foreach ($parameters as $parameter) {
			/** @var Parameter $parameter */
			$p = new \stdClass();
			$p->parameter = $parameter;
			$p->values = $parameter->options->toCollection()->fetchPairs('value');
			//var_dump($p->values);
			$this->parameters[$parameter->uid] = $p;
		}
	}

	/**
	 * Import obálek produktů z CSV
	 *
	 * 0 => "KOD_ZBO"
	 * 0 => "KOD_CALIBRA"
	 * 1 => "EAN"
	 * 2 => "NAZEV"
	 * 3 => "VYROBCE"
	 * 4 => "CENA_KATALOG"
	 * 5 => "CENA_KATALOG_DPH"
	 * 6 => "DPH"
	 * 7 => "HMOTNOST_G"
	 * 8 => "INFO_SHORT"
	 * 9 => "INFO"
	 * 10 => "CALIBRA_ANIMAL"
	 * 11 => "CALIBRA_PRODUCTS_LINE"
	 * 12 => "CALIBRA_AGE_CATEGORY"
	 * 13 => "CALIBRA_CATEGORY"
	 * 14 => "CALIBRA_MAIN_PROTEIN"
	 * 15 => "SPECIFICKE_URCENI_KRMIVA"
	 * 16 => "Varianta"
	 * 17 => "Výchozí varianta"
	 * 18 => "CALIBRA_BODY_SIZE"
	 *
	 * !!
	 * Dump - zobrazuju všechny errory, které brání k následnému importu
	 * !! importovat lze pouze csv, které nevyhazuje žádné chyby !!
	 *
	 */
	public function import()
	{
		echo '<h1>Calibra products import.</h1>';

		if (!isset($_GET['run']) || !in_array($_GET['run'], ['preview', 'execute'], true)) {
			echo "<p>Put the calibra.csv  file (delimiter = ';') into the temp dir. Then run this action with a parameter run=preview.</p>";
			exit;
		}

		$errors = [];

		try {
			$csv = Reader::createFromPath(TEMP_DIR . '/calibra.csv', 'r');
			$csv->setDelimiter(';');
			$csv->setHeaderOffset(0);
			$records = $csv->getRecords();
			$products = []; // group by Varianta
			foreach ($records as $row) {
				if (empty($row['KOD_ZBO']) && empty($row['KOD_CALIBRA'])) {
					$errors[] = sprintf('Needs to be included KOD_CALIBRA or KOD_ZBO in file.');
					continue;
				}

				$productVariant = $this->orm->productVariant->getBy([
					'novikoId' => $row['KOD_ZBO'],
					'product->mutation' => Mutation::ID_CS_DEFAULT,
				]);

				if (!isset($productVariant)) {
					$productVariant = $this->orm->productVariant->getBy([
						'calibraId' => $row['KOD_CALIBRA'],
						'product->mutation' => Mutation::ID_CS_DEFAULT,
					]);
				}

				if (!isset($productVariant)) {
					$errors[] = sprintf('Unknown ProductVariant KOD_ZBO %d %s', $row['KOD_ZBO'], $row['NAZEV']);
					continue;
				}

				if ($productVariant->product && $productVariant->product->id !== Product::NOVIKO_IMPORT_WRAP_ID) {
					$errors[] = sprintf('SKIPPED ProductVariant KOD_ZBO %d - already sorted Product ID %d', $row['KOD_ZBO'], $productVariant->product->id);
					continue;
				}

				if (empty($row['Varianta'])) {
					$errors[] = sprintf('Unknown binding KOD_ZBO %d Varianta %d', $row['KOD_ZBO'], $row['Varianta']);
					continue;
				}

				if (!isset($products[$row['Varianta']])) {
					$product = new \stdClass();
					$product->product = null;
					$product->variants = [];
					$products[$row['Varianta']] = $product;
				}

				if ($row['Výchozí varianta']) {
					$products[$row['Varianta']]->product = $row;
					$products[$row['Varianta']]->variants[999999] = $productVariant;// výchozí musí být vždy jako první -> sort
				} else {
					$products[$row['Varianta']]->variants[$productVariant->novikoId] = $productVariant;
				}

				krsort($products[$row['Varianta']]->variants);

				if (empty($products[$row['Varianta']]->product)) {
					$errors[] = sprintf('Unknown product - bad "Výchozí varianta value" %s for Variant ID %s', $row['Výchozí varianta'], $row['Varianta']);
					continue;
				}
			}

			if (!empty($errors)) {
				echo '<h2>Errors:</h2><ul>';
				foreach($errors as $error) {
					echo "<li>" . $error . '</li>';
				}

				echo '</ul>';
				echo '<strong>ERROR - IMPORT NOT ALLOWED!</strong>';
				exit;
			}

			if ($_GET['run'] !== 'execute') {
				if (!Debugger::$productionMode) {
					dump($products);
				} else {
					foreach($products as $id => $product) {
						echo "<br> ID: " . $id . ', Code: ' . $product->product['KOD_ZBO'] . ', Name:' . $product->product['NAZEV'] . ', variants: ' .
							implode(', ', array_map(fn($item) => $item->id, $product->variants));
					}
				}

				echo '<br><hr><strong>Is everything OK? If it is, run this action with a parameter run=execute to actually execute the import.</strong>';
				exit;
			}

			foreach ($products as $p) {
				$name = StringHelper::trimEol(trim($p->product['NAZEV']));
				$annotation = '<p>' .StringHelper::trimEol(trim($p->product['INFO_SHORT'])).'</p>';

				$product = new Product();
				$this->orm->product->attach($product);
				$product->mutation = Mutation::ID_CS_DEFAULT;
				$product->name = $name;
				$product->nameTitle = $name;
				$product->nameAnchor = $name;
				//$product->nameLang = trim($p->product['INFO_SHORT']);

				// až z XLS
				$product->annotation = $annotation;
				$product->annotationBox = $annotation;
				//$content = html_entity_decode(trim($p->product['INFO']));
				//$content = str_replace('\n', '', $content);
				//$product->content = Strings::startsWith($content, '<p>') ? $content : '<p>' . $content . '</p>';

				$product->public = 0;
				$product->isSale = 1;
				$product->isOld = 0;
				$product->isInPrepare = 0;
				$product->hideFirstImage = 0;

				// varianty
				$product->variants->set($p->variants);

				// images
				$sort = 0;
				foreach ($p->variants as $variant) {
					/** @var ProductVariant $variant */
					$image = $this->orm->libraryImage->getById($variant->tempImageId);
					if ($image && $image->id) {
						$productImage = new ProductImage();
						$productImage->image = $image->id;
						$productImage->url = $image->url;
						$productImage->name = $variant->nameDefault;
						$productImage->sort = $sort;
						$productImage->variants = $variant->id;
						$product->images->add($productImage);
						$sort++;
					}
				}

				$this->orm->product->persistAndFlush($product);

				$animal = trim($p->product['CALIBRA_ANIMAL']);

				// parametry - berou se z ->product = výchozí varianta
				foreach ($this->csvParameters as $uid => $col) {
					// parametry pouze pro dané zvíře
					if (in_array($uid, ['ageDog', 'feedTypeDog', 'dogSize']) && $animal != 'Dog') {
						continue;
					}
					if (in_array($uid, ['ageCat', 'feedTypeCat']) && $animal != 'Cat') {
						continue;
					}

					$pDetail = $this->parameters[$uid];

					/** @var Parameter $parameter */
					$parameter = $pDetail->parameter;

					if ($p->product[$col] == 'All') { // spec pro CALIBRA_BODY_SIZE
						$values = [
							'Small',
							'Medium',
							'Large',
						];
					} else {
						$values = explode('|', $p->product[$col]);
					}

					if (empty($values)) {
						echo '<br>Empty values - ' . $col . ' - ' . $p->product['KOD_ZBO'];
						continue;
					}

					$parameterValueIds = [];

					foreach ($values as $value) {
						$value = trim($value);

						if (empty($value)) {
							echo '<br>Empty values - ' . $col . ' - ' . $p->product['KOD_ZBO'];
							continue;
						}

						$parameterValueId = isset($pDetail->values[$value]) ? $pDetail->values[$value]->id : null;
						if ($parameterValueId) {
							$parameterValueIds[] = $parameterValueId;
						} else {
							$parameterValue = new ParameterValue();
							$parameterValue->value = $value;
							$parameterValue->alias = Strings::webalize($parameterValue->value);
							$parameterValue->parameter = $parameter;
							$parameterValue->parameterSort = $parameter->sort;
							$parameterValue = $this->orm->parameterValue->persistAndFlush($parameterValue);
							$this->parameters[$uid]->values[$parameterValue->value] = $parameterValue;
							$parameterValueIds[] = $parameterValue->id;
						}
					}

					switch ($parameter->type) {
						case Parameter::TYPE_MULTISELECT:
							$this->parameterValueModel->handleParameterValuesMultiSelectAttachment($product, $parameter, $parameterValueIds);
							break;
						case Parameter::TYPE_SELECT:
							$this->parameterValueModel->handleParameterValuesSelectAttachment($product, $parameter, $parameterValueIds);
							break;
						default:
					}

				}
				$product->alias = $name; // aliasy se musi resit po persistovani produktu

				// výchozí varianta
				$i = 0;
				foreach ($p->variants as $variant) {
					$variant->sort = $i;
					$variant->active = 1;
					$variant->calibraId = $p->product['KOD_CALIBRA'];
					$this->orm->productVariant->persistAndFlush($variant);
					$i++;
				}

				try {
					$this->productModel->addProduct($product);
				} catch (Throwable $e) {
					Debugger::log($e, 'exception');
					echo "<br><strong>Warning: elastic error: " . $e->getMessage() . '</strong>';
				}

				echo '<br> OK ' . $product->id;
			}
		} catch (Throwable $e) {
			Debugger::log($e, 'exception');
			echo "<br><strong>Exception: " . $e->getMessage() . '</strong>';

			exit;
		}

		echo '<br><strong>DONE</strong>';
		exit;
	}

	/**
	 * Import textů obálek produktů z CSV
	 *
	 * 0 => "Hmotnost"
	 * 1 => "ID"
	 * 2 => "Meta Title"
	 * 3 => "Meta Description"
	 * 4 => "H1"
	 * 5 => "Text"
	 *
	 * !!
	 * Dump - zobrazuju všechny errory, které brání k následnému importu
	 * !! importovat lze pouze csv, které nevyhazuje žádné chyby !!
	 *
	 */
	public function importText()
	{
		die(__METHOD__);
		$errors = [];

		try {
			$products = []; // group by Varianta

			for ($c = 1; $c <= 12; $c++) {
				$csv = Reader::createFromPath(TEMP_DIR . '/texty' . $c . '.csv', 'r');
				$csv->setDelimiter(';');
				$csv->setHeaderOffset(0);
				//$header = $csv->getHeader();dump($header);
				$records = $csv->getRecords();
				foreach ($records as $row) {
					if (empty($row['Meta Title'])) {
						dump('SKIPPED empty row ' . $row['ID']);
						continue;
					}

					$productVariant = $this->orm->productVariant->getBy([
						'novikoId' => $row['ID'],
						'product->mutation' => Mutation::ID_CS_DEFAULT,
					]);

					if (!$productVariant) {
						$errors[] = sprintf('Unknown ProductVariant Noviko ID %d | Meta Title %s', $row['ID'], $row['Meta Title']);
						continue;
					}

					if (!$productVariant->product || $productVariant->product->id == Product::NOVIKO_IMPORT_WRAP_ID) {
						$errors[] = sprintf('Unknow Product - unsorted VAR ID %d , Noviko %d', $productVariant->id, $row['ID']);
						continue;
					}

					$product = new \stdClass();
					$product->product = $productVariant->product;
					$product->data = $row;
					$products[$row['ID']] = $product;
				}
				dump($c);
			}


			if (!empty($errors)) {
				dump($errors);
				dumpe('ERROR - IMPORT NOT ALLOWED');
			}
			$product = null;
			dumpe($products);

			foreach ($products as $p) {
				$name = trim($p->data['Meta Title']);

				/** @var Product $product */
				$product = $p->product;
				$product->nameTitle = trim($p->data['Meta Title']);
				$product->description = trim($p->data['Meta Description']);
				$product->nameLang = trim($p->data['H1']);
				$content = '<p>' .trim($p->data['Text']). '</p>';
				//$content = html_entity_decode(trim($p->product['INFO']));
				//$content = str_replace('\n', '<br>', $content);
				$product->annotation =  $content ;
				$product->annotationBox =  $content ;
				$product->content =  $content ;

				$this->orm->product->persistAndFlush($product);
				$this->productModel->updateProduct($product);

				dump(['OK' => $product->id, 'Noviko ID' => $p->data['ID']]);

			}
		} catch (Throwable $e) {
			var_dump($e->getMessage());
		}

		dumpe('DONE');
	}

	public function importCustom()
	{
		die(__METHOD__);

		$errors = [];
		$this->orm->product->setPublicOnly(false);

		try {
			$csv = Reader::createFromPath(TEMP_DIR . '/varprice.csv', 'r');
			$csv->setDelimiter(';');
			$csv->setHeaderOffset(0);
			//$header = $csv->getHeader();dump($header);
			$records = $csv->getRecords();

			foreach ($records as $row) {
				dump($row);
				$productVariant = $this->orm->productVariant->getBy([
					'novikoId' => $row['novikoId'],
					'product->mutation' => Mutation::ID_SK_ESHOP,
				]);

				if (!$productVariant) {
					$errors[] = sprintf('Unknown ProductVariant NovikoID %d %s', $row['novikoId'], $row['nameDefault']);
					continue;
				}

				$productVariant->priceDPH = $row['priceDPH'];
				$this->orm->productVariant->persistAndFlush($productVariant);
			}
			dump($errors); // @todo jk

		} catch (Throwable $e) {
			var_dump($e->getMessage());
		}

		dumpe('DONE');
	}
}
