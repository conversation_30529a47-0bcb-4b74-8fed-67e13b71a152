<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;


use App\Model\Mutation;
use App\Model\User;
use App\Model\UserGroup;

final class SortCreator
{

	public function create(string $order, Mutation $mutation, ?User $user = null): Sort
	{
		$sort = new Sort();
		if ($order === 'alphabetic') {
			$sort->addByAlphabetic();
		} elseif ($order === 'alphabeticBottom') {
			$sort->addByAlphabeticBottom();
		} elseif ($order === 'score') {
			//$sort->addByScore();
		} elseif ($order === 'bestseller') {
			$sort->addByBestseller();
		} elseif ($order === 'cheapest') {
			$sort->addByPrice($user ? $user->groups->toCollection()->fetch() : $mutation->userGroups->toCollection()->getBy(['type' => UserGroup::TYPE_DEFAULT]),'asc');
		} elseif ($order === 'expensive') {
			$sort->addByPrice($user ? $user->groups->toCollection()->fetch() : $mutation->userGroups->toCollection()->getBy(['type' => UserGroup::TYPE_DEFAULT]),'desc');
		} elseif ($order === 'review') {
			//$sort->addByReview();
		} elseif ($order === 'sort') {
			$sort->addByBestseller();
			$sort->addBySort();
		}

		return $sort;
	}

}
