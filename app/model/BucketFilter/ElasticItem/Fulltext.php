<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

class Fulltext implements ElasticItem, QuestionableElasticItem
{

	public function __construct(
		private readonly ?string $fulltext = null,
	)
	{
	}

	public function getCondition(): AbstractQuery
	{
		$fields = [];
		$fields[] = 'name.dictionary^80';
		$fields[] = 'annotation.dictionary^40';
		$fields[] = 'content.dictionary^40';

		$b = new QueryBuilder();
//
//		$query = $b->query()->bool();
//
//		$phraseQuery = $b->query()->multi_match()
//			->setType('phrase')
//			->setQuery($this->fulltext)
//			->setFields($fields);
//
//		$bestFieldsQuery = $b->query()->multi_match()
//			->setType('best_fields')
//			->setQuery($this->fulltext)
//			->setFuzziness(2)
//			->setOperator('AND')
//			->setFields($fields);
//
//		$query->addShould($phraseQuery);
//		$query->addShould($bestFieldsQuery);
//
//		return $query;


		return $b->query()->multi_match()
			->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
			->setQuery($this->fulltext)
			->setFuzziness(3)
			->setOperator('OR')
			->setFields($fields);
	}

	public function getElasticKey(): string
	{
		return '';
	}

}
