<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\UserGroup;

final class Sort
{

	private array $sentences = [];

	public function addByPrice(UserGroup $userGroup, string $dir = 'asc'): void
	{
		$this->sentences["prices.{$userGroup->type}"] = $dir;
	}

	public function addByName(string $dir = 'asc'): void
	{
		$this->sentences['nameSort'] = $dir;
	}

	public function getSentences(): array
	{
		return $this->sentences;
	}

	public function addByStore(): void
	{
		$this->sentences['isInStore'] = 'desc';
	}

	public function addByTopScore(State $state, PriceLevel $priceLevel): void
	{
		$this->sentences["topScore.{$state->code}.{$priceLevel->type}"] = 'desc';
	}

	public function addByBestseller(): void
	{
		$this->sentences['isSample'] = 'asc';
		$this->sentences['forceBestSellerOrder'] = 'desc';
		$this->sentences['isInStore'] = 'desc';

	}

	public function addByScore(): void
	{
		$this->sentences['_score'] = 'desc';
	}

	public function addByAlphabetic(): void
	{
		$this->sentences['nameSort'] = 'asc';
	}

	public function addByAlphabeticBottom(): void
	{
		$this->sentences['nameSort'] = 'desc';
	}

	public function addByReview(): void
	{
		$this->sentences['reviewAverage'] = 'desc';
	}

	public function addBySort(): void
	{
		$this->sentences['sort'] = 'asc';
	}

}
