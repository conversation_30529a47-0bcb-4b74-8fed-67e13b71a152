<?php


namespace SuperKoderi\Model\Router;


use App\Model\Product;

class FilterVariantId
{
	public function __construct()
	{
	}

	public function in($params)
	{
		if (isset($params['object'])) {
			$object = $params['object'];
			if ($object instanceof Product) {
				$defaultVariant = $object->firstVariant;

				if (!$defaultVariant) {
					$params['presenter'] = false;
				} else {

					if (!isset($params['v'])) {
						$params['v'] = $defaultVariant->id;
					} else if (isset($params['v'])) {
						$selectedVariant = $object->activeVariants->getById($params['v']);
						if (!$selectedVariant) {
							// variant doesnt exist -> redirect to primary variant
							$params['v'] = $defaultVariant->id;
						}
					}
				}
			}
		}
		return $params;
	}

	public function out($params)
	{
		if (isset($params['object'])) {
			$object = $params['object'];
			if ($object instanceof Product) {
				// pokud se jedna o hlavni variantu tak v URL parametru tato varianta nebude
				if (isset($params['v']) && $params['v'] == $object->firstVariant->id) {
					unset($params['v']);
				}
			}
		}
		return $params;
	}
}
