<?php

namespace SuperKoderi;

use Nette\Database\Connection;
use Nette;
use Tracy\Debugger;

class DbalLog {

	use Nette\SmartObject;

    private $absPath;

    private $file;

	/** @var ConfigService */
	private $configService;

	/** @var \Nextras\Dbal\Connection */
	private $db;

	public function __construct($absPath, $file, \Nextras\Dbal\Connection $db, ConfigService $configService) {
        $this->absPath = $absPath;
        $this->file = $file;
		$this->configService = $configService;
		$this->db = $db;
	}

	public function register()
	{
		$this->purgeLog();
		if ($this->configService->get('dbalLog')) {
			$this->db->onQuery[] = array($this, 'logQuery');
		}
	}


    public function logQuery(\Nextras\Dbal\Connection $db, $sql) {
        Debugger::log($sql, $this->file);
    }

    private function purgeLog() {
        $file = $this->absPath . $this->file . ".log";
        if (file_exists($file)) {
            unlink($file);
        }
    }

}
