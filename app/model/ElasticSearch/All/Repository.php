<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\All\Convertor\ProductData;
use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\EsIndex\EsIndex;
use Elastica\Query;
use Elastica\QueryBuilder;
use Elastica\ResultSet;

class Repository extends \App\Model\ElasticSearch\Repository
{

	public function __construct(
		IndexModel $indexModel
	)
	{
		parent::__construct($indexModel);
	}


	// BE search - products
	public function elasticSearchProductsAdmin(EsIndex $esIndex, array $params, int $limit, int $offset, ?string $orderColumn = null, \SuperKoderi\User\Security\User $user = null, float $minScore = 0.01): array
	{
		//$params = (array) $params;
		$query = new Query();
        $query->setSize($limit)
            ->setFrom($offset)
            ->setMinScore($minScore);

		$b = new \Elastica\QueryBuilder();

		$must = [];
		$must[] = $b->query()->term(['kind' => ProductData::KIND_PRODUCT]);

		if (isset($params['isOld']) && $params['isOld']) {
			$must[] = $b->query()->term(['isOld' => $params['isOld']]);
		}

		if (isset($params['isNew']) && $params['isNew']) {
			$must[] = $b->query()->term(['isNew' => $params['isNew']]);
		}

		if (isset($params['mutationIds']) && $params['mutationIds']) {
			$must[] = $b->query()->terms('mutationId', $params['mutationIds']);
		}

		if (isset($params['hasDiscount'])) {
			$must[] = $b->query()->term(['filter.hasDiscount' => true]);
		}

        if (isset($params['fulltext'])) {
            $fields = [];
            $fields[] = 'name^40';
            $fields[] = 'nameTitle^30';
            $fields[] = 'annotation^20';
            $fields[] = 'description^20';


            $must[] = $b->query()->bool()->addShould(
                $b->query()->multi_match()
                    ->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
                    ->setQuery($params['fulltext'])
                    ->setFuzziness(3)
                    ->setOperator('OR')
                    ->setFields($fields)
            );
        }

		$bool = $queryBool = $b->query()
			->bool();
		foreach ($must as $item) {
			$bool->addMust($item);
		}

        $query->setQuery(
            $queryBool
        );
		$query->addAggregation(
			$b->aggregation()
				->terms('id')
				->setField('id')
				->setSize(1000)
		);

		$result = $this->baseSearch($esIndex, $query);

		$productIdAgg = $result->getAggregation('id');

		$productids = [];
		foreach ($productIdAgg['buckets'] as $bucket) {
			$productids[] = $bucket['key'];
		}
		return $productids;
	}


	public function elasticSearchAdminSuggest(EsIndex $esIndex, string $q, int $size = 10, int $from = 0): ResultSet
	{
		$query = new Query();
		$query->setSize($size)
			->setFrom($from)
			->setExplain(true)
			->setVersion(true);

		$fields = [];
		$fields[] = 'name^40';
		$fields[] = 'nameSort^30';

		$b = new QueryBuilder();

		$must = [];
		$must[] = $b->query()->bool()->addShould(
			$b->query()->multi_match()
				->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
				->setQuery($q)
				->setFuzziness(3)
				->setOperator('OR')
				->setFields($fields)
		);

		$queryBool = $b->query()
			->bool();

		foreach ($must as $item) {
			$queryBool->addMust($item);
		}

		$query->setQuery(
			$queryBool
		);

		return $this->baseSearch($esIndex, $query);
	}

}
