<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Product;
use App\Model\UserGroup;
use Nette\Utils\Strings;

class BaseData implements Convertor
{

	public function __construct()
	{
	}

	public function convert(Product $product): array
	{
//		$mutation = $product->getMutation();

		$data = [
			'nameSort' => $product->name,
			'name' => $product->name,
			'nameTitle' => $product->nameTitle,
			'nameAnchor' => $product->nameAnchor,
			'content' => $product->content !== null ? strip_tags($product->content) : '',
			'annotation' => $product->annotation,
			'isPublic' => (bool) $product->public,
			'isOld' => (bool) $product->isOld,
			'isNew' => (bool) $product->isNew,
			'publicFrom' => ConvertorHelper::convertTime($product->publicFrom),
			'publicTo' => ConvertorHelper::convertTime($product->publicTo),
			'soldCount' => $product->soldCount,
			'basketTipsOrder' => $product->basketTipsOrder,
			'reviewAverage' => $product->reviewAverage,
		];

		$isSubscription = false;
		$sort = 0;
		foreach ($product->activeVariants as $key => $activeVariant) {
			if ($key == 0) {

				/** @var UserGroup[] $priceGroups */
				$priceGroups = $product->mutation->userGroups;
				$prices = [];
				foreach ($priceGroups as $priceGroup) {
					$prices[$priceGroup->type] = $activeVariant->getPriceFinalDPHForGroup($priceGroup->id);
				}
				$data['prices'] = $prices;
			} else {
				$data['prices'][] = [];
			}

			$sort = $activeVariant->sort;

			if ($activeVariant->isSubscription === 1 && $isSubscription === false) {
				$isSubscription = true;
			}
		}
		$data['sort'] = $sort;
		$data['isSubscription'] = $isSubscription;

//		$data = [
//			'path' =>
//			'categories' => $product->attachCategories->findBy(['rootId' => $mutation->rootId])->fetchPairs(null, 'id'),
//		];

//		$variants = $product->variants->toCollection()->findBy([
//			'variantLocalizations->active' => 1,
//			'variantLocalizations->mutation' => $mutation,
//		]);

		$data['eans'] = [];
		$data['codes'] = [];

//		foreach ($variants as $variant) {
//			if ($variant->ean) {
//				$data['eans'][] = Strings::lower((string) $variant->ean);
//			}
//
//			if ($variant->code) {
//				$data['codes'][] = Strings::lower((string) $variant->code);
//
//			}
//		}


		return $data;
	}

}
