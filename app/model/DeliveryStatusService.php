<?php declare(strict_types=1);

namespace SuperKoderi;

use App\Model\Order;
use App\Model\OrderModel;
use App\Model\Orm;
use SuperKoderi\Model\PPL\PplApiClient;
use SuperKoderi\Model\PPL\PacketNotFoundException;

class DeliveryStatusService
{

	public function __construct(
		protected readonly PplApiClient $pplApiClient,
		protected readonly Orm $orm,
		protected readonly OrderModel $orderModel
	)
	{
	}

	/**
	 * Pro danou objednavku vrati URL pro sledovani baliku
	 */
	public function getOrderStatusLink(Order $order, string $idBalikExt): string
	{
		$trackingLink = null;
		$transports = $order->mutation->transportsArray;

		// pokud je zahranicni objednavka z ppl tak hledam jeho cislo v api
		if ($order->mutation->langCode !== 'cs' && $order->transport->subType === 'ppl') {
			// nactu link z entity
			if ($order->deliveryInfoUrl) {
				$orderLink = json_decode($order->deliveryInfoUrl, true);
				if (isset($orderLink[$idBalikExt])) {
					$trackingLink = $orderLink[$idBalikExt];
				}
			}

			// pokud jeste nemam ulozeny tracking link, natahnu ho z API
			if (! $trackingLink) {
				$trackingLink = $this->setPplDeliveryInfoUrlForOrder($order, $idBalikExt);
			}
		}

		// tracking link jsem nenasel vratim zakladni URL dorucovatele (stejne tak pro cz verzi)
		if ( ! $trackingLink) {
			$trackingLinkBase = $transports[$order->transport->subType]['trackingLinkBase'];
			$trackingLink = "{$trackingLinkBase}{$idBalikExt}";
		}

		if ( ! $trackingLink) {
			throw new \InvalidArgumentException("No tracking links found for order {$order->id}");
		}

		return $trackingLink;
	}

	/**
	 * Nastavi trackovaci link pro objednavku
	 *
	 * @param Order $order
	 * @param string $idBalikExt
	 * @return string|null
	 */
	protected function setPplDeliveryInfoUrlForOrder(Order $order, string $idBalikExt):? string
	{
		// nactu tracking link z PPL API
		$trackingLink = $this->pplApiClient->getPartnerLink($order->mutation->langCode, $idBalikExt);

		if ( ! $trackingLink) return null;

		$currentLinks = json_decode($order->deliveryInfoUrl ?? "{}", true);
		$currentLinks[$idBalikExt] = $trackingLink;

		$order->deliveryInfoUrl = \json_encode($currentLinks);
		$this->orm->persistAndFlush($order);

		return $trackingLink;
	}

	/**
	 * vraci informaci zda byla objednavka kompletne dorucena
	 */
	public function isOrderDelivered(Order $order): bool
	{
		$packets = [];

		foreach ($order->parcelNumber['baliky'] ?? [] as $parcelNumber) {
			$packets[] = $parcelNumber['idBalikExt'];
		}

		$packetCount = count($packets);

		// u objednavky nejsou zadane zadne baliky, nejspis jeste nebyla odeslana
		if ($packetCount === 0) {
			return false;
		}

		$results = $this->pplApiClient->getPackages($order->mutation->langCode, $packets);

		$deliveredPacket = [];
		foreach ($results as $result) {
			foreach ($result->PackageStatuses->MyApiPackageOutStatus ?? [] as $status) {
				// zjistuji zda bylo doruceno
				if ($status->StaID ?? null === PplApiClient::DELIVERED_CODE) {
					$deliveredPacket[] = $result->PackNumber;
				}
			}
		}

		// zjistim zda byli doruceny vsechny baliky
		if (count($deliveredPacket) === $packetCount) {
			// menim stav objednavky na done
			$this->orderModel->markAsDone($order);
			return true;
		}

		return false;
	}

	/**
	 * Validate if PPL place/parcel shop exists
	 *
	 * @param string $pplParcelCode
	 * @throws PacketNotFoundException
	 */
	public function validatePPLPlace(string $pplParcelCode): void
	{
		// Remove "KM" prefix if present (as mentioned in Subscription entity comment)
		$cleanCode = $pplParcelCode;

		// Try different language codes - start with Czech as it's most common
		$langCodes = ['cs', 'sk', 'pl'];
		$apiKeys = $this->pplApiClient->getApiKeys();
		$errors = [];

		foreach ($langCodes as $langCode) {
			if (isset($apiKeys[$langCode])) {
				try {
					if ($this->pplApiClient->parcelShopExists($langCode, $cleanCode)) {
						return;
					}
				} catch (\Exception $e) {
					$errors[] = "Lang {$langCode}: " . $e->getMessage();
					continue;
				}
			}
		}

		$errorDetails = !empty($errors) ? ' (Errors: ' . implode('; ', $errors) . ')' : '';
		throw new PacketNotFoundException("PPL place with code '{$pplParcelCode}' (cleaned: '{$cleanCode}') does not exist{$errorDetails}");
	}

}
