<?php

namespace SuperKoderi;

use App\Model\Mutation;
use InvalidArgumentException;
use Nette\Application\UI\Form;
use Nette\Utils\Validators;

class StringHelper
{
	/**
	 * Remove empty p tag at the end of content (content is generated in tinyMce)
	 * @param string $s
	 * @return string
	 */
	public static function removeTinyMceEmptyP($s)
	{
		return preg_replace('/(<p>\xC2\xA0<\/p>(\r?\n))*<p>\xC2\xA0<\/p>$/', '', $s);
	}

	/**
	 * Returns image ext by type
	 *
	 * @param int IMAGETYPE_[int]
	 * @return string
	 * @throws InvalidArgumentException
	 */
	public static function getImageExtFromMimeType($type)
	{
		$type = (int)$type;

		switch ($type) {
			case IMAGETYPE_JPEG:
				return '.jpg';
				break;
			case IMAGETYPE_PNG:
				return '.png';
				break;
			case IMAGETYPE_GIF:
				return '.gif';
				break;

			default:
				throw new InvalidArgumentException('Unsupported image type.');
				break;
		}
	}

	/**
	 * @param int $grams
	 * @param int $precision
	 * @return string
	 */
	public static function formatUnits(int $grams, int $precision = 1)
	{
		$units = array('g', 'kg', 't');
		$grams = max($grams, 0);
		$pow = floor(($grams ? log($grams) : 0) / log(1000));
		$pow = min($pow, count($units) - 1);

		// Uncomment one of the following alternatives
		$grams /= pow(1000, $pow);
		// $bytes /= (1 << (10 * $pow));

		return round($grams, $precision) . ' ' . $units[$pow];
	}

	/**
	 * Pro textový setters default null
	 * @param mixed $value
	 * @return string|null
	 */
	public static function stringTonull($value)
	{
		$value = trim($value);
		return empty($value) ? null : (string)$value;
	}

	/**
	 * Pro number setters default null
	 * @param mixed $value
	 * @return int|null
	 */
	public static function intTonull($value)
	{
		return empty($value) ? null : (int)$value;
	}

	/**
	 * Pro float setters default null
	 * @param mixed $value
	 * @return float|null
	 */
	public static function floatTonull($value)
	{
		return empty($value) ? null : (float)$value;
	}

	/**
	 * Pro array setters default null
	 * @param mixed $value
	 * @return array|null
	 */
	public static function arrayToNull($value)
	{
		return empty($value) ? null : (array)$value;
	}

	/**
	 * Remove EOL
	 *
	 * @param string $text
	 * @return string
	 */
	public static function trimEol($text)
	{
		return strtr($text, ["\r" => '', "\n" => '', "  " => ' ']);
	}

	/**
	 * Prevede (prevypravi) nepovolene znaky ve windows-1250 do UTF-8
	 * http://phpfashion.com/prevody-mezi-kodovanim
	 *
	 * @param string
	 * @return string
	 */
	public static function fixEncodingTranslit($s)
	{
		$s = @iconv('UTF-8', 'WINDOWS-1250//TRANSLIT', $s);
		return @iconv('WINDOWS-1250', 'UTF-8', $s);
	}

	public static function formValidatorZip(Form $form, $valuesAll, $key = 'zip', int|null $mutationId = null)
	{
		if (!empty($valuesAll[$key])) {
			$valuesAll[$key] = str_replace(' ', '', $valuesAll[$key]);
			if (!Validators::isNumeric($valuesAll[$key]) && in_array($mutationId, [Mutation::ID_SK_ESHOP, Mutation::ID_CS_DEFAULT], true)) {
				$form[$key]->addError('form_validation_zip_number');
			}
		}
	}

	public static function containsCyrillic(string $text): bool
	{
		if ($text === '') {
			return false;
		}
		return preg_match('/[\p{Cyrillic}]/u', $text) === 1;
	}
}
