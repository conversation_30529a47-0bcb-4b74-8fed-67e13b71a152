<?php declare(strict_types = 1);

namespace SuperKoderi\Email;

use App\Model\Alias;
use App\Model\EmailTemplateModel;
use App\Model\Orm;
use App\Model\Subscription;
use App\Model\SubscriptionOrder;
use Nette\Application\LinkGenerator;
use Nextras\Dbal\Utils\DateTimeImmutable;
use SuperKoderi\ConfigService;
use SuperKoderi\ImageResizer;
use SuperKoderi\IPagesFactory;
use SuperKoderi\LogicException;
use SuperKoderi\Mailer;
use SuperKoderi\MutationHolder;
use SuperKoderi\SkLinkGenerator;
use SuperKoderi\Template\Creator;
use SuperKoderi\Templating\Helpers;
use SuperKoderi\TranslatorDB;
use Symfony\Component\CssSelector\Exception\ParseException;
use function str_replace;
use function strpos;

class SubscriptionMail extends Base
{

	public function __construct(
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly SkLinkGenerator $skLinkGenerator,
		private readonly LinkGenerator $linkGenerator,
		MutationHolder $mutationHolder,
		IPagesFactory $pagesFactory,
		ConfigService $configService,
		EmailTemplateModel $emailTemplateModel,
		ImageResizer $imageResizer,
		Mailer\Base $baseMailer,
		Creator $creator,
	)
	{
		parent::__construct(
			$mutationHolder,
			$pagesFactory,
			$configService,
			$emailTemplateModel,
			$imageResizer,
			$baseMailer,
			$creator,
		);
	}

	/**
	 * @return $this
	 * @throws LogicException
	 * @throws ParseException
	 */
	public function send(array|string $from, array|string $to, array|string $replyTo, string $subject, string $dbTemplate, Subscription $subscription, string|null $latteTemplate = null, bool $checkMailConfirmationSend = true, array $attachments = []): self
	{
		if (($checkMailConfirmationSend && !(bool) $subscription->createdSubscriptionNotificationSentAt) || !$checkMailConfirmationSend) {
			$html = $this->render($dbTemplate, $latteTemplate, [], $subscription->user->mutation);

			//2. replace [DATA-*]
			$html = $this->replaceSubscriptionInfo($html, $subscription, $dbTemplate);

			$subject = $this->generateSubject($dbTemplate, $subject, $subscription->user->mutation);
			$subject = $this->replaceSubscriptionInfo($subject, $subscription, $dbTemplate);

			$data = $this->getDataAttachment($dbTemplate, $attachments);

			//3. send email
			$this->baseMailer->send($from, $to, $subject, $html, $data, $replyTo);
		}

		return $this;
	}

	public function markCreatedSent(Subscription $subscription): void
	{
		if ($subscription->createdSubscriptionNotificationSentAt === null) {
			$subscription->createdSubscriptionNotificationSentAt = new DateTimeImmutable();
			$this->orm->persistAndFlush($subscription);
		}
	}

	public function markNotificationSent(SubscriptionOrder $subscriptionOrder): void
	{
		if ($subscriptionOrder->notificationSentAt === null) {
			$subscriptionOrder->notificationSentAt = new DateTimeImmutable();
			$this->orm->persistAndFlush($subscriptionOrder);
		}
	}

	public function markSentSkippedOrder(SubscriptionOrder $subscriptionOrder): void
	{
		if ($subscriptionOrder->sentSkippedOrderAt === null) {
			$subscriptionOrder->sentSkippedOrderAt = new DateTimeImmutable();
			$this->orm->persistAndFlush($subscriptionOrder);
		}
	}

	public function markMissingRecurringPaymentNotificationSent(SubscriptionOrder $subscriptionOrder): void
	{
		$subscriptionOrder->lastMissingRecurringPaymentSentAt = new DateTimeImmutable();
		$this->orm->persistAndFlush($subscriptionOrder);
	}

	public function markReservationNotification(SubscriptionOrder $subscriptionOrder): void
	{
		$subscriptionOrder->createOrderReservationAt = new DateTimeImmutable();
		$this->orm->persistAndFlush($subscriptionOrder);
	}

	private function replaceSubscriptionInfo(string $html, Subscription $subscription, string $dbTemplate): string
	{
		Helpers::$mutation = $subscription->mutation;
		$this->orm->setMutation($subscription->mutation);
		$this->mutationHolder->setMutation($subscription->mutation);

		$this->translator->reInit($subscription->mutation->langCode);

		$html = str_replace('[SUBSCRIPTION_NAME]', $subscription->name, $html);
		$html = str_replace('[SUBSCRIPTION_ID]', (string)$subscription->id, $html);
		$html = str_replace('[SUBSCRIPTION_NEXT_ORDER_DATE]', $subscription->nextOrderCreationDate ? $subscription->nextOrderCreationDate->format('d.m.Y H:i:s') : '', $html);

		if (strpos($html, '[SUBSCRIPTION_ADMIN_LINK]') !== false) {
			$link = $this->linkGenerator->link('Admin:Subscription:edit', ['id' => $subscription->id]);
			$html = str_replace('[SUBSCRIPTION_ADMIN_LINK]', $link, $html);
		}

		if (strpos($html, '[SUBSCRIPTION_EDIT_LINK]') !== false) {
			$tree = $this->orm->tree->getByUid('subscriptionDetail');
			$alias = $this->orm->tree->getAliasByTree($tree);
			$link = '';
			if ($alias instanceof Alias) {
				$link = $this->skLinkGenerator->linkFromAlias($alias, $subscription->mutation->langCode, ['hash' => $subscription->hash]);
			}

			$html = str_replace('[SUBSCRIPTION_EDIT_LINK]', $link, $html);
		}

		if (strpos($html, '[SUBSCRIPTION_NEXT_ORDER_EDIT_LINK]') !== false) {
			$tree = $this->orm->tree->getByUid('subscriptionDetail');
			$alias = $this->orm->tree->getAliasByTree($tree);
			$link = '';
			if ($alias instanceof Alias) {
				$link = $this->skLinkGenerator->linkFromAlias($alias, $subscription->mutation->langCode, ['hash' => $subscription->hash]);
			}

			$html = str_replace('[SUBSCRIPTION_NEXT_ORDER_EDIT_LINK]', $link, $html);
		}


		if (strpos($html, '[SUBSCRIPTION_ORDER]') !== false) {
			$params = [];
			$params['tr'] = $this->translator;

			$params['imageResizer'] = $this->imageResizer;
			$params['domainUrl'] = $this->mutation->getDomain();
			$params['lg'] = $this->mutation->langCode;
			$params['lang'] = $this->mutation->langCode;
			$params['langCode'] = $this->mutation->langCode;
			$params['data'] = $subscription->user;
			$params['subscriptionOrder'] = $dbTemplate === 'subscriptionReservationStock' ? $subscription->reservedSubscriptionOrder : $subscription->openSubscriptionOrder;
			$params['subscription'] = $subscription;

			$recapitulationHtml = $this->creator->createTemplate(FE_TEMPLATE_DIR . '/email/part/subscriptionOrder.latte', $params)->__toString();
			$html = str_replace('[SUBSCRIPTION_ORDER]', $recapitulationHtml, $html);
		}

		if (strpos($html, '[SUBSCRIPTION_RECAP]') !== false) {
			$params = [];
			$params['tr'] = $this->translator;

			$params['imageResizer'] = $this->imageResizer;
			$params['domainUrl'] = $this->mutation->getDomain();
			$params['lg'] = $this->mutation->langCode;
			$params['lang'] = $this->mutation->langCode;
			$params['langCode'] = $this->mutation->langCode;
			$params['data'] = $subscription->user;
			$params['subscription'] = $subscription;

			$recapitulationHtml = $this->creator->createTemplate(FE_TEMPLATE_DIR . '/email/part/subscription.latte', $params)->__toString();
			$html = str_replace('[SUBSCRIPTION_RECAP]', $recapitulationHtml, $html);
		}

		if (strpos($html, '[ONLINEPAYMENTLINK]') !== false) {
			$tree = $this->orm->tree->getByUid('retryOnlinePayment');
			$alias = $this->orm->tree->getAliasByTree($tree);
			$link = '';
			if ($alias instanceof Alias) {
				$link = $this->skLinkGenerator->linkFromAlias($alias, $subscription->mutation->langCode, ['orderNumber' => $subscription->lastProcessedOrder->number, 'hash' => $subscription->lastProcessedOrder->hash]);
			}

			$html = str_replace('[ONLINEPAYMENTLINK]', $link, $html);
		}

		return $html;
	}

}


interface ISubscriptionMailFactory
{

	function create(): SubscriptionMail;

}
