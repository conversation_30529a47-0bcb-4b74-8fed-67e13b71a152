<?php


namespace App\Model;

use Nette\Http\Url;

class PlaceModel
{
	/**
	 * @var Orm
	 */
	private $orm;

	public function __construct(Orm $orm)
	{
		$this->orm = $orm;
	}


	public function save(Place $place = null, $data, $user = NULL)
	{
		if (!$place) {
			$place = new Place();
			$this->orm->place->attach($place);

			if ($user) {
				$place->created = $user ? $user->id : NULL;
			}
			$place->createdTime = new \DateTime();
		}

		$noSave = ['id'];
		$intValues = ['public'];
		$boolValues = [];

		foreach ($place->getMetadata()->getProperties() as $i) {
			$col = (string)$i->name;
			if (in_array($col, $noSave)) {
				continue;
			}
			if (isset($data[$col])) {
				if (in_array($col, $intValues)) {
					$data[$col] = (int)$data[$col];
				}
				$place->$col = $data[$col];
			} else {
				if (in_array($col, $boolValues)) {
					$place->$col = 0;
				}
			}
		}

		$this->orm->persistAndFlush($place);

		return $place;
	}


	public function delete(Place $place)
	{
		$this->orm->place->removeAndFlush($place);
	}

}
