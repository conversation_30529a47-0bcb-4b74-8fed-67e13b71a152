<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\Orm\TraitsEntity\HasCustomFields;
use ArrayIterator;
use Dibi\DateTime;
use Exception;
use Nette\Utils\Floats;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use stdClass;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\hasTranslatorTrait;
use SuperKoderi\MoneyHelper;
use SuperKoderi\Noviko\PriceLogs\ProductVariantPriceLogger;
use SuperKoderi\TranslatorDB;
use function abs;
use function assert;
use function explode;
use function implode;
use function intval;
use function is_int;
use function round;

/**
 * @property int $id {primary}
 * @property float $priceDPH {default 0.0}
 * @property int $active {default 0}
 * @property int $isOpenBox {default 0}
 * @property int $isNewBox {default 0}
 * @property string|null $ean
 * @property string $code {default ''}
 * @property DateTimeImmutable $created {default 'now'}
 * @property int|null $createdBy
 * @property DateTimeImmutable $edited {default 'now'}
 * @property int|null $editedBy
 * @property int $sort {default 0}
 * @property int $soldCount {default 0}
 * @property DateTimeImmutable|null $mailChimped
 *
 * @property int $isQuantityDiscount {default 0}
 * @property int $qdAmount {default 0}
 * @property int $useQuantityUserGroupDiscount {default 0}
 *
 * @property float|null $priceDPHRange
 * @property int|null $isInStore
 * @property int|null $isNew
 * @property int|null $isInDiscount
 * @property int|null $isSubscription
 * @property int|null $isOversize
 *
 * @property bool|null $isTastingSet
 * @property bool|null $isSample
 * @property bool|null $isDiet
 * @property int|null $animalType
 * @property int|null $dogSize
 * @property int|null $ageDog
 * @property int|null $ageCat
 * @property int|null $productType
 * @property int|null $feedTypeDog
 * @property int|null $feedTypeCat
 *
 * @property bool|null $isFreeTransport
 * @property string|null $flagsJson
 * @property stdClass $customFieldsJson {container CfJsonContainer}
 *
 * NOVIKO
 * @property int|null $novikoId
 * @property int|null $calibraId
 * @property string|null $nameDefault
 * @property DateTimeImmutable|null $novikoEdited
 * @property int $stock {default 0} // aktuální minimální hodnota zásoby na skladě
 * @property int|null $weight // váha v g
 * @property string|null $nameWeight // váha v text > 1000 1,5 kg
 * @property float $retailPrice {default 0.0} // doporučená MOC cena bez DPH
 * @property float $retailPriceVat {default 0.0}
 * @property float $vat {default 0.0} // hodnota DPH
 * @property string|null $codeSuplier // dodavatelský kod
 * @property int|null $tempImageId // ID img pro pozdější připojení k obálce - import varianty bez wrap produktu
 *
 *
 * RELATIONS
 * @property Product $product {m:1 Product::$variants}
 * @property ParameterValue|null $param1Value {m:1 ParameterValue::$variants1}
 * @property ParameterValue|null $param2Value {m:1 ParameterValue::$variants2}
 * @property OneHasMany|Supply[] $supplies {1:m Supply::$variant, cascade=[persist, remove]}
 * @property OneHasMany|ProductDiscount[] $discounts {1:m ProductDiscount::$variant, cascade=[persist, remove]}
 * @property OneHasMany|ProductVariantPrice[] $prices {1:m ProductVariantPrice::$productVariant, cascade=[persist, remove]}
 * @property ProductVariant|null $compensation  {m:1 ProductVariant, oneSided=true}
 * @property ProductVariantPresent[]|OneHasMany $presentsAll {1:m ProductVariantPresent::$productVariant, cascade=[persist, remove], orderBy=sort}
 * @property ProductVariantPresent[]|OneHasMany $presentParents {1:m ProductVariantPresent::$productVariantGift, cascade=[persist, remove]}
 * @property OneHasMany|ProductVariantPriceLog[] $priceLogs {1:m ProductVariantPriceLog::$productVariant, cascade=[persist, remove]}
 * @property SubscriptionItem[] $subscriptionItems {1:m SubscriptionItem::$productVariant}
 * @property Voucher[]|OneHasMany $gifts {1:m Voucher::$gift}
 *
 * VIRTUAL
 * -> WRITABLE
 * @property stdClass $cf {virtual}
 *
 * -> READ-ONLY
 * @property-read string $url {virtual}
 * @property-read array|null $path {virtual}
 * @property-read string $stringId {virtual}
 * @property-read int|null $param1Id {virtual}
 * @property-read int|null $param2Id {virtual}
 * @property-read int|null $param1ValueId {virtual}
 * @property-read int|null $param2ValueId {virtual}
 * @property-read int|null $paramValueIds {virtual}
 * @property-read Alias|null $alias {virtual}
 * @property-read string|null $template {virtual}
 * @property-read string|null $keywords {virtual}
 * @property-read string|null $description {virtual}
 * @property-read string|null $annotation {virtual}
 * @property-read ProductImage|null $firstImage {virtual}
 * @property-read string|null $uid {virtual}
 * @property-read string|null $name {virtual}
 * @property-read string|null $nameLang {virtual}
 * @property-read string|null $nameAnchor {virtual}
 * @property-read string|null $nameTitle {virtual}
 * @property-read int|null $priceDiscountDPH {virtual}
 * @property-read int|null $salePriceLowest {virtual}
 *
 * @property-read float $price {virtual} // cena bez DPH
* // * property int $priceDPH	// cena s DPH
 * @property-read float $DPH {virtual} // samotné DPH
 *
 * @property-read float $priceFinal {virtual} // výsledná cena bez DPH
 * @property-read float $priceFinalDPH {virtual} // výsledná cena s DPH
 * @property-read float $priceRealOrigDph {virtual}
 * @property-read float $finalDPH {virtual} // výsledné DPH
 *
 * @property-read int $isOld {virtual}
 * @property-read bool $isRealSale {virtual}
 * @property-read int $isBig {virtual}
 * @property-read string|null $content {virtual}
 * @property-read Supply[]|ArrayIterator $supply {virtual}
 * @property-read ProductContent[]|OneHasMany $contents {virtual}
 * @property-read ProductComment[]|OneHasMany $comments {virtual}
 * @property-read ProductReview[]|OneHasMany $reviews {virtual}
 * @property-read ProductFile[]|OneHasMany $files {virtual}
 * @property-read ProductTree[]|OneHasMany $productTrees {virtual}
 * @property-read ParameterValue[]|ManyHasMany $parametersValues {virtual}
 * @property-read Voucher|OneHasOne|null $voucher {virtual}
 * @property-read array|null $links {virtual}
 * @property-read Tree[]|null $pages {virtual}
 * @property-read array|null $videos {virtual}
 * @property-read Product[]|null $products {virtual}
 * @property-read ProductImage[]|OneHasMany $images {virtual}
 * @property-read ProductImage[]|OneHasMany $imagesDetail {virtual}
 * @property-read bool $isShell {virtual}
 * @property-read Tree[]|null $pagesReviews {virtual}
 * @property-read Tree[]|null $pagesArticles {virtual}
 * @property-read ProductVariant[]|null $accessories {virtual}
 * @property-read ProductVariant[]|null $similarProductsTotal {virtual}
 * @property-read ProductVariant[]|null $productsTotal {virtual}
 * @property-read bool $hasDiscount {virtual}
 * @property-read ProductVariantPrice|null $discount {virtual}
 * @property-read bool $isInPrepare {virtual}
 * @property-read bool $isWarranty {virtual}
 * @property-read int $totalSupplyCount {virtual}
 * @property-read ProductParameterIcon[]|null $paramsIconsDetail {virtual}
 * @property-read ProductParameterIcon[]|null $paramsIconsList {virtual}
 * @property-read bool|null $isVariant {virtual}
 * @property-read string|null $variantName {virtual}
 * @property-read string|null $variantNameNoBreak {virtual}
 * @property-read string $cacheId {virtual}
 * @property-read Tree|OneHasOne $articlePages {virtual}
 * @property-read Tree|null $freeClass {virtual}
 * @property-read Tree|null $workshopPage {virtual}
 * @property-read Tree|null $class {virtual}
 * @property-read string|null $googleCategory {virtual}
 * @property-read string|null $heurekaCategory {virtual}
 * @property-read string|null $zboziCategory {virtual}
 * @property-read int|null $isFreeTransportReal {virtual}
 * @property-read Flag[]|ArrayIterator $flags {virtual}
 * @property-read Flag[]|ArrayIterator $flagsAll {virtual}
 * @property-read ICollection|ProductVariantPresent[] $presents {virtual}
 * @property-read ICollection|ProductVariantPresent[] $presentsForSubscription {virtual}
 * @property-read ProductVariant|null $compensationReal {virtual}
 * @property-read ProductVariantPrice|null $firstSubscriptionPrice {virtual}
 * @property-read ProductVariantPrice|null $subscriptionPrice {virtual}
 * @property-read ProductVariantPrice|null $basicPrice {virtual}
 * @property-read int $realDiscount {virtual}
 * @property-read float|null $weightKg {virtual}
 *
 */
class ProductVariant extends Entity
{

	use hasTranslatorTrait;
	use hasOrmTrait;
	use HasCustomFields;

	public const SHELL_STRING_ID = '0-0';

	private array $cache = [];

	/** @var TranslatorDB */
	private $translator;

	/** @var  TreeRepository */
	private $treeRepository;

	/** @var \Nette\Security\User */
	private $user;

	/** @var UserModel */
	private $userModel;

	/** @var ProductVariantRepository */
	private $productVariantRepository;


	public function injectService(TranslatorDB $translator,
	                              TreeRepository $treeRepository,  \Nette\Security\User $user, UserModel $userModel,
	                              ProductVariantRepository $productVariantRepository)
	{
		$this->translator = $translator;
		$this->treeRepository = $treeRepository;
		$this->user = $user;
		$this->userModel = $userModel;
		$this->productVariantRepository = $this->getRepository()->getModel()->productVariant;

	}


	/**
	 * return lowest sale price from price logs durint last sale period.
	 *
	 * @return float
	 * @throws Exception
	 */
	protected function getterSalePriceLowest(): float
	{
		$groupKey = $this->userModel->getGroupKey($this->product->mutation);

		$priceLogCreatedFrom = new DateTimeImmutable('-' . ProductVariantPriceLogger::SALE_FORGET_DAYS . ' days');

		/**
		 * in case when is product action longer than default action period is required to find the lowest price of product from first day of action
		 */
		if ($this->hasDiscount) {
			$groupPrice = $this->getBestDiscount($groupKey);
			if ($groupPrice->productDiscount->isActive) {
				if ($groupPrice->productDiscount->from < $priceLogCreatedFrom) {
					$priceLogCreatedFrom = $groupPrice->productDiscount->from;

					/**
					 * remove default sale period from firstday of action
					 */
					$priceLogCreatedFrom = $priceLogCreatedFrom->modify('-' . ProductVariantPriceLogger::SALE_FORGET_DAYS . ' days');
				}
			}
		}

		$conditions = [
			ICollection::AND,
			['groupKey' => $groupKey],
			['createdAt>=' => $priceLogCreatedFrom],
		];

		if (!$priceLog = $this->priceLogs->toCollection()->orderBy('salePrice', 'ASC')->getBy($conditions)) {
			return $this->priceFinalDPH;
		}

		return $priceLog->salePrice;
	}

	protected function getterPriceDPH(float $priceDPH): float
	{
		return MoneyHelper::specialRounding($priceDPH, $this->product->mutation);
	}

	/**
	 * @return ICollection|EmptyCollection|ProductVariant[]
	 */
	protected function getPresentsByGroup(string $groupKey): ICollection
	{
		if (!isset($this->id)) {
			return new EmptyCollection();
		}

		$now = new DateTimeImmutable();
		$conditions = [
			ICollection::AND,
			['userGroup' => explode('-', $groupKey)],
			['productVariantGift->stock>' => 0],
			['productVariantGift->active' => 1],
			['productVariantGift->product->public' => 1],
			[ICollection::OR, 'validFrom' => null, 'validFrom<=' => $now],
			[ICollection::OR, 'validTo' => null, 'validTo>=' => $now],
		];

		$collection = $this->presentsAll->toCollection()->findBy($conditions)
			->orderBy('sort', ICollection::ASC);

		return $collection;
	}

	/**
	 * @return ICollection|EmptyCollection|ProductVariant[]
	 */
	protected function getterPresents(): ICollection
	{
		$groupKey = $this->userModel->getGroupKey($this->product->mutation);

		return $this->getPresentsByGroup($groupKey);
	}

	protected function getterPresentsForSubscription(): ICollection
	{
		$groups = $this->orm->userGroup->findBy(['mutation' => $this->product->mutation]);

		$presents = new EmptyCollection();
		foreach ($groups as $group) {
			if ($this->hasPresentWithinGroups($group->id)) {
				$presents = $this->getPresentsByGroup((string)$group->id);
			}
		}

		return $presents;
	}

	public function hasPresentWithinGroups(int|array $groupKeys = []): bool
	{
		if ($groupKeys === []) {
			$groupKeys = $this->orm->userGroup->findAll()->findBy([
				'mutation' => $this->product->mutation,
			])->fetchPairs(null, 'id');
		}	elseif (is_int($groupKeys)) {
			$groupKeys = [0 => $groupKeys];
		}

		return $this->getPresentsByGroup(implode('-', $groupKeys))->countStored() > 0;
	}

	public function getterArticlePages(): ICollection
	{
		return $this->product->articlePages;
	}

	public function getterStringId()
	{
		if (!isset($this->cache['stringId'])) {
			$stringId = [];
//			bd($this->param1Value);
			if ($this->param1Value) {
				$stringId[] = $this->param1Value->id;
			} else {
				$stringId[] = 0;
			}
			if ($this->param2Value) {
				$stringId[] = $this->param2Value->id;
			} else {
				$stringId[] = 0;
			}
			$this->cache['stringId'] = implode('-', $stringId);
		}

		return $this->cache['stringId'];
	}


	public function getterParam1Id()
	{
		if ($this->param1Value) {
			return $this->param1Value->id;
		} else {
			return NULL;
		}
	}


	public function getterParam2Id()
	{
		if ($this->param2Value) {
			return $this->param2Value->id;
		} else {
			return NULL;
		}
	}


	public function getterParam2ValueId()
	{
		if ($this->param2Value) {
			return $this->param2Value->id;
		} else {
			return NULL;
		}
	}


	public function getterParam1ValueId()
	{
		if ($this->param1Value) {
			return $this->param1Value->id;
		} else {
			return NULL;
		}
	}


	protected function getterCreated($created)
	{
		if ($created) {
			return new DateTimeImmutable($created->format("Y-m-d H:i:s"));
		}
	}


	protected function getterEdited($edited)
	{
		if ($edited) {
			return new DateTimeImmutable($edited->format("Y-m-d H:i:s"));
		}
	}


	protected function getterVoucher()
	{
		return $this->product->voucher;
	}


	protected function getterName()
	{
		return $this->product->name;
	}


	public function getterVariantName()
	{
		return $this->nameWeight;
	}


	public function getterVariantNameNoBreak()
	{
		$variantName = str_replace(' ', '&nbsp;', $this->nameWeight);
		return $variantName;
	}


	public function getterNameLang()
	{
		return $this->product->nameLang;
	}


	protected function getterParamValueIds()
	{
		$ret = [];
		if (isset($this->param1Value->id)) {
			$ret[] = $this->param1Value->id;
		}
		if (isset($this->param2Value->id)) {
			$ret[] = $this->param2Value->id;
		}
		return $ret;
	}


	protected function getterIsShell()
	{
		return $this->stringId == self::SHELL_STRING_ID;
	}


	public function getSupplyForStock(Stock $stock)
	{
		if ($this->isAfttached()) {
			foreach ($this->supplies as $supply) {
				if ($supply->stock == $stock) {
					return $supply;
				}
			}
		}

		// return empty supply for stock
		$emptySupply = new Supply();
		$emptySupply->variant = $this;
		$emptySupply->stock = $stock;

		return $emptySupply;
	}


	protected function getterPath()
	{
		return $this->product->path;
	}


	protected function getterTemplate()
	{
		return $this->product->template;
	}


	protected function getterKeywords()
	{
		return $this->product->keywords;
	}


	protected function getterDescription()
	{
		return $this->product->description;
	}


	protected function getterAnnotation()
	{
		return $this->product->annotation;
	}


	protected function getterAlias()
	{
		return $this->product->alias;
	}


	protected function getterIsInPrepare()
	{
		return $this->product->isInPrepare;
	}


	protected function getterIsWarranty()
	{
		return $this->product->isWarranty;
	}


	protected function getterIsRealSale()
	{
		return $this->totalSupplyCount && $this->priceFinalDPH > 0 && $this->active && $this->product->isSale && $this->product->public && !$this->product->isGift;
	}


	/**
	 * @return ProductVariant|null
	 */
	protected function getterCompensationReal()
	{
		if ($this->compensation) { // retezeni - vrat prvni aktivni prodejnou nahradu
			return $this->compensation->isRealSale ? $this->compensation : $this->compensation->compensationReal;
		}

		return null;
	}


	//TODO REF VOJTA ??
	protected function getterFirstImage()
	{
		//return $this->product->firstImage;
		return $this->images->current();
	}


	// nalezeni obrazku pro danou variantu
	protected function getterImages()
	{
		$images = $this->product->images;
		$finalImages = [];
		foreach ($images as $i) {
			$toSearch = explode("|", $i->variants ?? '');
			if (in_array($this->id, $toSearch)) {
				$finalImages[] = $i;
			} elseif ($i->variants === NULL || !$i->variants) {
				$finalImages[] = $i;
			}
		}
		return new ArrayIterator($finalImages);
	}


	// obrazky na detail zohlednujici priznak hideFirstImage !
	protected function getterImagesDetail()
	{
		if ($this->images) {
			$final = (array) $this->images;
		} else {
			return new ArrayIterator();
		}

		return new ArrayIterator($final);
	}


	protected function getterTotalSupplyCount()
	{
		return $this->stock;
	}


	// vraceni nejlepsi doby doruceni
	// pokud je zadany transportType:
	// pr: produkt je k osobnimu odberu hned - u sobnuho odberu dam hned, u jinych doprav + 1 den
	// pr: produkt je není v sosbním odberu hned, neresim
	public function getBestSupplyDeliveryDate($wantToBuy = NULL, $transportType = NULL)
	{
		$bestDate = NULL;
		foreach ($this->supplies as $supply) {
			$dt = $supply->getDeliveryTime($wantToBuy, $transportType);
			if ($dt && $bestDate === NULL) {
				$bestDate = $dt;
			} elseif ($dt) {
				// jen pokud je blizsi cas
				if ($dt['dateTime'] < $bestDate['dateTime']) {
					$bestDate = $dt;
				}
			}
		}
		return $bestDate;
	}


	protected function getterUid()
	{
		return $this->product->uid;
	}


	protected function getterNameAnchor()
	{
		return $this->nameDefault;
	}


	protected function getterNameTitle()
	{
		return $this->product->nameTitle;
	}


	protected function getterPriceDiscountDPH()
	{
		return $this->priceFinalDPH - $this->priceDPH;
	}


	protected function getterPrice(): float
	{
		return $this->priceDPH - $this->DPH;
	}


	public function getterDPH(): float
	{
		return MoneyHelper::getDPH($this->vat, $this->priceDPH);
	}


	// pro vypis v administrace
	public function getPriceFinalDPHForGroup($groupKey = '1')
	{
		return $this->getBestPriceDPH($groupKey);
	}


	protected function getterPriceFinal(): float
	{
		return round(($this->priceFinalDPH - $this->finalDPH), 2);
	}


	protected function getterPriceFinalDPH(): float
	{
		$groupKey = $this->userModel->getGroupKey($this->product->mutation);
		return $this->getBestPriceDPH($groupKey);
	}

	protected function getterPriceRealOrigDph(): float
	{
		$groupKey = $this->userModel->getGroupKey($this->product->mutation);
		return $this->getBestDiscount($groupKey)->realOrigPriceDPH;
	}


	protected function getterFinalDPH(): float
	{
		return MoneyHelper::getDPH($this->vat, $this->priceFinalDPH);
	}


	public function isSampleAndAvailableFree()
	{
		if ($this->product->isSample) {
			return $this->userModel->isRightToSamplesFree();
		}

		return false;
	}


	protected function getBestPriceDPH($groupKey = null)
	{
		$lowestDiscount = $this->getBestDiscount($groupKey);

		if (isset($lowestDiscount)) {
			$return = $lowestDiscount->priceDPH;
		} else {
			$return = $this->getBasePrice();
		}

		return MoneyHelper::specialRounding($return, $this->product->mutation);
	}


	protected function getBestDiscount(int|string $groupKey): ProductVariantPrice|null
	{
		return $this->prices->toCollection()->getBy(['groupKey' => $groupKey]);
	}

	public function hasDiscountWithinGroups($groupKeys = [], ?bool $onlyActiveDiscount = false): bool
	{
		$hasDiscount = false;
		if (empty($groupKeys)) {
			$groupKeys = $this->orm->userGroup->findAll()->findBy([
				'mutation'=>$this->product->mutation,
			])->fetchPairs(null,'id');
		}

		if (is_integer($groupKeys)) {
			$int = $groupKeys;
			$groupKeys = [];
			$groupKeys[0] = $int;
		}

		foreach ($groupKeys as $gKey=>$gK) {
			$discount = $this->getBestDiscount($gK);
			if ($discount) {
				//$hasDiscount = true;
				if ($discount->productDiscount instanceof ProductDiscount && $onlyActiveDiscount) {
					$now = new DateTimeImmutable();
					if ($discount->productDiscount->from <= $now && $discount->productDiscount->to >= $now) {
						$hasDiscount = true;
					} else {
						$hasDiscount =  false;
					}
				}
			}
		}

		return $hasDiscount;
	}

	protected function getterHasDiscount(): bool
	{
		$groupKey = $this->userModel->getGroupKey($this->product->mutation);
		$discount = $this->getBestDiscount($groupKey);

		return $discount !== null && $discount->priceOriginDPH > 0.001;
	}

	protected function getterDiscount(): ProductVariantPrice|null
	{
		$groupKey = $this->userModel->getGroupKey($this->product->mutation);

		return $this->getBestDiscount($groupKey);
	}

	public function getBasePrice(): float
	{
		$return = $this->priceDPH > 0.001 ? $this->priceDPH : $this->retailPriceVat;

		return MoneyHelper::specialRounding($return, $this->product->mutation);
	}

	protected function getterContent(): string|null
	{
		return $this->product->content;
	}

	protected function getterContents()
	{
		return $this->product->contents;
	}


	protected function getterFiles()
	{
		return $this->product->files;
	}


	protected function getterLinks()
	{
		return $this->product->links;
	}


	protected function getterPages()
	{
		return $this->product->pages;
	}


	protected function getterVideos()
	{
		return $this->product->videos;
	}


	protected function getterProducts()
	{
		return $this->product->products;
	}


	protected function getterIsOld()
	{
		return $this->product->isOld;
	}


	protected function getterIsBig()
	{
		return $this->product->isBig;
	}


	protected function getterPagesReviews()
	{
		return $this->product->pagesReviews;
	}


	protected function getterPagesArticles()
	{
		return $this->product->pagesArticles;
	}


	protected function getterAccessories()
	{
		return $this->product->accessories;
	}


	protected function getterSimilarProductsTotal()
	{
		return $this->product->similarProductsTotal;
	}


	protected function getterProductsTotal()
	{
		return $this->product->productsTotal;
	}


	public function getParameterValueByUid($parameterUid)
	{
		return $this->product->getParameterValueByUid($parameterUid);
	}


	public function getParameters()
	{
		return $this->product->getParameters();
	}


	public function getterParamsIconsDetail()
	{
		return $this->product->paramsIconsDetail;
	}


	public function getterParamsIconsList()
	{
		return $this->product->paramsIconsList;
	}


	public function getterIsVariant()
	{
		return TRUE;
	}


	public function getParentCat()
	{
		$parentId = NULL;
		if ($this->path) {
			$parentId = end($this->path);
		}
		if (!$parentId) {
			return $this->treeRepository->getByUid("eshop");
		}
		return $this->treeRepository->getById($parentId);
	}


	public function getActionEnds()
	{
		if (!$this->hasDiscount) {
			return NULL;
		}

		$now = new DateTime();
		foreach ($this->discounts as $discount) {
			if ($discount->to) {
				return $now->diff($discount->to);
			}
		}

		return NULL;
	}


	protected function getterCacheId()
	{
		return 'var' . $this->id;
	}


	protected function getterWorkshopPage()
	{
		return $this->product->workshopPage;
	}


	// REMOVE
	protected function getterClass()
	{
		return $this->workshopPage;
	}


	// REMOVE
	protected function getterFreeClass()
	{
		return $this->workshopPage;
	}


	protected function getterHeurekaCategory()
	{
		if (is_array($this->path)) {
			foreach (array_reverse($this->path) as $parentId) {
				$page = $this->treeRepository->getById($parentId);
				if (isset($page->cf->feeds) && isset($page->cf->feeds->heureka) && $page->cf->feeds->heureka) {
					return $page->cf->feeds->heureka;
				}
			}
		}
	}


	protected function getterZboziCategory()
	{
		if (is_array($this->path)) {
			foreach (array_reverse($this->path) as $parentId) {
				$page = $this->treeRepository->getById($parentId);
				if (isset($page->cf->feeds) && isset($page->cf->feeds->zbozi) && $page->cf->feeds->zbozi) {
					return $page->cf->feeds->zbozi;
				}
			}
		}

	}


	protected function getterGoogleCategory()
	{
		if (is_array($this->path)) {

			foreach (array_reverse($this->path) as $parentId) {
				$page = $this->treeRepository->getById($parentId);
				if (isset($page->cf->feeds) && isset($page->cf->feeds->google) && $page->cf->feeds->google) {
					return $page->cf->feeds->google;
				}
			}
		}
	}


	protected function getterIsFreeTransportReal()
	{
		if ($this->isFreeTransport) {
			return TRUE;
		} else {
			$limit = 5000;
//			if ($this->user->isLoggedIn()) {
//				$limit = 2000;
//			}
			if ($this->priceFinalDPH >= $limit) {
				return TRUE;
			}
		}

		return FALSE;
	}

	/**
	 * @return ArrayIterator
	 */
	public function getRelatedAnimals(User $user)
	{
		return $this->product->getRelatedAnimals($user);
	}


    // klasicke stitky
	protected function getterFlags(): ArrayIterator
	{
		if (!isset($this->cache['flags'])) {
			$flags = new ArrayIterator();
			if ($this->flagsJson) {
				$data = json_decode($this->flagsJson);

				foreach ($data as $flagId) {
					$flags[] = $this->orm->flag->getById($flagId);
				}
			}

			$this->cache['flags'] = $flags;
		}

		return $this->cache['flags'];
	}

	/**
	 * standard flags + flags from presents and discounts + free transport + parameter Product line
	 */
	protected function getterFlagsAll(): ArrayIterator
	{
		if (!isset($this->cache['allFlags'])) {

			$_labels = $this->flags;

			$labels = [];
			foreach ($_labels as $i) {
				$labels[$i->id] = $i;
			}

			if ($this->hasDiscount && $this->discount?->flagTop !== null) {
				$labels[$this->discount->flagTop->id] = $this->discount->flagTop;
			}

			foreach ($this->presents as $present) {
				if (isset($present->flagTop)) {
					$labels[$present->flagTop->id] = $present->flagTop;
				}
			}

			$flagsAll = $this->orm->flag->findBy(['mutation' => $this->product->mutation])->orderBy('sort');
			$productLineValue = $this->getParameterValueByUid('productLine');

			$final = new ArrayIterator();
			foreach ($flagsAll as $f) {
				// klasicke stitky
				if ($f->type == Flag::TYPE_LABEL && isset($labels[$f->id])) {
					$final[] = $f;
				}

				// doprava zdarma
				if ($f->type === Flag::TYPE_TRANSPORT_FREE && (bool) $this->isFreeTransport) {
					$final[] = $f;
				}

				// produktova rada
				if ($f->type === Flag::TYPE_PARAM_PRODUCT_LINE && $productLineValue && $productLineValue->id==$f->paramValue) {
					$final[] = $f;
				}
			}

			$this->cache['allFlags'] = $final;
		}

		return $this->cache['allFlags'];
	}


	protected function getterRealDiscount(): int
	{
		$groupKey = $this->userModel->getGroupKey($this->product->mutation);
		$priceEntity = $this->getBestDiscount($groupKey);
		assert($priceEntity instanceof ProductVariantPrice);

		if (!$priceEntity->hasDiscount) {
			return 0;
		}

		if ($priceEntity->isPriceAccordingToLaw) {
			return isset($priceEntity->productDiscount) ? intval($priceEntity->productDiscount->percent) :
				(isset($priceEntity->userGroupDiscount) ? intval($priceEntity->userGroupDiscount->value) : 0);
		} else {
			return abs((int) round(($this->priceRealOrigDph - $this->priceFinalDPH) * 100 / $this->priceRealOrigDph));
		}
	}

	protected function getterFirstSubscriptionPrice(): ?ProductVariantPrice
	{
		$price = null;
		$userGroup = $this->orm->userGroup->getBy(['mutation' => $this->product->mutation, 'type' => UserGroup::TYPE_FIRST_SUBSCRIPTION]);
		if ($userGroup instanceof UserGroup) {
			$price = $this->prices->toCollection()->getBy(['groupKey' => $userGroup->id]);
			if ($price === null) {
				$userGroup = $this->orm->userGroup->getBy(['mutation' => $this->product->mutation, 'type' => UserGroup::TYPE_DEFAULT]);
				$price = $this->prices->toCollection()->getBy(['groupKey' => $userGroup->id]);
			}
		}

		if ($price === null) {
			$price = $this->prices->toCollection()->getBy(['mutationId' => $this->product->mutation->id]);
		}

		return $price;
	}

	protected function getterSubscriptionPrice(): ?ProductVariantPrice
	{
		$price = null;
		$userGroup = $this->orm->userGroup->getBy(['mutation' => $this->product->mutation, 'type' => UserGroup::TYPE_SUBSCRIPTION]);
		if ($userGroup instanceof UserGroup) {
			$price = $this->prices->toCollection()->getBy(['groupKey' => $userGroup->id]);
			if ($price === null) {
				$userGroup = $this->orm->userGroup->getBy(['mutation' => $this->product->mutation, 'type' => UserGroup::TYPE_DEFAULT]);
				$price = $this->prices->toCollection()->getBy(['groupKey' => $userGroup->id]);
			}
		}

		if ($price === null) {
			$price = $this->prices->toCollection()->getBy(['mutationId' => $this->product->mutation->id]);
		}

		return $price;
	}

	protected function getterBasicPrice(): ?ProductVariantPrice
	{
		$price = null;
		$userGroup = $this->orm->userGroup->getBy(['mutation' => $this->product->mutation, 'type' => UserGroup::TYPE_DEFAULT]);
		if ($userGroup) {
			$price = $this->prices->toCollection()->getBy(['groupKey' => $userGroup->id]);
		}

		if ($price === null) {
			$price = $this->prices->toCollection()->getBy(['mutationId' => $this->product->mutation->id]);
		}

		return $price;
	}

    public function isFlagSet(Flag $flag)
    {
    	if ($this->flags->count() > 0) {
    		foreach ($this->flags as $f) {
    			if ($f->id == $flag->id) {
    				return true;
			    }
		    }
	    }
	    return false;
    }


	/**
	 * @param int $stock
	 * @param int $amount
	 * @return int
	 */
	public static function getQdStock(int $stock, int $amount)
	{
		$qdStock = 0;

		if ($stock > 0 && $amount > 0 && $stock >= $amount) {
			$qdStock = floor($stock / $amount);
		}

		return (int) $qdStock;
    }

	protected function getterWeightKg(): ?float
	{
		return $this->weight !== null ? $this->weight / 1000 : null;
	}

}
