<?php

namespace App\Model;

use DateInterval;
use Nette\Utils\Floats;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\Noviko\PriceLogs\ProductVariantPriceLogger;
use Tracy\Debugger;
use function count;
use function explode;
use function strlen;

class ProductVariantModel
{

	private array $userGroupsCache = [];

	public function __construct(
		private readonly Orm $orm,
		private readonly ProductVariantPriceLogger $productVariantPriceLogger
	)
	{
	}


	public function createEmpty($userId = NULL)
	{
		$emptyVariant = new ProductVariant();
		$emptyVariant->priceDPH = 0;
		$emptyVariant->active = 1;
		$emptyVariant->ean = 0;
		$emptyVariant->code = '';
		$emptyVariant->created = new DateTimeImmutable();
		$emptyVariant->createdBy = $userId;
		$emptyVariant->edited = new DateTimeImmutable();
		$emptyVariant->editedBy = $userId;

		return $emptyVariant;
	}


	public function create($params, $userId = NULL)
	{
		usort($params, function ($a, $b) {
			return $a->id <= $b->id;
		});

		$variant = new ProductVariant();

		$variant->param1 = $this->orm->parameter->getById($params[0]->id);
		$variant->param1Value = $this->orm->parameterValue->getById($params[0]->value);

		if (isset($params[1]->id)) {
			$variant->param2 = $this->orm->parameter->getById($params[1]->id);
			$variant->param2Value = $this->orm->parameterValue->getById($params[1]->value);
		}

		$variant->stockCount = 0;
		$variant->priceDPH = 0;
		$variant->onAsk = 0;
		$variant->ean = 0;
		$variant->code = '';
		$variant->created = new DateTimeImmutable();
		$variant->createdBy = $userId;
		$variant->edited = new DateTimeImmutable();
		$variant->editedBy = $userId;

		return $variant;
	}

	public function setBestPricesPerGroups(ProductVariant $variant, array $groups, bool $flush = true): void
	{
		bd("setBestPricesPerGroups - " . $variant->id);

		$priceData = $this->preparePriceCalculationData($variant);
		$lowestNonSubscriptionPrice = 9999999.00;

		foreach ($groups as $groupKey) {
			$groupPriceResult = $this->calculateBestPriceForGroup(
				$variant,
				$groupKey,
				$priceData['basePrice'],
				$priceData['productLine'],
				$priceData['discountCache']
			);

			if ($groupPriceResult['isSubscription']) {
				$groupPriceResult['finalPrice'] = $this->applySubscriptionPriceLogic(
					$groupPriceResult['finalPrice'],
					$lowestNonSubscriptionPrice
				);
			}

			$priceLine = $this->createOrUpdatePriceLine(
				$variant,
				$groupKey,
				$groupPriceResult,
				$priceData
			);

			if (!$groupPriceResult['isSubscription']) {
				$lowestNonSubscriptionPrice = min($lowestNonSubscriptionPrice, $priceLine->priceDPH);
			}

			$this->logPriceChange($variant, $groupKey, $groupPriceResult, $priceData['basePrice']);
			$this->orm->productVariantPrice->persist($priceLine);
		}

		if ($flush) {
			$this->orm->flush();
		}
	}

	private function preparePriceCalculationData(ProductVariant $variant): array
	{
		$priceLines = $this->orm->productVariantPrice->findBy(['productVariant' => $variant])->orderBy('id');
		$defaultGroup = $this->orm->userGroup->getBy(['type' => 'default', 'mutation' => $variant->product->mutation->id]);
		$defaultGroupPrice = $priceLines->getBy(['groupKey' => $defaultGroup->id]);
		$currentPriceLines = $priceLines->fetchPairs('groupKey', null);

		$defaultPrice = ($defaultGroupPrice instanceof ProductVariantPrice)
			? $defaultGroupPrice->priceDPH
			: 99999999;

		$basePrice = $variant->priceDPH ?: $variant->retailPriceVat;
		$productLine = $variant->getParameterValueByUid('productLine');

		return [
			'currentPriceLines' => $currentPriceLines,
			'defaultPrice' => $defaultPrice,
			'basePrice' => $basePrice,
			'productLine' => $productLine,
			'discountCache' => []
		];
	}

	private function calculateBestPriceForGroup(
		ProductVariant $variant,
		string $groupKey,
		float $basePrice,
		?string $productLine,
		array &$discountCache
	): array {
		$groupIds = array_map('intval', explode('-', $groupKey));
		$userGroup = $this->orm->userGroup->getByIdChecked($groupIds[0]);
		$isSubscription = in_array($userGroup->type, [UserGroup::TYPE_SUBSCRIPTION, UserGroup::TYPE_FIRST_SUBSCRIPTION]);

		$bestPrice = $basePrice;
		$discountDetails = [
			'flag' => null,
			'flagTop' => null,
			'type' => null,
			'userGroupDiscount' => null,
			'productDiscount' => null
		];

		if ($this->shouldApplyDiscounts($variant)) {
			$userGroupDiscount = $this->findBestUserGroupDiscount($variant, $groupIds, $productLine, $basePrice, $discountCache);
			if ($userGroupDiscount && $userGroupDiscount->getPriceAfterDiscount($basePrice) < $bestPrice) {
				bd("apply group discount");
				$bestPrice = $userGroupDiscount->getPriceAfterDiscount($basePrice);
				$discountDetails = [
					'flag' => $userGroupDiscount->flag,
					'flagTop' => $userGroupDiscount->flagTop,
					'type' => $userGroupDiscount->type,
					'userGroupDiscount' => $userGroupDiscount,
					'productDiscount' => null
				];
			}

			$productDiscount = $this->findBestProductDiscount($variant, $groupIds);
			if ($productDiscount && $productDiscount->priceFinalDPH < $bestPrice) {
				bd("apply variant discount");
				$bestPrice = $productDiscount->priceFinalDPH;
				$discountDetails = [
					'flag' => $productDiscount->flag,
					'flagTop' => $productDiscount->flagTop,
					'type' => 'action',
					'userGroupDiscount' => null,
					'productDiscount' => $productDiscount
				];
			}
		}

		bd("final best price for group $groupKey = " . $bestPrice);

		return [
			'finalPrice' => $bestPrice,
			'isSubscription' => $isSubscription,
			'groupIds' => $groupIds
		] + $discountDetails;
	}

	private function shouldApplyDiscounts(ProductVariant $variant): bool
	{
		return !$variant->isQuantityDiscount || ($variant->isQuantityDiscount && $variant->useQuantityUserGroupDiscount);
	}

	private function findBestUserGroupDiscount(
		ProductVariant $variant,
		array $groupIds,
		?string $productLine,
		float $basePrice,
		array &$discountCache
	): ?UserGroupDiscount {
		if (!$productLine) {
			return null;
		}

		$bestDiscount = null;

		foreach ($groupIds as $groupId) {
			$cacheKey = $groupId . "-" . $productLine;

			if (isset($discountCache[$cacheKey])) {
				$discount = $discountCache[$cacheKey];
			} else {
				$discount = $this->loadUserGroupDiscounts($variant, $groupId, $productLine);
				$discountCache[$cacheKey] = $discount;
			}

			if ($discount && (!$bestDiscount || $discount->getPriceAfterDiscount($basePrice) < $bestDiscount->getPriceAfterDiscount($basePrice))) {
				$bestDiscount = $discount;
			}
		}

		return $bestDiscount;
	}

	private function loadUserGroupDiscounts(ProductVariant $variant, int $groupId, string $productLine): ?UserGroupDiscount
	{
		$groupDiscounts = $this->orm->userGroupDiscount->findBy(['userGroup' => $groupId]);
		$groupDiscounts = $groupDiscounts->findBy([ICollection::OR, ['productLine' => $productLine], ['productLine' => null]]);

		if ($variant->hasPresentWithinGroups($groupId)) {
			$groupDiscounts = $groupDiscounts->findBy(['combinesWithGifts' => true]);
		}

		$bestDiscount = null;
		foreach ($groupDiscounts as $discount) {
			if ($discount->isActive && (!$bestDiscount || $discount->getPriceAfterDiscount(1) < $bestDiscount->getPriceAfterDiscount(1))) {
				$bestDiscount = $discount;
			}
		}

		return $bestDiscount;
	}

	private function findBestProductDiscount(ProductVariant $variant, array $groupIds): ?ProductDiscount
	{
		$bestDiscount = null;

		foreach ($groupIds as $groupId) {
			$discounts = $this->orm->discount->findByVariantAndUserGroup($variant, $groupId);
			foreach ($discounts as $discount) {
				if (!$bestDiscount || $discount->priceFinalDPH < $bestDiscount->priceFinalDPH) {
					$bestDiscount = $discount;
				}
			}
		}

		return $bestDiscount;
	}

	private function applySubscriptionPriceLogic(float $subscriptionPrice, float $lowestNonSubscriptionPrice): float
	{
		return min($subscriptionPrice, $lowestNonSubscriptionPrice);
	}

	private function createOrUpdatePriceLine(
		ProductVariant $variant,
		string $groupKey,
		array $groupPriceResult,
		array $priceData
	): ProductVariantPrice {
		if (isset($priceData['currentPriceLines'][$groupKey])) {
			$priceLine = $priceData['currentPriceLines'][$groupKey];
			bd('update groupKey ' . $priceLine->groupKey);
			unset($priceData['currentPriceLines'][$groupKey]);
		} else {
			$priceLine = new ProductVariantPrice();
			$priceLine->productVariant = $variant;
			$priceLine->productId = $variant->product->id;
			$priceLine->mutationId = $variant->product->mutation->id;
			$priceLine->groupKey = $groupKey;
			bd('insert groupKey ' . $groupKey);
		}

		$priceLine->priceDPH = $groupPriceResult['finalPrice'];
		$priceLine->flag = $groupPriceResult['flag'];
		$priceLine->flagTop = $groupPriceResult['flagTop'];
		$priceLine->productDiscount = $groupPriceResult['productDiscount'];
		$priceLine->userGroupDiscount = $groupPriceResult['userGroupDiscount'];

		if ($groupPriceResult['type'] === 'action') {
			$priceLine->priceOriginDPH = $priceData['basePrice'];
		} elseif ($groupPriceResult['isSubscription']) {
			$priceLine->priceOriginDPH = $priceData['defaultPrice'];
		} else {
			$priceLine->priceOriginDPH = 0;
		}

		return $priceLine;
	}

	private function logPriceChange(ProductVariant $variant, string $groupKey, array $groupPriceResult, float $basePrice): void
	{
		if ($variant->priceLogs->toCollection()->findBy(["groupKey" => $groupKey])->countStored() == 0) {
			$this->productVariantPriceLogger->initLog($variant, $groupKey, $basePrice, $groupPriceResult['finalPrice']);
		} else {
			$computeRealOrigPrice = $groupPriceResult['type'] === 'action'
				&& filter_var($groupKey, FILTER_VALIDATE_INT) !== false
				&& $this->orm->userGroup->getByIdChecked((int) $groupKey)->type === 'default';

			$origPrice = $groupPriceResult['type'] === 'action' ? $basePrice : $groupPriceResult['finalPrice'];
			$this->productVariantPriceLogger->createLogWithChangeCheck(
				$variant,
				$groupKey,
				$origPrice,
				$groupPriceResult['finalPrice'],
				$computeRealOrigPrice
			);
		}
	}

	public function savePresents(ProductVariant $productVariant, $data)
	{
		$actualProducts = [];
		foreach ($productVariant->presentsAll as $present) {
			$actualProducts[$present->productVariantGift->id . '-' . $present->userGroup->id] = $present;
		}

		$presentsFinal = [];
		if (isset($data['presents'])) {
			$newSort = 0;
			foreach ($data['presents'] as $k => $attachProductVariantId) {
				$dateTo = strlen($data['validTo'][$k]) > 0 ? new DateTimeImmutable($data['validTo'][$k]) : null;

				if (isset($dateTo)) {
					$dateTo = $dateTo->setTime(23, 59, 59);
				}

				$presentsFinal[$attachProductVariantId . '-' . $data['presentsGroup'][$k]] = [
					'sort' => $newSort,
					'validFrom' => strlen($data['validFrom'][$k]) > 0 ? new DateTimeImmutable($data['validFrom'][$k]) : null,
					'validTo' => $dateTo,
					'flagTopId' => strlen($data['flagTopId'][$k]) > 0 ? $data['flagTopId'][$k] : null,
				];
				$newSort++;
			}
		}

		if (count($presentsFinal) > 0) {
			foreach ($presentsFinal as $key => $presentData) {
				$sort = $presentData['sort'];
				$parts = explode('-', $key);
				$attachProductVariantId = $parts[0];
				$groupId = $parts[1];

				if (isset($actualProducts[$key]) || strlen($attachProductVariantId) > 0) {
					if (isset($actualProducts[$key])) {
						$present = $actualProducts[$key];
					} else {
						$present = new ProductVariantPresent();
						$this->orm->productVariantPresent->attach($present);
						$present->productVariant = $productVariant;
						$present->productVariantGift = (int) $attachProductVariantId;
					}

					$present->userGroup = (int) $groupId;
					$present->sort = $sort;
					$present->validFrom = isset($presentData['validFrom']) ? new DateTimeImmutable($presentData['validFrom']) : null;
					$present->validTo = isset($presentData['validTo']) ? new DateTimeImmutable($presentData['validTo']) : null;
					$present->flagTop = (int) $presentData['flagTopId'];
					$this->orm->productVariantPresent->persist($present);
					unset($actualProducts[$key]);
				}
			}
		}

		// remove
		foreach ($actualProducts as $attachProductVariantId => $i) {
			$parts = explode('-', $attachProductVariantId);
			$attachProductVariantId = $parts[0];
			$groupId = $parts[1];
			$entity = $productVariant->presentsAll->toCollection()->getBy(['productVariantGift' => $attachProductVariantId, 'userGroup' => $groupId]);
			if (isset($entity)) {
				$this->orm->productVariantPresent->remove($entity);
			}
		}

		$this->orm->flush();
	}

}


