<?php
/**
 * Created by PhpStorm.
 * User: vojta
 * Date: 28.11.17
 * Time: 10:55
 */

namespace App\Model;

use SuperKoderi\ConfigService;

class LibraryImageModel
{
	private $orm;

	private $configService;

	public function __construct(Orm $orm, ConfigService $configService)
	{
		$this->orm = $orm;
		$this->configService = $configService;
	}


	public function add(\Nette\Http\FileUpload $file, $cat, $isCopy = FALSE, $sourceImage = NULL, $md5 = NULL, array $options = [])
	{
		$name = $file->getName();
		// name without extension
		$name = @substr($name, 0, @strrpos($name, '.'));


		$newImage = new LibraryImage();
		$newImage->name = $name;
		$newImage->library = $this->orm->libraryTree->getById($cat);

		$this->orm->libraryImage->persist($newImage);

		$filename = $newImage->id.'-'.strtolower($file->getSanitizedName());
		//echo WWW_DIR.IMAGES_DIR.$filename;

		// kontrola zda cesta existuje
		if (!file_exists(WWW_DIR.IMAGES_DIR)) {
			mkdir(WWW_DIR.IMAGES_DIR, 0755, true);
		}

		if ($isCopy) {
			copy($file->getTemporaryFile(),WWW_DIR.IMAGES_DIR.$filename);
		} else {
			$file->move(WWW_DIR.IMAGES_DIR.$filename);
		}

		if ($this->configService->get('imageOriginal', 'resize')) {
			// resize Orig Image - to save space on disk
			$NewImageWidth      = $this->configService->get('imageOriginal', 'width'); //New Width of Image
			$NewImageHeight     = $this->configService->get('imageOriginal', 'height'); // New Height of Image
			$Quality        = 95; //Image Quality
			$imagePath = WWW_DIR.IMAGES_DIR.$filename;
			$destPath = WWW_DIR.IMAGES_DIR.$filename;
			$checkValidImage = @getimagesize($imagePath);
			if (file_exists($imagePath) && $checkValidImage) { //Continue only if 2 given parameters are true
				//Image looks valid, resize.
				if ($this->resizeImage($imagePath, $destPath, $NewImageWidth, $NewImageHeight, $Quality)) {
					//resize Success
				} else {
					//resize Failed
				}
			}

			// crop
			if (isset($options['crop'])) {
				$netteImage = \Nette\Utils\Image::fromFile($destPath);
				$width = $options['crop'][2] - $options['crop'][0];
				$height = $options['crop'][3] - $options['crop'][1];

				if (isset($options['square'])) { // korekce, aby byl vzdy presny ctverec
					if ($width > $height) {
						$width = $height;
					} elseif ($height > $width) {
						$height = $width;
					}
				}

				$netteImage = $this->crop($netteImage, $options['crop'][0], $options['crop'][1], $width, $height);
				$netteImage->save($destPath);
			}
		}

		$newImage->filename = $filename;
		$newImage->sourceImage = $sourceImage;
		$newImage->md5 = $md5;
		$newImage->sort = -$newImage->id;

		$this->orm->libraryImage->persistAndFlush($newImage);

		return $newImage;
	}

	/**
	 * Crops image.
	 * @param $image
	 * @param $left
	 * @param $top
	 * @param $width
	 * @param $height
	 * @return \Nette\Utils\Image
	 */
	public function crop($image, $left, $top, $width, $height)
	{
		list($r['x'], $r['y'], $r['width'], $r['height']) = \Nette\Utils\Image::calculateCutout($image->getWidth(), $image->getHeight(), $left, $top, $width, $height);

		$newImage = \Nette\Utils\Image::fromBlank($r['width'], $r['height'], \Nette\Utils\Image::RGB(0, 0, 0, 127))->getImageResource();
		imagecopy($newImage, $image->getImageResource(), 0, 0, $r['x'], $r['y'], $r['width'], $r['height']);


		return new \Nette\Utils\Image($newImage);
	}

	// TODO REF presunout - mela by existovat jedna ovecna classa na resize obrazku
	function resizeImage($SrcImage,$DestImage, $MaxWidth,$MaxHeight,$Quality)
	{
		list($iWidth,$iHeight,$type)    = getimagesize($SrcImage);

		if ($iWidth <= $MaxWidth && $iHeight<= $MaxHeight) {
			return false;
		}

		$ImageScale             = min($MaxWidth/$iWidth, $MaxHeight/$iHeight);
		$NewWidth               = ceil($ImageScale*$iWidth);
		$NewHeight              = ceil($ImageScale*$iHeight);

		$image = \Nette\Utils\Image::fromFile($SrcImage);
		$NewWidth = (int) $NewWidth;
		$NewHeight = (int) $NewHeight;
		$image->resize($NewWidth, $NewHeight);
		return $image->save($DestImage);
	}


	// pole s cetami k obrazkum
	public function handleImageFromK2($detailPhotos, $cat = NULL)
	{
		if (!$cat) {
			echo "zadejte kategorii obrazku pro import";
			die;
		}
		// obrazky - vlozeni obrazku do knihovny
		$finalImages = [];

		foreach ($detailPhotos as $type => $photo) {
//			echo $foto." ";
			$photoNew = str_replace("\\", "/", $photo);
			$pathToFile = WWW_DIR . $this->configService->getParam('k2DataDocDir') . $photoNew;

			if (!file_exists($pathToFile)) {
//				echo " skip   ";
				continue;
			}
			$md5 = md5_file($pathToFile)."_$photo";


			// kontrola zda obrazek jiz neni vlzen v knihovne
//			$imageExists = $db->query("SELECT id FROM image WHERE sourceImage = %s", $foto)->fetch();

//			$cc = [
//				'sourceImage' => $foto,
//			];
			$imageExists = FALSE;
			$_imageExists = $this->orm->libraryImage->findBySourceImage($md5);
			foreach ($_imageExists as $i) {
				$imageExists = $i;
//				echo " existuje ";
				break;
			}

			if ($imageExists && $imageExists->id) {
//				echo "jiz existuje: ".$imageExists->id;

				// TODO kontrola zda je to opravdu ten obrazek
//				$sizeImage = filesize($pathToFile);
//				$sizeImageA = filesize(WWW_DIR."/data/images/".$imageExists->filename);
//				if ($sizeImage != $sizeImageA) {
//					echo $sizeImage." ";
//
//					echo $sizeImageA." ";
//					echo "fotka se zmenila\n";//
//				}

				$finalImages[$type] = $imageExists->id;
			} else {
				$imageInfo = getimagesize($pathToFile);

				switch(strtolower($imageInfo['mime'])) {
					case 'image/png':
					case 'image/jpeg':
					case 'image/gif':

						$nameImage = substr($photoNew, strrpos($photoNew, '/') + 1);
						$file = array(
							'tmp_name' => $pathToFile,
							'name' => $nameImage,//array_pop(explode('/', $fotoNew)),
							'size' => 0,
							'error' => 0,
							'type' => $imageInfo['mime'],
						);
						$file = new \Nette\Http\FileUpload($file);

						$file = $this->add($file, $cat, TRUE, $photo, $md5);
						$finalImages[$type] = $file->id;

						break;
					default:
						echo "\n neprosel obrazek: $photoNew - ".strtolower($imageInfo['mime'])."\n";
						break;
				}

			}
		}

		return $finalImages;
	}


}


