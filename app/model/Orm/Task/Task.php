<?php declare(strict_types = 1);

namespace App\Model;

use Exception;
use Nette\DI\Container;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use SuperKoderi\InvoiceExporter;
use <PERSON>Kode<PERSON>\CreditNoteExporter;
use Throwable;
use function assert;
use function unserialize;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property string|null $collection
 * @property int|null $delay
 * @property string $data
 * @property DateTimeImmutable $createdTime {default now}
 * @property DateTimeImmutable $updatedTime {default now}
 * @property string $status {enum self::STATUS_*} {default self::STATUS_IN_QUEUE}
 * @property string|null $statusMessage
 * @property TaskProcessor $processor {virtual}
 * @property mixed $dataUnserialized {virtual}
 */
class Task extends Entity
{

	public const TYPE_INVOICE_EXPORT = 'inv_exp';
	public const TYPE_CREDIT_NOTE_EXPORT = 'credit_note_exp';
	public const TYPE_NEWSLETTER_CAMPAIGN_MAILER = 'newsletter_campaign_mailer';

	public const STATUS_IN_QUEUE = 'in_queue';
	public const STATUS_PROCESSING = 'processing';
	public const STATUS_DONE = 'done';
	public const STATUS_CANCELED = 'canceled';
	public const STATUS_ERROR = 'error';

	/**	@inject */
	public Container $container;

	protected array $type2Processor = [
		self::TYPE_INVOICE_EXPORT => InvoiceExporter::class,
		self::TYPE_CREDIT_NOTE_EXPORT => CreditNoteExporter::class,
		self::TYPE_NEWSLETTER_CAMPAIGN_MAILER => NewsletterCampaignMailer::class,
	];

	/**
	 * @throws Exception
	 */
	public function getterProcessor(): TaskProcessor
	{
		$class = $this->type2Processor[$this->type];
		try {
			$processor = $this->container->getByType($class);
			assert($processor instanceof TaskProcessor);
		} catch (Throwable) {
			throw new Exception('Task processor of type "' . $class . '" not in container');
		}

		return $processor;
	}

	public function getterDataUnserialized(): mixed
	{
		return unserialize($this->data);
	}

}
