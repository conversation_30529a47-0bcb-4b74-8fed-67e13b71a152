<?php

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\OneHasOne;
use SuperKoderi\hasConstsTrait;

/**
 * @property int $id {primary}
 * @property DateTimeImmutable $created {default now}
 * @property string $status {default 'new'}
 *
 *
 * RELATIONS
 * @property UserAnimal $animal {1:1 UserAnimal, isMain=true, oneSided=true}
 */
class ProductUserAnimalQueue extends \Nextras\Orm\Entity\Entity
{
	use hasConstsTrait;

	const STATUS_NEW = 'new';
	const STATUS_RUNNING = 'running';
	const STATUS_DONE = 'done';

}
