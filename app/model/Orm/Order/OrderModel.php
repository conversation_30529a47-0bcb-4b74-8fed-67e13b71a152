<?php

namespace App\Model;

use App\Console\Eshop\DpdOrderDeliveredCommand;
use <PERSON>bi\Connection;
use <PERSON>bi\DateTime;
use Dibi\Exception;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Entity\IEntity;
use SuperKoderi\Basket;
use SuperKoderi\BasketItem;
use SuperKoderi\ConfigService;
use SuperKoderi\Dpd\WsService;
use SuperKoderi\EasyMessages;
use SuperKoderi\LogicException;
use SuperKoderi\MoneyHelper;
use SuperKoderi\OrderMailService;
use SuperKoderi\TranslatorDB;
use Throwable;
use Tracy\Debugger;
use Tracy\Dumper;
use Tracy\ILogger;
use function count;
use function in_array;
use function preg_match;
use function round;
use function sprintf;
use function implode;

class OrderModel
{

	private Order|null $newOrderEntityCache = null;

	public function __construct(
		private readonly Orm $orm,
		private readonly ConfigService $configService,
		private readonly Connection $db,
		private readonly VoucherCodeModel $voucherCodeModel,
		private readonly WsService $dpdWsService,
		private readonly EasyMessages $easyMessages,
		private readonly TranslatorDB $translatorDB,
		private readonly VoucherCodeUsesModel $voucherCodeUsesModel,
		private readonly OrderMailService $orderMailService
	)
	{
	}

	public function saveHead(Order $order, ArrayHash $values): IEntity
	{
		foreach ($values as $key => $value) {
			$order->$key = $value;
		}

		return $this->orm->order->persistAndFlush($order);
	}

	public function getNewOrder(mixed $data, Basket $basket, Mutation|null $mutation = null): Order
	{
		if (!isset($this->newOrderEntityCache)) {
			$this->newOrderEntityCache = new Order();
		}

		$order = $this->newOrderEntityCache;
		$this->orm->order->attach($order);

		foreach ($order->items as $item) {
			$this->orm->orderItem->remove($item);
		}

		if (isset($mutation)) {
			$order->mutation = $mutation;
			// Step before moving invoiceData
			$order->invoiceData = [];
		}

		$order->status = Order::STATUS_NEW;
		$order->paymentType = $basket->getPaymentKey();
		$order->subPaymentType = $basket->getSubPaymentType();
		$order->transportType = $basket->getTransportKey();

		if ($order->transportType == Order::TRANSPORT_ZASILKOVNA) {
			if (isset($basket->getZasilkovna()['id'])) {
				$order->zasilkovnaId = $basket->getZasilkovna()['id'];
			}

			if (isset($basket->getZasilkovna()['infoText'])) {
				$order->transportInfoText = $basket->getZasilkovna()['infoText'];
			}
		}

		if ($order->transportType == Order::TRANSPORT_PPL_PARCEL) {
			if (isset($basket->getPplParcel()['code'])) {
				$order->pplParcelCode = $basket->getPplParcel()['code'];
			}
		}

		$intValues = ['enabledInChilds'];
		$boolValues = ['enabledInChilds'];
		$noSave = ['id', 'created'];


		foreach ($order->getMetadata()->getProperties() as $i) {
			$col = (string)$i->name;
			if (in_array($col, $noSave)) {
				continue;
			}
			if (isset($data[$col])) {
				if (in_array($col, $intValues)) {
					$data[$col] = (int)$data[$col];
				}
				$order->$col = $data[$col];
			} else {
				if (in_array($col, $boolValues)) {
					$order->$col = 0;
				}
			}
		}

		$order->enableHeurekaQuery = 1;
		if (isset($data['heurekaDisable']) && (bool)$data['heurekaDisable']) {
			$order->enableHeurekaQuery = 0;
		}

		$this->addItems($order, $basket);

		if ($order->mutation->langCode == 'sk' && ($order->paymentType == Order::PAYMENT_CASH || $order->paymentType == Order::PAYMENT_ONDELIVERY)) {
			$this->addNearest5CentsRounding($order);
		}

		return $order;
	}

	public function createNew(mixed $data, Basket $basket, Mutation $mutation): Order|null
	{
		try {
			// TODO: subPaymentType
			$order = $this->getNewOrder($data, $basket, $mutation);
			$this->orm->order->persist($order);
			$order->number = $this->generateOrderNo($order);

			$this->handleDecreaseProductStock($order);

			// online payment must be in status waiting - otherwise it is se to status new
			if ($order->paymentType == Order::PAYMENT_ONLINE) {
				$order->status = Order::STATUS_WAITING;
			}

			$this->orm->order->persist($order);

			$this->voucherCodeModel->deactivateCodes($basket, $order);

			return $order;
		} catch (Throwable $e) {
			bd($e->getMessage());
			$this->orm->order->getMapper()->rollback();
			Debugger::log($e, ILogger::EXCEPTION);

			return null;
		}
	}

	public function createNewFromSubscriptionOrder(SubscriptionOrder $subscriptionOrder): Order|null
	{
		try {
			// disable for now SUPCAL-561
			//$this->enoughStockForOrder($subscriptionOrder);

			if (empty($subscriptionOrder->subscriptionItems)) {
				throw new SubscriptionHasNoItemsException('Cannot process order, because subscription has no items.');
			}

			$subscription = $subscriptionOrder->subscription;
			$order = new Order();
			$order->user = $subscription->user;
			$order->email = $subscription->email;

			$order->lastname = $subscription->lastname;
			$order->firstname = $subscription->firstname;
			$order->phone = $subscription->phone;
			$order->street = $subscription->street;
			$order->city = $subscription->city;
			$order->zip = $subscription->zip;
			$order->state = $subscription->state;
			$order->dFirstname = $subscription->dFirstname;
			$order->dLastname = $subscription->dLastname;
			$order->dPhone = $subscription->dPhone;
			$order->dStreet = $subscription->dStreet;
			$order->dCity = $subscription->dCity;
			$order->dZip = $subscription->dZip;
			$order->dState = $subscription->dState;
			$order->transportType = $subscription->transportType;
			$order->zasilkovnaId = $subscription->zasilkovnaId;
			$order->pplParcelCode = $subscription->pplParcelCode;

			if ($subscriptionOrder->address instanceof SubscriptionOrderAddress) {
				$order->lastname = $subscriptionOrder->address->lastname;
				$order->firstname = $subscriptionOrder->address->firstname;
				$order->phone = $subscriptionOrder->address->phone;
				$order->street = $subscriptionOrder->address->street;
				$order->city = $subscriptionOrder->address->city;
				$order->zip = $subscriptionOrder->address->zip;
				$order->state = $subscriptionOrder->address->state;
				$order->dFirstname = $subscriptionOrder->address->dFirstname;
				$order->dLastname = $subscriptionOrder->address->dLastname;
				$order->dPhone = $subscriptionOrder->address->dPhone;
				$order->dStreet = $subscriptionOrder->address->dStreet;
				$order->dCity = $subscriptionOrder->address->dCity;
				$order->dZip = $subscriptionOrder->address->dZip;
				$order->transportType = $subscriptionOrder->address->transportType ?? $subscription->transportType;
				$order->zasilkovnaId = $subscriptionOrder->address->zasilkovnaId ?? $subscription->zasilkovnaId;
				$order->pplParcelCode = $subscriptionOrder->address->pplParcelCode ?? $subscription->pplParcelCode;
			}

			if ($order->transportType !== Order::TRANSPORT_PPL_PARCEL) {
				$order->pplParcelCode = null;
			}

			$order->mutation = $subscription->mutation;
			$order->invoiceData = [];
			$order->subscriptionOrder = $subscriptionOrder;

			$order->status = Order::STATUS_NEW;
			$order->paymentType = $subscription->paymentType;
			$order->subPaymentType = $subscription->subPaymentType;
			$order->enableHeurekaQuery = 0;

			foreach ($subscriptionOrder->subscriptionItems as $item) {
				$orderItem = new OrderItem();
				$orderItem->uniqueKey = BasketItem::PARSE_KEY_SUBSCRIPTION . $item->productVariant->id;
				$orderItem->variantId = $item->productVariant->id;
				$orderItem->order = $order;
				$orderItem->productId = $item->productVariant->product->id;
				$orderItem->name = $item->productVariant->nameAnchor;
				$price = $subscriptionOrder->sequence == 1 ? $item->productVariant->firstSubscriptionPrice : $item->productVariant->subscriptionPrice;
				$orderItem->unitPriceDPH = $price->priceDPH;
				$orderItem->unitPrice = MoneyHelper::getPriceWithoutDPH(
					$price->priceDPH,
					$item->productVariant->vat,
					customPrecision: MoneyHelper::unitPriceRoundingPositions($item->productVariant->product->mutation),
				);
				$orderItem->vat = $item->productVariant->vat;
				$orderItem->originalUnitPriceDPH = $item->productVariant->priceDPH;
				$orderItem->originalUnitPrice = MoneyHelper::getPriceWithoutDPH(
					$item->productVariant->priceDPH,
					$item->productVariant->vat,
					customPrecision: MoneyHelper::unitPriceRoundingPositions($item->productVariant->product->mutation),
				);
				$orderItem->amount = $item->amount;
				$orderItem->novikoId = $item->productVariant->novikoId;
				$orderItem->type = OrderItem::TYPE_SUBSCRIPTION;

				if (count($item->productVariant->presentsForSubscription) > 0) {
					foreach ($item->productVariant->presentsForSubscription as $present) {
						$this->addPresent($order, $present->productVariantGift, $orderItem->uniqueKey, $orderItem->amount, parentSubscriptionProduct: $item->productVariant);
					}
				}
			}

			foreach ($subscriptionOrder->activeOneTimeItems as $item) {
				$orderItem = new OrderItem();
				$orderItem->uniqueKey = BasketItem::PARSE_KEY_VARIANT . $item->productVariant->id;
				$orderItem->variantId = $item->productVariant->id;
				$orderItem->order = $order;
				$orderItem->productId = $item->productVariant->product->id;
				$orderItem->name = $item->productVariant->nameAnchor;
				$orderItem->unitPriceDPH = $item->productVariant->retailPriceVat;
				$orderItem->unitPrice = $item->productVariant->retailPrice;
				$orderItem->vat = $item->productVariant->vat;
				$orderItem->originalUnitPriceDPH = $item->productVariant->priceDPH;
				$orderItem->originalUnitPrice = MoneyHelper::getPriceWithoutDPH(
					$item->productVariant->priceDPH,
					$item->productVariant->vat,
					customPrecision: MoneyHelper::unitPriceRoundingPositions($item->productVariant->product->mutation),
				);
				$orderItem->amount = $item->amount;
				$orderItem->novikoId = $item->productVariant->novikoId;
				$orderItem->type = 'product';

				if (count($item->productVariant->presentsForSubscription) > 0) {
					foreach ($item->productVariant->presentsForSubscription as $present) {
						$this->addPresent($order, $present->productVariantGift, $orderItem->uniqueKey, $orderItem->amount, parentSubscriptionProduct: $item->productVariant);
					}
				}
			}

			$defaultVat = $order->mutation ? $order->mutation->vatDefault : $this->configService->getParam('shop', 'defaultVat');

			$payment = $this->orm->mutationPayments->getBy(['mutation' => $subscription->mutation, 'key' => $order->paymentType]);

			$paymentIsFree = $order->productsPriceDPH >= $payment->freeFrom;

			$paymentOrderItem = new OrderItem();
			$paymentOrderItem->order = $order;
			$paymentOrderItem->productId = 0;
			$paymentOrderItem->name = $payment->name;
			$paymentOrderItem->unitPriceDPH = $paymentIsFree ? 0 : $payment->priceDPH;
			$paymentOrderItem->unitPrice = $paymentIsFree ? 0 : MoneyHelper::getPriceWithoutDPH(
				$payment->priceDPH,
				$defaultVat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($order->mutation),
			);
			$paymentOrderItem->vat = $order->mutation->vatDefault;
			$paymentOrderItem->originalUnitPriceDPH = $payment->priceDPH;
			$paymentOrderItem->originalUnitPrice = MoneyHelper::getPriceWithoutDPH(
				$payment->priceDPH,
				$defaultVat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($order->mutation),
			);
			$paymentOrderItem->amount = 1;
			$paymentOrderItem->variantName = null;
			$paymentOrderItem->type = OrderItem::TYPE_PAYMENT;
			$paymentOrderItem->subType = $payment->key;


			$transport = $this->orm->mutationTransports->getBy(['mutation' => $order->mutation, 'key' => $order->transportType]);

			$lastSubscriptionOrder = null;
			if ($subscriptionOrder->sequence > 1) {
				$lastSubscriptionOrder = $this->orm->subscriptionOrder->getBy(['subscription' => $subscription, 'order!=' => null]);
			}

			$transportIsFree = $order->productsPriceDPH >= $transport->freeFrom;
			$transportOrderItem = new OrderItem();
			$transportOrderItem->order = $order;
			$transportOrderItem->productId = 0;
			$transportOrderItem->name = $lastSubscriptionOrder ? $lastSubscriptionOrder->order->transport->name : $subscriptionOrder->subscription->transportType;
			$transportOrderItem->unitPriceDPH = $transportIsFree ? 0 : $transport->priceDPH;
			$transportOrderItem->unitPrice = $transportIsFree ? 0 : MoneyHelper::getPriceWithoutDPH(
				$transport->priceDPH,
				$defaultVat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($order->mutation),
			);
			$transportOrderItem->vat = $defaultVat;
			$transportOrderItem->originalUnitPriceDPH = $transport->priceDPH;
			$transportOrderItem->originalUnitPrice = MoneyHelper::getPriceWithoutDPH($transport->priceDPH, $defaultVat);
			$transportOrderItem->amount = 1;
			$transportOrderItem->variantName = null;
			$transportOrderItem->type = OrderItem::TYPE_TRANSPORT;
			$transportOrderItem->subType = $order->transportType;
			$transportOrderItem->novikoId = $transport->novikoId;

			if ($subscriptionOrder->discountAmount !== null) {
				$voucherOrderItem = new OrderItem();
				$voucherOrderItem->order = $order;
				$voucherOrderItem->productId = 0;
				$voucherOrderItem->name = $subscriptionOrder->discountText;
				$voucherOrderItem->unitPriceDPH = -$subscriptionOrder->discountAmount;
				$unitPrice = MoneyHelper::getPriceWithoutDPH($voucherOrderItem->unitPriceDPH, $order->mutation->vatDefault);
				$voucherOrderItem->unitPrice = $unitPrice; // without minus - it is already used in MoneHelper above
				$voucherOrderItem->originalUnitPriceDPH = -$subscriptionOrder->discountAmount;
				$voucherOrderItem->originalUnitPrice = $unitPrice;
				$voucherOrderItem->vat = $order->mutation->vatDefault;
				$voucherOrderItem->amount = 1;
				$voucherOrderItem->variantName = null;
				$voucherOrderItem->type = OrderItem::TYPE_VOUCHER;
			}

			$this->orm->order->persist($order);

			$order->number = $this->generateOrderNo($order);

			if ($order->mutation->langCode == 'sk' && ($order->paymentType == Order::PAYMENT_CASH || $order->paymentType == Order::PAYMENT_ONDELIVERY)) {
				$this->addNearest5CentsRounding($order);
			}

			$this->handleDecreaseProductStock($order);

			$this->orm->order->persistAndFlush($order);

			return $order;
		} catch (SubscriptionOrderNotCreatedBecauseLowStockException $e) {
			bd($e->getMessage());
			$this->orm->order->getMapper()->rollback();
			throw $e;
		} catch (SubscriptionHasNoItemsException $e) {
			bd($e->getMessage());
			return null;
		} catch (Throwable $e) {
			die($e);
			bd($e->getMessage());
			$this->orm->order->getMapper()->rollback();
			Debugger::log($e, ILogger::EXCEPTION);

			return null;
		}
	}

	private function addItems(Order $order, Basket $basket)
	{
		$defaultVat = $order->mutation ? $order->mutation->vatDefault : $this->configService->getParam('shop', 'defaultVat');

//		foreach ($basket->getProducts() as $type => $basketItem) {
		$basketItems = $basket->getItems();
		if (isset($basketItems['errors']) && $basketItems['errors']) {
			$order->uniqueKeyErrors = $basketItems['errors'];
			unset($basketItems['errors']);
		}

		foreach ($basketItems as $uniqueKey => $basketItemsList) {
			$parentProduct = null;
			/** @var BasketItem $basketItem */
			foreach ($basketItemsList as $basketItem) {
				if (in_array($basketItem->type, [OrderItem::TYPE_PRODUCT, OrderItem::TYPE_SUBSCRIPTION])) {
					$parentProduct = $basketItem;
					$orderItem = new OrderItem();

					$orderItem = $this->prepareOrderItem($uniqueKey, $order, $basketItem, $orderItem);

					$orderItem->type = (($basketItem->variant->product->isSet) ? OrderItem::TYPE_SET : OrderItem::TYPE_PRODUCT);
					if ($basketItem->variant->voucher) {
						$orderItem->subType = 'voucher';
						$orderItem->variantId = $basketItem->variant->id;

					} else {
						$orderItem->subType = null;
						$orderItem->variantId = $basketItem->variant->id;
						if ($basketItem->variant->param1Value) {
							$orderItem->variantName = $basketItem->variant->param1Value->value;
						}
					}

					if ($basketItem->type === OrderItem::TYPE_SUBSCRIPTION) {
						if ($basket->isFirstSubscriptionOrder()) {
							$orderItem->unitPriceDPH = $basketItem->variant->firstSubscriptionPrice->priceDPH;
							$orderItem->unitPrice = MoneyHelper::getPriceWithoutDPH(
								$basketItem->variant->firstSubscriptionPrice->priceDPH,
								$basketItem->variant->vat,
								customPrecision: MoneyHelper::unitPriceRoundingPositions($basketItem->variant->product->mutation),
							);
						} else {
							$orderItem->unitPriceDPH = $basketItem->variant->subscriptionPrice->priceDPH;
							$orderItem->unitPrice = MoneyHelper::getPriceWithoutDPH(
								$basketItem->variant->subscriptionPrice->priceDPH,
								$basketItem->variant->vat,
								customPrecision: MoneyHelper::unitPriceRoundingPositions($basketItem->variant->product->mutation),
							);
						}
						$orderItem->type = OrderItem::TYPE_SUBSCRIPTION;
					}
				}


				if ($basketItem->type == OrderItem::TYPE_SAMPLE) {
					$orderItem = new OrderItem();
					$orderItem->uniqueKey = $uniqueKey;
					$orderItem->order = $order;
					$orderItem->productId = $basketItem->variant->product->id;
					$orderItem->variantId = $basketItem->variant->id;
					$orderItem->novikoId = $basketItem->variant->novikoId;
					$orderItem->name = $basketItem->variant->nameAnchor;
					$orderItem->unitPriceDPH = $basketItem->unitPriceDPH;
					$orderItem->unitPrice = $basketItem->unitPrice;
					$orderItem->vat = $basketItem->vat;
					$orderItem->originalUnitPriceDPH = $basketItem->originalUnitPriceDPH;
					$orderItem->originalUnitPrice = $basketItem->originalUnitPrice;
					$orderItem->amount = $basketItem->amount;
					$orderItem->type = $basketItem->type;

					$orderItem->subType = null;
					if ($basketItem->variant->param1Value) {
						$orderItem->variantName = $basketItem->variant->param1Value->value;
					}

				}


				if ($basketItem->type == OrderItem::TYPE_PRESENT) {
					$this->addPresent($order, $basketItem->variant, $uniqueKey, $basketItem->amount, $parentProduct);
				}


				if ($basketItem->type == OrderItem::TYPE_PRESENT_EXTRA) {
					$orderItem = new OrderItem();
					$orderItem->order = $order;
					$orderItem->productId = $basketItem->variant->product->id;
					$orderItem->variantId = $basketItem->variant->id;
					$orderItem->novikoId = $basketItem->variant->novikoId;
					$orderItem->name = $basketItem->variant->nameAnchor;
					$orderItem->unitPriceDPH = 0;
					$orderItem->unitPrice = 0;
					$orderItem->originalUnitPriceDPH = $basketItem->variant->priceDPH;
					$orderItem->originalUnitPrice = $basketItem->variant->price;
					$orderItem->amount = 1; // vždy jeden
					$orderItem->variantName = $basketItem->variant->variantName;
					$orderItem->type = OrderItem::TYPE_PRESENT_EXTRA;
					$orderItem->subType = null;
					$orderItem->parentId = null;
					$orderItem->vat = $basketItem->variant->vat;
				}
			}
		}

		$isFree = $basket->getProductsPriceDPH() >= $basket->getTransportFreeFrom();
		$transportIsFree = $isFree;
		$paymentIsFree = false;

		$this->recalculateTransportByOrderItems($basket, $order);
		if ((bool)$order->transportType) {
			$tr = $basket->getTransport();
			$orderItem = new OrderItem();
			$orderItem->order = $order;
			$orderItem->productId = 0;

			$orderItem->name = $tr['name'];

			$orderItem->unitPriceDPH = $transportIsFree ? 0 : $tr['priceDPH'];
			$orderItem->unitPrice = $transportIsFree ? 0 : MoneyHelper::getPriceWithoutDPH(
				$tr['priceDPH'],
				$defaultVat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($order->mutation),
			);
			$orderItem->vat = $defaultVat;
			$orderItem->originalUnitPriceDPH = $tr['priceDPH'];
			$orderItem->originalUnitPrice = MoneyHelper::getPriceWithoutDPH($tr['priceDPH'], $defaultVat);
			$orderItem->amount = 1;
			$orderItem->variantName = null;
			$orderItem->type = OrderItem::TYPE_TRANSPORT;
			$orderItem->subType = $basket->getTransportKey();

			if ($order->mutation) { // mutaci znam az pri finalnim ulozeni, driv neni novikoId potreba
				$transports = $order->mutation->transports->toCollection()->getBy(['key' => $order->transportType]);
				$orderItem->novikoId = $transports->novikoId;
			}
		}

		$paymentType = $order->paymentType ?? null;
		$payment = $basket->payments->{$paymentType} ?? null;

		if ($paymentType && $payment) {

			$orderItem = new OrderItem();
			$orderItem->order = $order;
			$orderItem->productId = 0;
			$orderItem->name = $basket->getPayment()['name'];
			$orderItem->unitPriceDPH = $paymentIsFree ? 0 : $basket->getPayment()['priceDPH'];
			$orderItem->unitPrice = $paymentIsFree ? 0 : MoneyHelper::getPriceWithoutDPH(
				$basket->getPayment()['priceDPH'],
				$defaultVat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($order->mutation),
			);
			$orderItem->vat = $order->mutation->vatDefault;
			$orderItem->originalUnitPriceDPH = $basket->getPayment()['priceDPH'];
			$orderItem->originalUnitPrice = MoneyHelper::getPriceWithoutDPH(
				$basket->getPayment()['priceDPH'],
				$defaultVat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($order->mutation),
			);
			$orderItem->amount = 1;
			$orderItem->variantName = null;
			$orderItem->type = OrderItem::TYPE_PAYMENT;
			$orderItem->subType = $basket->getPaymentKey();
		}

		$this->processVouchers($basket, $order, $transportIsFree, $defaultVat);
	}


	/**
	 * @param VoucherCode $voucherCode
	 * @param Basket      $basket
	 * @param bool        $transportIsFree
	 * @return bool
	 */
	private function areVoucherCriteriasAcomplished(VoucherCode $voucherCode, Basket $basket, bool $transportIsFree): bool
	{
		if ($voucherCode->voucher->application == Voucher::APPLICATION_SERVICE && $transportIsFree) {
			$this->easyMessages->send(EasyMessages::KEY_VOUCHER, VoucherCodeModel::VOUCHER_TRANSPORT_NO_EFFECT, EasyMessages::TYPE_OK);
			$basket->removeVoucher($voucherCode);

			return false;
		}

		if ($voucherCode->voucher->useOnceByUser)
		{
			if (!$basket->getUserModel()->getUser()->isLoggedIn()) {
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, VoucherCodeModel::ERROR_NOT_LOGGED, EasyMessages::TYPE_OK);
				$basket->removeVoucher($voucherCode);
				return false;
			}

			if ($this->voucherCodeUsesModel->usedByUser($voucherCode, $basket->getUserModel()->getUser())){
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, VoucherCodeModel::ERROR_USED, EasyMessages::TYPE_OK);
				$basket->removeVoucher($voucherCode);
				return false;
			}
		}

		if ($voucherCode->voucher->useCodeOnce) {
			// Voucher se smi pouzit pouze jednou
			if ($voucherCode->isUsed) {
				// voucher je jiz jednou pouzit, neni ho mozne pozuit vicekrat
				$this->easyMessages->send(
					EasyMessages::KEY_VOUCHER,
					VoucherCodeModel::ERROR_USED,
					EasyMessages::TYPE_OK
				);
				$basket->removeVoucher($voucherCode);
				return false;
			}
		}

		return true;
	}

	/**
	 * @param Basket $basket
	 * @param Order  $order
	 * @param bool   $transportIsFree
	 * @param float  $defaultVat
	 * @return void
	 */
	private function processVouchers(Basket $basket, Order $order, bool $transportIsFree, float $defaultVat): void
	{
		$voucherCodes = $basket->getVoucherCodes();

		if ($voucherCodes instanceof EmptyCollection) {
			return;
		}

		$voucherToItemMap = [];
		foreach ($voucherCodes as $voucherCode)
		{
			if (!$this->areVoucherCriteriasAcomplished($voucherCode, $basket, $transportIsFree)){
				continue;
			}

			$voucherCodeDiscount = $basket->getVoucherDiscount($voucherCode);

			$orderItem = new OrderItem();
			$orderItem->order = $order;
			$orderItem->productId = 0;
			$orderItem->name = $voucherCode->voucher->name;
			$orderItem->unitPriceDPH = -$voucherCodeDiscount;
			$orderItem->unitPrice = -MoneyHelper::getPriceWithoutDPH(
				$voucherCodeDiscount,
				$defaultVat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($order->mutation),
			);
			$orderItem->originalUnitPriceDPH = -$voucherCodeDiscount;
			$orderItem->originalUnitPrice = -MoneyHelper::getPriceWithoutDPH($voucherCodeDiscount, $defaultVat);
			$orderItem->vat = $defaultVat;
			$orderItem->amount = 1;
			$orderItem->variantName = null;
			$orderItem->type = OrderItem::TYPE_VOUCHER;
			$orderItem->subType = (string) $voucherCode->id;

			// Kupón na dárek...
			if ($voucherCode->voucher->kind === Voucher::KIND_GIFT && $voucherCode->voucher->gift !== null) {
				if (isset($voucherToItemMap[$voucherCode->voucher->id])) {
					$voucherToItemMap[$voucherCode->voucher->id]->amount += 1;
				} else {
					$voucherGift = $voucherCode->voucher->gift;

					$orderItem = new OrderItem();
					$orderItem->order = $order;
					$orderItem->productId = $voucherGift->product->id;
					$orderItem->variantId = $voucherGift->id;
					$orderItem->novikoId = $voucherGift->novikoId;
					$orderItem->name = $voucherGift->nameAnchor;
					$orderItem->unitPriceDPH = 0;
					$orderItem->unitPrice = 0;
					$orderItem->originalUnitPriceDPH = $voucherGift->priceDPH;
					$orderItem->originalUnitPrice = $voucherGift->price;
					$orderItem->amount = 1;
					$orderItem->variantName = $voucherGift->variantName;
					$orderItem->type = OrderItem::TYPE_PRESENT_EXTRA;
					$orderItem->subType = null;
					$orderItem->vat = $voucherGift->vat;

					$voucherToItemMap[$voucherCode->voucher->id] = $orderItem;
				}
			}
		}
	}

	private function addNearest5CentsRounding(Order $order): void
	{
		$difference = round(MoneyHelper::roundToNearest5Cents($order->totalPriceDPH) - $order->totalPriceDPH, 2);

		$orderItem = new OrderItem();
		$orderItem->order = $order;
		$orderItem->productId = 0;
		$this->translatorDB->reInit($order->mutation->langCode);
		$orderItem->name = $this->translatorDB->translate('_pdf_cash_payment_rounding');

		$orderItem->unitPriceDPH = $difference;
		$orderItem->unitPrice = $difference;
		$orderItem->vat = 0;
		$orderItem->originalUnitPriceDPH = $difference;
		$orderItem->originalUnitPrice = $difference;
		$orderItem->amount = 1;
		//$orderItem->novikoId = $basketItem->variant->novikoId;

		$orderItem->type = OrderItem::TYPE_CASH_ROUNDING;
	}

	public function addReviewToQueue($orderId, $email)
	{

		// TODO VOJTA REF.
		$sentTime = new DateTime();
		$sentTime->add(new \DateInterval('P30D'));

		$data = array(
			'order_id' => $orderId,
			'sent' => 0,
			'created' => new DateTime(),
			'hash' => substr(sha1($email . $orderId), 10, 30),
			'sent_time' => $sentTime
		);
		return $this->db->insert('order_review_queue', $data)
			->on('DUPLICATE KEY UPDATE %a ', $data)
			->execute();

	}

	/**
	 * @throws LogicException
	 * @throws Exception
	 */
	private function generateNo(Order $order, string $type): string
	{
		switch ($type) {
			case 'order':
				$col = 'number';
				$colDate = 'created';
				$add = Mutation::NUMBER_PREFIX_ADD_ORDER;

				break;
			case 'invoice':
				$col = 'invoiceNumber';
				$colDate = 'invoiceDate';
				$add = Mutation::NUMBER_PREFIX_ADD_INVOICE;

				break;
			default:
				throw new LogicException(sprintf('Unknown type "%s" for generate number.', $type));
		}

		$number = $this->db->query("SELECT MAX(%n) FROM %n WHERE mutationId = %i AND YEAR(".$colDate.") = YEAR(NOW())", $col, 'order', $order->mutation->id)->fetchSingle();
		$prefix = empty($number) ? str_pad($order->mutation->numberPrefix + $add, 2, "0", STR_PAD_LEFT) . date('y') : ''; // empty = nova rada

		return $prefix . str_pad($number + 1, 6, "0", STR_PAD_LEFT);
	}

	/**
	 * @throws LogicException
	 * @throws Exception
	 */
	public function generateOrderNo(Order $order): string
	{
		return $this->generateNo($order, 'order');
	}

	/**
	 * @param Order $order
	 * @return string
	 * @throws LogicException
	 * @throws Exception
	 */
	protected function generateInvoiceNo(Order $order): string
	{
		return $this->generateNo($order, 'invoice');
	}

	/**
	 * Save response from online payment
	 */
	public function savePaymentCreation(Order $order, mixed $response, bool $flush = true, ?bool $retriedPayment = false): void
	{
		if ($retriedPayment || $order->paymentId !== null) {
			$log = new OrderLog();
			$log->orderId = $order->id;
			$log->type = OrderLog::TYPE_INFO;
			$log->message = 'Created new payment (ID: ' . $response['id'] . '), old payment id: '	. $order->paymentId . ' with status: ' . $order->paymentStatus;

			$this->orm->orderLog->persist($log);
			$order->paymentHistory .= $order->paymentId . ':' . $order->paymentStatus . '|';
		}

		if (!$order->isPayed) {
			$order->paymentId = $response['id'];
			$order->paymentJson = Json::encode($response);
			$order->paymentStatus = $response['state'];
		}

		// log payment response
		$log = new OrderLog();
		$log->orderId = $order->id;
		$log->type = OrderLog::TYPE_INFO;
		$log->message = Json::encode($response);
		$this->orm->orderLog->persist($log);

		if ($flush || $retriedPayment || $order->subscriptionOrder instanceof SubscriptionOrder) {
			$this->orm->order->persistAndFlush($order);
		} else {
			$this->orm->order->persist($order);
		}
	}

	protected function checkDate($date)
	{
		$timeHandler = new \SuperKoderi\TimeHandler();

		$day = $date->format("D");
		$dayF = $date->format("d-m");
		$month = $date->format("m");
		$year = $date->format("Y");

		if ($day == "Sun") {
		} elseif (in_array($dayF, $timeHandler->holidays)) {
		} elseif (($month == 3 || $month == 4) && $timeHandler->_matchEasterDate($year, $dayF)) { // velikonoce - pondeli nebo patek
		} else {
			return TRUE;
		}

		return FALSE;
	}

	protected function getDateFromString($dateString)
	{
		$tmp = explode(".", $dateString);
		$date = new \DateTime();
		$date->setDate((int)$tmp[0], (int)$tmp[1], (int)$tmp[2]);
		$date->setTime((int)$tmp[3], 0, 0);
		return $date;
	}

	/**
	 * U vsech produktu z Noviko skladu se ponizi pocet podle objednavky, u CZ a SK mutace, vcetne mnozstevnich variant
	 */
	public function handleDecreaseProductStock(Order $order): void
	{
		foreach ($order->items as $item) {
			if ($item->novikoId && !in_array($item->type, [OrderItem::TYPE_PAYMENT, OrderItem::TYPE_TRANSPORT, OrderItem::TYPE_CASH_ROUNDING])) {
				$stock = max($item->variant->stock - $item->amount, 0);

				// nactu vsechny CZ a SK varianty (maji spolecny sklad) vcetne mnozstevnich variant
				$productVariants = $this->orm->productVariant->findBy([
					'novikoId' => $item->novikoId,
					'product->mutation' => [Mutation::ID_CS_DEFAULT, Mutation::ID_SK_ESHOP],
				]);
				$productVariant = null;
				foreach ($productVariants as $productVariant) { // mnozstevni variantu
					$productVariant->stock = $productVariant->isQuantityDiscount ? ProductVariant::getQdStock($stock, $productVariant->qdAmount) : $stock;
					$this->orm->productVariant->persist($productVariant);
				}
			}
		}
	}

	public function markAsDone(Order $order, User $user = null)
	{
		$order->status = Order::STATUS_DONE;
		$order->doneDate = new DateTimeImmutable();
		$this->orm->order->persistAndFlush($order);
	}

	public function markInvoiceDate(Order $order): Order
	{
		$order->invoiceDate = new DateTimeImmutable();
		$this->orm->order->persistAndFlush($order);

		return $order;
	}

	/**
	 * @param Order $order
	 * @param bool $withPersist
	 * @return Order
	 * @throws LogicException
	 * @throws Exception
	 */
	public function doInvoice(Order $order, bool $withPersist = true)
	{
		if ($order->invoiceNumber) {
			throw new LogicException(sprintf('Order ID %d has already invoice number %s', $order->id, $order->invoiceNumber));
		}

		if (empty($order->invoiceDate)) {
			$order->invoiceDate = new DateTimeImmutable(); // prvne ulozime cas, podle ktereho se pak generuje invoiceNumber
			$this->orm->order->persistAndFlush($order);
		}

		$this->logInvoiceNumber($order->number, $order->invoiceNumber);

		$order->invoiceNumber = $this->generateInvoiceNo($order);

		$this->logInvoiceNumber($order->number, $order->invoiceNumber);

		if ($withPersist) {
			$this->orm->order->persistAndFlush($order);
		}
		return $order;
	}

	private function logInvoiceNumber(string $orderNumber, ?string $invoiceNumber = null): void
	{
		$filename = LOG_DIR . '/invoicenumber.txt';
		$timestamp = date('Y-m-d H:i:s');
		$logEntry = "[{$timestamp}] -> {$orderNumber} -{$invoiceNumber}" . PHP_EOL;

		file_put_contents($filename, $logEntry, FILE_APPEND);
	}

	/**
	 * @param Order $order
	 * @param string|null $cancelReason
	 * @return IEntity|Order
	 * @throws LogicException
	 */
	public function storno(Order $order, string $cancelReason = null, ?bool $sendMail = false, ?bool $stornoByCommand = false)
	{
		if ($order->canceled) {
			throw new LogicException(sprintf('Order ID %d has already been canceled', $order->id));
		}

		$order->status = Order::STATUS_CANCEL;
		$order->canceled = new DateTimeImmutable();

		$log = new OrderLog();
		$log->orderId = $order->id;
		$log->type = OrderLog::TYPE_INFO;
		$log->message = 'Order canceled. Reason: ' . $cancelReason . ($stornoByCommand ? ' (by command)' : '');
		$this->orm->orderLog->persist($log);

		if (!$order->cancelReason && $cancelReason) { // duvod uz muze byt urcen nekde predtim
			$order->cancelReason = $cancelReason;
		}

		if (!$order->cancelReason) {
			$order->cancelReason = Order::CANCEL_REASON_DEFAULT;
		}

		if ($sendMail && $order->mailStorno === null) {
			$this->orderMailService->orderStorno($order);
		}

		return $this->orm->persistAndFlush($order);
	}

	/**
	 * XML <last_scan_code> (string)
	 * =====================================
	 * 	13 		package delivered
	 *	14		package was not delivered
	 * 	18 		package picked-up, but it does not work much, thus the next check of delivery_date
	 *	23 		Delivered to the Pickup point
	 *	DODEY	Doručeno (vyzvednuto příjemcem)
	 * 	DOCRA	Příjemce si nevyzvedl zásilku, úložní doba vypršela
	 *
	 * @throws LogicException
	 */
	public function checkDpdIsDelivered(Order $order, bool $isVerboseLog = false, array|null &$undeliveredPackages = null): bool
	{
		if (empty($order->parcelNumber) || ! is_array($order->parcelNumber['baliky'])) {
			throw new LogicException(sprintf('Order %s does not have a parcel number', $order->number));
		}

		$undelivered = []; // obj muze mit vice baliku = vyrizena obj jen kdyz jsou doruceny vsechny baliky

		foreach ($order->parcelNumber['baliky'] as $parcel) {
			if (empty($parcel['idBalikExt'])) {
				throw new LogicException(sprintf('Unknown parcel number: %s', $parcel['idBalikExt']));
			}
			$result = $this->dpdWsService->getParcelStatus($parcel['idBalikExt'], $order->mutation);

			if ($isVerboseLog) {
				Debugger::log($result, DpdOrderDeliveredCommand::DEBUG_LOG_LEVEL);
			}

			if ($result === null) {
				$undelivered[] = 1;
			} else {

				$isDelivered = false;
				$hasLastScanCode = preg_match('/a\:last\_scan\_code\>([A-Z0-9]+)\<\/a\:last\_scan\_code/', $result, $matchesLastScanCode);

				if ($hasLastScanCode !== false) {
					$lastScanCode = $matchesLastScanCode[1] ?? null;
					$isDelivered = isset($lastScanCode) && in_array($lastScanCode, ['13', '18', '23', 'DODEY'], true);

					if ($lastScanCode === '14') {
						$additionalScanCode = preg_match('/a\:additional\_scan\_code\>([A-Z0-9]+)\<\/a\:additional\_scan\_code/', $result, $matchesLastAdditionalScanCode);
						$additionalScanText = preg_match('/a\:additional\_scan\_text\>(.*?)\<\/a\:additional\_scan\_text/', $result, $matchesLastAdditionalScanText);

						if ($undeliveredPackages !== null && ((bool) $additionalScanCode || (bool) $additionalScanText)) {
							$undeliveredPackages[$order->mutation->langCode][$order->id][] = [
								'id' => $parcel['idBalik'],
								'extId' => $parcel['idBalikExt'],
								'code' => $matchesLastAdditionalScanCode[1] ?? 'undefined',
								'text' => $matchesLastAdditionalScanText[1] ?? 'undefined',
							];
						}
					}
				}

				if ($isVerboseLog) {
					Debugger::log($isDelivered, DpdOrderDeliveredCommand::DEBUG_LOG_LEVEL);
				}
				if ( ! $isDelivered) {
					$isDelivered = (int)preg_match('/a\:delivery\_date\>[0-9]+\<\/a\:delivery\_date/', $result, $matches) > 0; //delivered package has delivery_date set
				}

				$isReturned = false; // (pokud je servisní kód zásilky 298, 299, 300 nebo 301, balíček byl úspěšně vrácen zpět odesilateli).
				preg_match('#service_code>([\d]{3})<#', $result, $matchesServiceCodes);

				if (isset($matchesServiceCodes[1])) {
					$isReturned = in_array($matchesServiceCodes[1], ['298', '299', '300', '301'], true);
				}

				if ( ! $isDelivered || $isReturned) {
					$undelivered[] = 1;
				}
			}
		}

		if ($isVerboseLog) {
			Debugger::log(Dumper::toText($undelivered), DpdOrderDeliveredCommand::DEBUG_LOG_LEVEL);
		}

		if (count($undelivered) === 0) {
			$this->markAsDone($order);
			if (isset($undeliveredPackages[$order->mutation->langCode][$order->id])) {
				unset($undeliveredPackages[$order->mutation->langCode][$order->id]);
			}

			if ($isVerboseLog) {
				Debugger::log(sprintf('markAsDoneEnd | ID: %s | Status: %s | donaDate: %s', $order->id, $order->status, isset($order->doneDate) ? $order->doneDate->format(\SuperKoderi\Utils\DateTime::FORMAT_DEFAULT) : ''), DpdOrderDeliveredCommand::DEBUG_LOG_LEVEL);
			}

			return true;
		}

		return false;
	}

	public function recalculateTransportByOrderItems(Basket $basket, Order $order): Basket
	{
		$unsetTKeys = [];
		foreach ($basket->transports as $tKey => $transport) {
			if (!empty($transport['configData'])) {
				$weightPriceLimits = new ArrayHash();

				try {
					// Example DB config
					// [{"maxWeight":4500, "priceDPH":4.5, "priceDPHCod":6.5}, {"maxWeight":8500, "priceDPH":6.5, "priceDPHCod":8.5}]
//					$weightPriceLimits = Json::decode($transport['weightToPrice']);

					$weightPriceLimits = $transport->configData;
				} catch (\Exception $e) {
					// IF exception -> invalid json -> invalid config
					Debugger::log($e);
					// UNSET this transport
					$unsetTKeys[] = $tKey;
					continue;
				}

				// IF here are some price limits
				if (!empty($weightPriceLimits)) {
					// SORT them by weight
					$weightPriceLimits = (array) $weightPriceLimits;
					usort($weightPriceLimits, function($a, $b) { return $a->maxWeight - $b->maxWeight; });
					$weightPriceLimits = ArrayHash::from($weightPriceLimits);

					// CHECK if some combination is valid
					$unsetTransport = true;
					$wplToBeUsed = new ArrayHash();
					foreach ($weightPriceLimits as $wpl) {
						if ($order->weight < $wpl->maxWeight) {
							$unsetTransport = false;
							$wplToBeUsed = $wpl;
							break;
						}
					}
					// IF NOT transport with correct weight found -> unset transport
					// IF FOUND -> set new price levels
					if ($unsetTransport) {
						// unset only if config is set but none of options is the right
						if ((array)$weightPriceLimits != (array)(new ArrayHash())) {
							$unsetTKeys[] = $tKey;
						}
					} else {
						// CHECK if config data are right
						// OTHERWISE unset transport

						if (isset($wplToBeUsed->priceDPH) && isset($wplToBeUsed->priceDPHCod)) {
							$basket->transports[$tKey]['priceDPH'] = $wplToBeUsed->priceDPH;
							$basket->transports[$tKey]['priceDPHCod'] = $wplToBeUsed->priceDPHCod;
							if (isset($wplToBeUsed->annotation)) {
								$tData = [];
								$tData['name'] = $transport->name;
								$tData['annotation'] = $wplToBeUsed->annotation;
								$tData['maxWeight'] = $wplToBeUsed->maxWeight;
								$order->transportData = $tData;
							}
						} else {
							$unsetTKeys[] = $tKey;
						}

					}
				}

			}
		}

		foreach ($unsetTKeys as $key) {
			unset($basket->transports[$key]);
		}

		if (!isset($basket->transports[$basket->getTransportKey()])) {
			$basket->setTransport((string)array_key_first((array)$basket->transports));
		}

		return $basket;
	}

	public function getFinancialStatisticsByMutation($filter, $user): array
	{
		bd('STATISTICS');
		$statistics = [];
		$statistics = $this->addPartialSumToStatistics($this->orm->order->getOrderStatisticsByMutation($filter, $user), $statistics);
		$statistics = $this->addPartialSumToStatistics($this->orm->order->getCreditStatisticsByMutation($filter, $user), $statistics);
		$statistics = $this->addOrderCountToStatistics($this->orm->order->getOrderCountByMutation($filter, $user), $statistics, 'invoices');
		$statistics = $this->addOrderCountToStatistics($this->orm->order->getCreditCountByMutation($filter, $user), $statistics, 'creditNotes');
		$statistics = $this->roundStatistics($statistics);
		bd($statistics);
		return $statistics;
	}

	private function addPartialSumToStatistics(array $statisticsByMutation, array $statistics): array
	{
		if (!$statisticsByMutation) {
			// todo nahlasit chybu
			return $statistics;
		}

		foreach ($statisticsByMutation as $mutationStatistics) {
			$mutation = $this->orm->mutation->getBy([
				'id' => $mutationStatistics->mutationId,
			]);

			$mutationCode = $mutation->langCode;

			if (!isset($statistics[$mutationCode])) {
				$statistics[$mutationCode] = [];
				$statistics[$mutationCode]['summary'] = [];


				if (!$mutation) {
					// todo nahlasit chybu
					continue;
				}

				$statistics[$mutationCode]['mutation'] = $mutation;
			}

			if (!isset($statistics[$mutationCode]['summary'][$mutationStatistics->vat])) {
				$statistics[$mutationCode]['summary'][$mutationStatistics->vat] = [];
				$statistics[$mutationCode]['summary'][$mutationStatistics->vat]['priceDPH'] = 0;
				$statistics[$mutationCode]['summary'][$mutationStatistics->vat]['price'] = 0;
				$statistics[$mutationCode]['summary'][$mutationStatistics->vat]['DPH'] = 0;
			}

			$statistics[$mutationCode]['summary'][$mutationStatistics->vat]['priceDPH'] += $mutationStatistics->sumPriceDPH;
			$statistics[$mutationCode]['summary'][$mutationStatistics->vat]['price'] += $mutationStatistics->sumPrice;
			$statistics[$mutationCode]['summary'][$mutationStatistics->vat]['DPH'] += $mutationStatistics->sumPriceDPH - $mutationStatistics->sumPrice;
		}

		return $statistics;
	}

	private function roundStatistics(array $statistics)
	{
		// float is not the best -> rounding
		foreach ($statistics as $mKey => $statisticMutation) {
			foreach ($statistics[$mKey]['summary'] as $key => $statisticsSummary) {
				foreach ($statistics[$mKey]['summary'][$key] as $subKey => $item) {
					$statistics[$mKey]['summary'][$key][$subKey] = round($item, 4);
				}
			}
		}

		return $statistics;
	}

	private function addOrderCountToStatistics(array $countByMutation, array $statistics, string $keyToBeSet): array
	{
		foreach ($countByMutation as $mutationCount) {
			$mutation = $this->orm->mutation->getBy([
				'id' => $mutationCount->mutationId,
			]);

			$mutationCode = $mutation->langCode;
			$statistics[$mutationCode][$keyToBeSet] = $mutationCount->orderCount;
		}

		return $statistics;
	}

	private function enoughStockForOrder(SubscriptionOrder $subscriptionOrder): bool
	{
		$stockCheck = [];
		foreach ($subscriptionOrder->subscriptionItems as $item) {
			if (!isset($stockCheck[$item->productVariant->id])) {
				$stockCheck[$item->productVariant->id] = $item->amount;
			} else {
				$stockCheck[$item->productVariant->id] += $item->amount;
			}
		}
		foreach ($subscriptionOrder->activeOneTimeItems as $item) {
			if (!isset($stockCheck[$item->productVariant->id])) {
				$stockCheck[$item->productVariant->id] = $item->amount;
			} else {
				$stockCheck[$item->productVariant->id] += $item->amount;
			}
		}

		// check stock
		$lowStocks = [];
		foreach ($stockCheck as $productVariantId => $amount) {
			$productVariant = $this->orm->productVariant->getById($productVariantId);
			if ($productVariant->stock < $amount) {
				$lowStocks[] = $productVariantId;
			}
		}
		if (!empty($lowStocks)) {
			throw new SubscriptionOrderNotCreatedBecauseLowStockException('Cannot create order because of low stock on product variant ' . implode(', ', $lowStocks));
		}

		return true;
	}

	/**
	 * @param string $uniqueKey
	 * @param Order $order
	 * @param BasketItem $basketItem
	 * @param OrderItem $orderItem
	 * @return OrderItem
	 */
	private function prepareOrderItem(string $uniqueKey, Order $order, BasketItem $basketItem, OrderItem $orderItem): OrderItem
	{
		$orderItem->uniqueKey = $uniqueKey;
		$orderItem->order = $order;
		$orderItem->productId = $basketItem->variant->product->id;
		$orderItem->name = $basketItem->variant->nameAnchor;
		$orderItem->vat = $basketItem->vat;
		$orderItem->originalUnitPriceDPH = $basketItem->originalUnitPriceDPH;
		$orderItem->originalUnitPrice = $basketItem->originalUnitPrice;
		$orderItem->amount = $basketItem->amount;
		$orderItem->novikoId = $basketItem->variant->novikoId;
		$orderItem->unitPriceDPH = $basketItem->unitPriceDPH;
		$orderItem->unitPrice = $basketItem->unitPrice;
		$orderItem->sort = $basketItem->sort;

		if ($basketItem->variant->qdAmount) {
			$orderItem->novikoQdAmount = $basketItem->variant->qdAmount;
		}

		return $orderItem;
	}

	private function addPresent(Order $order, ProductVariant $productVariant, string $uniqueKey, ?int $amount = 1, ?BasketItem $parentProduct = null, ?ProductVariant $parentSubscriptionProduct = null): void
	{
		$orderItem = new OrderItem();
		$orderItem->order = $order;
		$orderItem->productId = $productVariant->product->id;
		$orderItem->variantId = $productVariant->id;
		$orderItem->novikoId = $productVariant->novikoId;
		$orderItem->name = $productVariant->nameAnchor;
		$orderItem->unitPriceDPH = 0;
		$orderItem->unitPrice = 0;
		$orderItem->originalUnitPriceDPH = $productVariant->priceDPH;
		$orderItem->originalUnitPrice = $productVariant->price;
		$orderItem->amount = $amount;
		$orderItem->variantName = $productVariant->variantName;
		$orderItem->type = OrderItem::TYPE_PRESENT;
		$orderItem->subType = null;
		$orderItem->vat = 0;
		if ($parentProduct) {
			$orderItem->vat = $parentProduct?->vat ?? 0;
		}
		if ($parentSubscriptionProduct) {
			$orderItem->vat = $parentSubscriptionProduct?->vat ?? 0;
		}

		$orderItem->parentId = $uniqueKey;
	}
}
