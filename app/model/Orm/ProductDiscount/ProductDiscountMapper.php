<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\Orm\TraitsMapper\HasCamelCase;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ProductDiscountMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'product_discount';

	public function findByVariantAndUserGroup(ProductVariant $variant, int $userGroupId): ICollection
	{
		$builder = $this->builder()->select('pd.*')->from('product_discount', 'pd')
			->joinInner('[product_variant] AS pv', '[pd.variantId] = [pv.id]')
			//->joinInner('[user_group] AS ug', '[pd.userGroupId] = [ug.id]')
			->andWhere('pv.id = %i', $variant->id)
			->andWhere('pd.userGroupId = %i', $userGroupId)
			->andWhere('pd.from <= %dt', new DateTimeImmutable())
			->andWhere('pd.to >= %dt', new DateTimeImmutable());
		return $this->toCollection($builder);
	}

}
