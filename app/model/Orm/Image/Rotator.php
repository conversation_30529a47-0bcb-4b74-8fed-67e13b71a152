<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Utils\Image;

class Rotator
{

	public const NORMAL_ORIENTATION = 1;

	public function normalizeRotation(string $pathToFile, string $extension): void
	{
		// funkce exif_imagetype funguje pouze pro JPEG a TIFF
		$validTypes = [IMAGETYPE_JPEG, IMAGETYPE_TIFF_II, IMAGETYPE_TIFF_MM];

		$type = exif_imagetype($pathToFile);
		if (!in_array($type, $validTypes, true)) {
			return;
		}

		$exifHeaders = exif_read_data($pathToFile);

		if ($exifHeaders !== false
			&& isset($exifHeaders['MimeType'])
			&& isset($exifHeaders['Orientation'])
			&& $exifHeaders['Orientation'] !== self::NORMAL_ORIENTATION) {
			$orientation = $exifHeaders['Orientation'];
			$modify = false;

			$imageForRotation = Image::fromFile($pathToFile);
			if (($rotation = $this->getImageRotation($orientation)) !== null) {
				$imageForRotation->rotate($rotation, 0);
				$modify = true;
			}

			if (($flipMode = $this->getImageFlip($orientation)) !== null) {
				$imageForRotation->flip($flipMode);
				$modify = true;
			}

			if ($modify) {
				$imageForRotation->save($pathToFile, null, Image::detectTypeFromFile($pathToFile));
			}
		}
	}

	public function getImageFlip(int $orientation): int|null
	{
		return match ($orientation) {
			2 => IMG_FLIP_HORIZONTAL,
			4, 5, 7 => IMG_FLIP_VERTICAL,
			default => null,
		};
	}

	public function getImageRotation(int $orientation): int|null
	{
		return match ($orientation) {
			3, 4 => 180,
			6, 7 => 270,
			5, 8 => 90,
			default => null,
		};
	}

}
