<?php

namespace App\Model;


/**
 * @property int $id {primary}
 * @property int $isPublic
 * @property int|null $isTesting {default 0}
 * @property string $key
 * @property string $name
 * @property float|null $priceDPH
 * @property float|null $freeFrom
 * @property float|null $freeFromLogIn
 * @property float|null $priceDPHCod
 * @property float|null $freeFromCod
 * @property string|null $heurekaId
 * @property string|null $tooltip {default null}
 * @property int $sort {default 0}
 * @property int|null $novikoId
 * @property array $configData {wrapper JsonContainer}
 * @property string|null $trackingLinkBase
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$transports}
 *
 * VIRTUAL
 *
 */
class MutationTransports extends \Nextras\Orm\Entity\Entity
{

	const ALLOWED_FOR_SUBSCRIPTION = [
		Order::TRANSPORT_DPD,
		Order::TRANSPORT_PPL,
		Order::TRANSPORT_PPL_PARCEL,
	];

	const ALLOWED_FOR_OVERSIZE = [
		Order::TRANSPORT_PPL,
		Order::TRANSPORT_DPD,
	];

//	public const DELIVERY_PRICE_AFTER_BREAKDATE = [
//		Mutation::ID_CS_DEFAULT => 699,
//		Mutation::ID_SK_ESHOP => 35,
//	];
//
//	public const BREAK_DATE_FOR_DELIVERY_PRICE = '2024-09-02 00:00:00';

}
