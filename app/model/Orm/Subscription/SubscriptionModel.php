<?php declare(strict_types = 1);

namespace App\Model;

use Exception;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;
use SuperKoderi\ConfigService;
use SuperKoderi\GoPay\IClientFactory;
use SuperKoderi\Noviko\Order\OrderService as NovikoOrderService;
use SuperKoderi\OrderMailService;
use SuperKoderi\SubscriptionMailService;
use Throwable;
use function in_array;
use function sprintf;

readonly class SubscriptionModel
{

	public function __construct(
		private Orm $orm,
		private SubscriptionLogModel $subscriptionLogModel,
		private SubscriptionOrderLogModel $subscriptionOrderLogModel,
		private SubscriptionOrderModel $subscriptionOrderModel,
		private OrderModel $orderModel,
		private SubscriptionPaymentModel $subscriptionPaymentModel,
		private SubscriptionMailService $subscriptionMailService,
		private IClientFactory $gopayClientFactory,
		private OrderMailService $orderMailService,
		private ConfigService $configService
	)
	{
	}

	public function processSubscriptionFromOrder(Order $order, string $subscriptionHash): Subscription
	{
		// user probably does not have subscription and we need to create it
		$subscription = $this->orm->subscription->getBy(['hash' => $subscriptionHash]);
		if (!$subscription instanceof Subscription) {
			$subscription = $this->createSubscription('New Subscription', 4, Subscription::STATUS_ACTIVE, $order->user, hash: $subscriptionHash);
		}

		$subscription->status = Subscription::STATUS_ACTIVE;
		$subscription->paymentType = $order->paymentType;
		$subscription->subPaymentType = $order->subPaymentType;
		$subscription->transportType = $order->transportType;
		$subscription->zasilkovnaId = $order->zasilkovnaId;
		$subscription->pplParcelCode = $order->pplParcelCode;

		if ($subscription->openSubscriptionOrder instanceof SubscriptionOrder) {
			throw new SubscriptionOrderNotClosedException();
		}

		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, 'Subscription interval is ' . $subscription->interval);

		// we are creating subscription order and set it as processed
		$subscriptionOrder = $this->subscriptionOrderModel->createFirstSubscriptionFromOrder($subscription, $order);
		$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_PROCESSED);

		$newOrderStatus = SubscriptionOrder::STATUS_OPEN;
		if ($order->paymentType === Order::PAYMENT_ONLINE) {
			$subscription->status = Subscription::STATUS_PENDING;
			$newOrderStatus = SubscriptionOrder::STATUS_NEW;
		}

		// we need to create another subscription order status new for setting next order
		$this->subscriptionOrderModel->createNext($subscription, $newOrderStatus);

		return $subscription;
	}

	public function createSubscription(string $name, int $interval, string|null $status = Subscription::STATUS_DRAFT, User|null $user = null, string|null $hash = null, array $userAddressData = []): Subscription
	{
		if ($interval < 2) {
			$interval = 4;
		}
		if ($hash) {
			$subscription = $this->orm->subscription->getBy(['hash' => $hash, 'user' => $user]);
			$subscription->name = $name;
			$subscription->interval = $interval;
			$subscription->updated = new DateTimeImmutable();

			$this->orm->subscription->persistAndFlush($subscription);
			if ($subscription instanceof Subscription) {
				return $subscription;
			}
		}

		$subscription = new Subscription();
		$subscription->name = $name;
		$subscription->interval = $interval;
		$subscription->hash = $hash ?: $this->getUniqueHash();
		$subscription->status = Subscription::STATUS_DRAFT;
		if ($user instanceof User) {
			$subscription->user = $user;
		}

		if ($status) {
			$subscription->status = $status;
		}

		if (!empty($userAddressData)) {
			$this->processAddressData($subscription, $userAddressData);
		}

		$this->orm->subscription->persistAndFlush($subscription);

		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription %s created, name: %s.', $subscription->id, $subscription->name));

		return $subscription;
	}

	public function sendSubscriptionCreationMail(Subscription $subscription): void
	{
		if ($subscription->status === Subscription::STATUS_ACTIVE) {
			$this->subscriptionMailService->sendSubscriptionCreated($subscription);
		}

		if ($subscription->createdSubscriptionNotificationSentAt) {
			$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription created notification sent at %s.', $subscription->createdSubscriptionNotificationSentAt->format('Y-m-d H:i:s')));
		}
	}

	public function sendNotification(SubscriptionOrder $subscriptionOrder): void
	{
		$this->subscriptionMailService->sendNotification($subscriptionOrder);
		$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionLog::TYPE_INFO, sprintf('SubscriptionOrder notification in days will be created order, sent at %s.', (new DateTimeImmutable())->format('Y-m-d H:i:s')));
	}

	public function editSubscription(Subscription $subscription, string $name, int $interval, string|null $status = null, array $userAddressData = [], ?bool $byAdmin = false): Subscription
	{
		if ($interval < 2) {
			$interval = 4;
		}
		$oldData = ['name' => $subscription->name, 'interval' => $subscription->interval, 'status' => $subscription->status];

		$subscription->name = $name;
		$subscription->interval = $interval;
		if ($status) {
			$subscription->status = $status;
		}

		$newData = ['name' => $subscription->name, 'interval' => $subscription->interval, 'status' => $subscription->status];


		$this->processAddressData($subscription, $userAddressData);

		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription %s edited, oldData: %s -> newData: %s. %s', $subscription->id, json_encode($oldData), json_encode($newData), $byAdmin ? '(By admin)' : ''));
		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('AddressData %s. %s', json_encode($userAddressData), $byAdmin ? '(By admin)' : ''));

		$this->orm->subscription->persistAndFlush($subscription);

		return $subscription;
	}

	public function editSubscriptionPayment(Subscription $subscription, array $values): Subscription
	{
		if (isset($values['paymentType'])) {
			$subscription->paymentType = $values['paymentType'];
		} else {
			return $subscription;
		}

		$this->orm->subscription->persistAndFlush($subscription);

		return $subscription;
	}

	public function editSubscriptionTransport(Subscription $subscription, array $values): Subscription
	{
		if (!empty($values['pplParcelCode'])) {
			// we need to remove KM if starting by that
			$code = preg_replace('/^KM/', '', $values['pplParcelCode']);
			$subscription->pplParcelCode = $code;
			$subscription->transportInfoText = $values['transportInfoText'];
		} else {
			return $subscription;
		}

		if ($subscription->transportType !== Order::TRANSPORT_PPL_PARCEL) {
			$subscription->pplParcelCode = null;
			$subscription->transportInfoText = null;
		}

		$this->orm->subscription->persistAndFlush($subscription);

		return $subscription;
	}

	public function activateRecurringPayment(Subscription $subscription, ?Order $order = null): SubscriptionPayment|null
	{
		$subscriptionPayment = null;
		if ($order === null) {
			$order = $subscription->lastProcessedOrder;
		}
		if ($order->paymentType === Order::PAYMENT_ONLINE && $order->paymentStatus !== Order::ONLINE_PAYMENT_PAID) {
			$goPayClient = $this->gopayClientFactory->create(
					(string) $order->mutation->langCode,
			);
			$paymentInstrument = null;
			if ($subscription->subPaymentType) {
				$paymentInstrument = Order::GOPAY_PAYMENT_INSTRUMENT[$subscription->subPaymentType];
			}
			$url = $goPayClient->createPayment($order, paymentInstrument: $paymentInstrument);

			$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s recurring payment created, payment ID: %s status: %s.', $order->subscriptionOrder->id, $order->paymentId, $order->paymentStatus));

			$subscriptionPayment = $this->orm->subscriptionPayment->getBy(['paymentId' => $order->paymentId, 'subscription' => $subscription]);
		}

		return $subscriptionPayment;
	}

	public function reactivateSubscription(Subscription $subscription): ?Order
	{
		$subscriptionOrder = $this->subscriptionOrderModel->createNext($subscription, SubscriptionOrder::STATUS_OPEN, reactivated: true);

		try {
			$order = $this->processSubscriptionOrder($subscriptionOrder, throwException: true, reactivated: true);
		} catch (Exception $e) {
			$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_ERROR, sprintf('Subscription reactivation failed, error: %s.', $e->getMessage()));

			return null;
		}

		if ($order instanceof Order) {
			$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription reactivated, created new order number: %s.', $order->number));
		}

		if ($subscription->paymentType === Order::PAYMENT_ONLINE) {
			$subscription->status = Subscription::STATUS_PENDING;
		} else {
			$subscription->status = Subscription::STATUS_ACTIVE;
		}

		$subscription->reason = null;
		$subscription->otherReason = null;

		$subscription->updated = new DateTimeImmutable();
		$subscription->cancelled = null;
		$this->orm->subscription->persistAndFlush($subscription);

		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription reactivated at %s. Status: %s', $subscription->updated->format('Y-m-d H:i:s'), $subscription->status));

		$this->subscriptionMailService->subscriptionReactivated($subscription);

		$this->processPaymentOrCod($subscriptionOrder);

		return $order;
	}

	public function cancelSubscription(Subscription $subscription, ?bool $cancelledBecauseOfReturned = false, ?bool $cancelledBecauseOfOrderNotPaid = false): void
	{
		$message = '';
		if ($cancelledBecauseOfReturned) {
			$message = 'Cancelled because of returned packages.';
		}
		if ($cancelledBecauseOfOrderNotPaid) {
			$message = 'Cancelled because of order not paid.';
		}

		$this->deactivateSubscription($subscription, message: $message, cancelledBecauseOfReturned: $cancelledBecauseOfReturned);
	}

	public function deactivateSubscription(Subscription $subscription, ?bool $fromAdmin = false, ?string $message = null, ?bool $cancelledBecauseOfReturned = false, ?string $reason = null, ?string $otherReason = null): void
	{
		$subscriptionOrders = $this->orm->subscriptionOrder->findBy(['subscription' => $subscription, 'status' => [SubscriptionOrder::STATUS_OPEN, SubscriptionOrder::STATUS_NEW, SubscriptionOrder::STATUS_PROCESSED, SubscriptionOrder::STATUS_RESERVED, SubscriptionOrder::STATUS_LOW_STOCK, SubscriptionOrder::STATUS_CANCELLED]]);
		foreach ($subscriptionOrders as $subscriptionOrder) {
			if ($subscriptionOrder->order instanceof Order) {
				try {
					$this->orderModel->storno(order: $subscriptionOrder->order, cancelReason: 'Subscription deactivated');
				} catch (Exception $e) {

				}
			}
			$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_CANCELLED);
		}

		if ($reason) {
			$subscription->reason = $reason;

			if ($otherReason) {
				$subscription->otherReason = $otherReason;
			}
		}

		$subscription->status = Subscription::STATUS_DEACTIVATED;
		$subscription->cancelled = new DateTimeImmutable();
		$subscription->updated = new DateTimeImmutable();
		$this->orm->subscription->persistAndFlush($subscription);

		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription deactivated at %s. %s %s', $subscription->updated->format('Y-m-d H:i:s'), $fromAdmin ? 'By admin.' : '', $message));

		$this->cancelRecurringPayment($subscription);

		if ($cancelledBecauseOfReturned) {
			$this->subscriptionMailService->subscriptionCancelledOrdersReturned($subscription);
		} else {
			$this->subscriptionMailService->subscriptionDeactivated($subscription);
		}
	}

	public function cancelRecurringPayment(Subscription $subscription): void
	{
		if ($subscription->recurringPayment === null) {
			return;
		}

		$this->subscriptionPaymentModel->cancelRecurringPayments($subscription);
		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription payments cancelled at %s.', $subscription->updated->format('Y-m-d H:i:s')));
	}

	/**
	 * @throws SubscriptionHasNoOpenOrderException
	 */
	public function changeDatesForSubscription(Subscription $subscription, int $newInterval): Subscription
	{
		if (array_key_exists($newInterval, Subscription::INTERVALS) === false) {
			throw new Exception('Not Allowed interval');
		}

		$subscriptionOrder = $subscription->newSubscriptionOrder;
		if ($subscription->newSubscriptionOrder || ($subscription->openSubscriptionOrder && $subscription->openSubscriptionOrder->order === null)) {
			$subscriptionOrder = $subscription->openSubscriptionOrder ?: $subscription->newSubscriptionOrder;
			$this->subscriptionOrderModel->changeSubscriptionOrderDates($subscriptionOrder, $newInterval);
		}

		$subscription->interval = $newInterval;
		$this->orm->subscription->persistAndFlush($subscription);

		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription %s - edited interval (to %s).', $subscription->name, $newInterval));

		if ($subscriptionOrder instanceof SubscriptionOrder && $subscriptionOrder->isReadyToProcess) {
			$subscriptionOrder->processedImmediately = true;
			$this->processSubscriptionOrder($subscriptionOrder);
			$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionOrderLog::TYPE_INFO, sprintf('Subscription order %s - processed immediately because after changed interval has to be processed.', (string)$subscriptionOrder->id));
		}

		return $subscription;
	}

	public function skipNextOrder(Subscription $subscription, ?bool $fromAdmin = false): void
	{
		$subscriptionOrder = $this->orm->subscriptionOrder->getLastForSubscription($subscription, SubscriptionOrder::STATUS_RESERVED);
		if ($subscriptionOrder === null) {
			$subscriptionOrder = $this->orm->subscriptionOrder->getLastForSubscription($subscription, SubscriptionOrder::STATUS_OPEN);
		}
		if ($subscriptionOrder === null) {
			$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription %s - no open or reserved order. Did not skip anything.', $subscription->name));
			return;
		}

		$this->subscriptionOrderModel->skipOrder($subscriptionOrder);

		if ($subscriptionOrder->order instanceof Order) {
			$this->orderModel->storno(order: $subscriptionOrder->order);
		}

		$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s skipped.%s', $subscriptionOrder->id, $fromAdmin ? ' By admin.' : ''));

		$this->subscriptionMailService->sendSkippedNotification($subscriptionOrder);

		$this->newOrderToOpenAfterPaidOrSkipped($subscription, $subscriptionOrder);
	}

	/**
	 * @throws SubscriptionIsNotActiveException
	 * @throws SubscriptionOrderNotClosedException
	 */
	public function createNextSubscriptionOrder(Subscription $subscription, bool $isReactivated = false): SubscriptionOrder
	{
		if ($subscription->status !== Subscription::STATUS_ACTIVE && $isReactivated === false) {
			throw new SubscriptionIsNotActiveException('Cannot create next order, because subscription is not active.');
		}

		$subscriptionOrder = $this->subscriptionOrderModel->createNext($subscription);

		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s created.', $subscriptionOrder->id));

		return $subscriptionOrder;
	}

	public function logSubscriptionError(Subscription $subscription, string $message): void
	{
		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_ERROR, $message);
	}

	/**
	 * @throws Exception
	 */
	public function addItemToSubscription(string $type, Subscription $subscription, ProductVariant $productVariant, int|null $amount = 1, bool|null $fromAdmin = null): void
	{
		$this->subscriptionOrderModel->addItemToSubscription($type, $subscription, $productVariant, $amount, $fromAdmin);
		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Item productVariant %s (%s) added to subscription. %s', $productVariant->id, $type, $fromAdmin ? 'By admin.' : ''));
	}

	public function removeItemFromSubscription(string $type, Subscription $subscription, ProductVariant $productVariant, SubscriptionOrder|null $subscriptionOrder = null): void
	{
		$this->subscriptionOrderModel->removeItemFromSubscription($type, $subscription, $productVariant, $subscriptionOrder);
		$this->subscriptionLogModel->log($subscription, SubscriptionLog::TYPE_INFO, sprintf('Item productVariant %s (%s) removed from subscription.', $productVariant->id, $type));
	}

	public function setAmountForItemToSubscription(SubscriptionItem $subscriptionItem, int $amount = 1, ?string $type = null): void
	{
		if ($amount < 0) {
			throw new Exception('amount_cannot_be_negative');
		}
		if ($amount === 0) {
			if ($subscriptionItem->type == SubscriptionItemType::SUBSCRIPTION->value && count($subscriptionItem->subscription->subscriptionItems) === 1) {
				throw new \Exception('subscription_cannot_remove_last_item');
				return;
			}

			$this->removeItemFromSubscription($subscriptionItem->type, $subscriptionItem->subscription, $subscriptionItem->productVariant);
			return;
		}
		if ($type != null) {
			$subscriptionItem->type = $type;
		}
		$this->subscriptionOrderModel->setAmountForItemToSubscription($subscriptionItem, $amount);
	}

	public function removeItemById(int $itemId, bool|null $fromAdmin = null): void
	{
		$entity = $this->orm->subscriptionItem->getBy(['id' => $itemId]);
		if (!$entity instanceof SubscriptionItem) {
			return;
		}
		$this->subscriptionLogModel->log($entity->subscription, SubscriptionLog::TYPE_INFO, sprintf('Item productVariant %s removed from subscription. %s', $entity->productVariant->id, $fromAdmin ? 'By admin.' : ''));
		$entity->isDeleted = true;
		$this->orm->persistAndFlush($entity);
	}

	public function forceCreateImmediately(SubscriptionOrder $subscriptionOrder): void
	{
		$subscriptionOrder->payOrderAfter = new DateTimeImmutable();
		$subscriptionOrder->createOrderReservationAfter = new DateTimeImmutable();
		if ($subscriptionOrder->notificationSentAt === null) {
			$subscriptionOrder->notificationSentAt = new DateTimeImmutable();
			$subscriptionOrder->sendNotificationAfter = new DateTimeImmutable();
		}

		$subscriptionOrder->processedImmediately = true;
		$this->processSubscriptionOrder($subscriptionOrder);

		$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s forced create immediately by admin.', $subscriptionOrder->id));
	}

	public function processPaymentNow(SubscriptionOrder $subscriptionOrder): void
	{
		$subscriptionOrder->payOrderAfter = new DateTimeImmutable();

		$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionOrderLog::TYPE_INFO, sprintf('Subscription order %s forced to pay now by admin.', $subscriptionOrder->id));
	}

	/**
	 * @throws SubscriptionHasNoItemsException
	 */
	public function processSubscriptionOrder(SubscriptionOrder $subscriptionOrder, bool|null $tryAgainByAdmin = false, bool|null $throwException = false, bool|null $reactivated = false): ?Order
	{
		if ($tryAgainByAdmin) {
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s will be processed again by admin.', $subscriptionOrder->id));
		}

		if (empty($subscriptionOrder->subscription->items)) {
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_ERROR, 'Cannot process order, because subscription has no items.');

			if ($throwException) {
				throw new SubscriptionHasNoItemsException('Cannot process order, because subscription has no items.');
			}

			return null;
		}

		$isProcessedOrder = $this->orm->subscriptionOrder->getBy(['subscription' => $subscriptionOrder->subscription, 'status' => SubscriptionOrder::STATUS_PROCESSED]) instanceof SubscriptionOrder;
		if ($isProcessedOrder) {
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_ERROR, sprintf('Cannot create order for subscription (subscription order id %s), because last order is not paid or shipped.', $subscriptionOrder->id));

			if ($throwException) {
				throw new SubscriptionHasProcessedOrderException('Cannot create order for subscription, because last order is not paid or shipped.');
			}

			return null;
		}

		try {
			$order = $this->orderModel->createNewFromSubscriptionOrder($subscriptionOrder);
		} catch (SubscriptionOrderNotCreatedBecauseLowStockException $exception) {
			$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_LOW_STOCK);
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_ERROR, $exception->getMessage());
			$this->subscriptionMailService->orderFromSubscriptionCreationFailed($subscriptionOrder->subscription);
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_ERROR, 'Send failed order notification to admin (low stock).');

			return null;
		} catch (Exception $exception) {
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_ERROR, 'Failed to create order from subscription order.' . $exception->getMessage());
			return null;
		}

		if ($order === null) {
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_ERROR, sprintf('Cannot process order, because order was not created. (subscription order id %s)', $subscriptionOrder->id));

			return null;
		}

		$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_RESERVED);

		$this->subscriptionMailService->sendReservationMail($subscriptionOrder);

		$this->createNextSubscriptionOrder($subscriptionOrder->subscription, $reactivated);

		return $order;
	}

	public function processPaymentOrCod(SubscriptionOrder $subscriptionOrder, bool $forceMarkAsPaid = false): void
	{
		if ($subscriptionOrder->order->subscription->status === Subscription::STATUS_PENDING) {
			$subscriptionOrder->subscription->status = Subscription::STATUS_ACTIVE;
			$this->sendSubscriptionCreationMail($subscriptionOrder->subscription);
			$this->orm->subscription->persistAndFlush($subscriptionOrder->subscription);
		}

		if ($subscriptionOrder->sequence > 1 && ($subscriptionOrder->status == SubscriptionOrder::STATUS_PAID || $subscriptionOrder->status == SubscriptionOrder::STATUS_COD)) {
			return;
		}

		$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_PROCESSED);

		if ($subscriptionOrder->order->paymentType === Order::PAYMENT_ONDELIVERY) {
			// status COD is already set
			$this->orderMailService->normalConfirm($subscriptionOrder->order);
			$subscriptionOrder->orderPaidAt = new DateTimeImmutable();
		} elseif ($subscriptionOrder->order->paymentType === Order::PAYMENT_ONLINE) {
			$subscriptionOrder->lastPaymentProcessedAt = new DateTimeImmutable();
			if ($subscriptionOrder->order->isPayed || $this->configService->isEnvLocal() || $forceMarkAsPaid) {
				$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_PAID);
				$subscriptionOrder->orderPaidAt = new DateTimeImmutable();
				$this->processPaidSubscriptionOrder($subscriptionOrder);
				$this->subscriptionPaymentModel->changeStatus((string)$subscriptionOrder->order->paymentId, 'PAID');
				$subscriptionOrder->order->invoiceDate = new DateTimeImmutable();
				try {
					$this->orderMailService->normalConfirm($subscriptionOrder->order);
				} catch (Exception $e) {

				}
			}
		}

		if ($this->configService->isEnvLocal() && $subscriptionOrder->order->paymentId === null && $subscriptionOrder->order->paymentType === Order::PAYMENT_ONLINE) {
			$subscriptionOrder->order->paymentStatus = Order::ONLINE_PAYMENT_PAID;
			$subscriptionOrder->order->paymentId = rand(10000, 10000000);
			$subscriptionOrder->order->paymentJson = json_encode(['local' => true]);
		}

		if ($subscriptionOrder->isPaid) {
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s processed, changed status to open for order number: %s.', $subscriptionOrder->id, $subscriptionOrder->order->number));
			$this->newOrderToOpenAfterPaidOrSkipped($subscriptionOrder->subscription);
		}
	}

	/**
	 * @param SubscriptionOrder $subscriptionOrder
	 * @throws SubscriptionOrderNotClosedException
	 */
	public function processPaidSubscriptionOrder(SubscriptionOrder $subscriptionOrder): void
	{
		if ($subscriptionOrder->status !== SubscriptionOrder::STATUS_PAID)  {
			return;
		}

		if ($subscriptionOrder->order === null) {
			$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionLog::TYPE_ERROR, sprintf('Cannot process order, because order is not created. (subscriptionOrder %s)', $subscriptionOrder->id));

			return;
		}

		if (!in_array($subscriptionOrder->status, [SubscriptionOrder::STATUS_PROCESSED, SubscriptionOrder::STATUS_PAID])) {
			$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionLog::TYPE_ERROR, sprintf('Cannot mark as paid, only allowed for processed. This subscriptionOrder has status: %s', $subscriptionOrder->status));

			return;
		}

		$subscriptionOrder->orderPaidAt = new DateTimeImmutable();
		$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_PAID);
		$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s paid.', $subscriptionOrder->id));

		if ($subscriptionOrder->subscription->nextSubscriptionOrder instanceof SubscriptionOrder) {
			$this->subscriptionOrderModel->changeStatus($subscriptionOrder->subscription->nextSubscriptionOrder, SubscriptionOrder::STATUS_OPEN);
		} else {
			$this->subscriptionOrderModel->createNext($subscriptionOrder->subscription, SubscriptionOrder::STATUS_OPEN);
		}

//		if ($this->novikoOrderService->saveHeader($subscriptionOrder->order)) {
//			$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s updated to Noviko.', $subscriptionOrder->id));
//		} else {
//			$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionLog::TYPE_ERROR, sprintf('Subscription order %s failed to update to Noviko.', $subscriptionOrder->id));
//		}
	}

	public function cancelSubscriptionOrder(SubscriptionOrder $subscriptionOrder, string $reason = SubscriptionOrder::CANCELLING_REASON_NOT_PAID_IN_INTERVAL): void
	{
		if ($subscriptionOrder->isPaid) {
			return;
		}
		if ($subscriptionOrder->order instanceof Order && $subscriptionOrder->order->canceled === false) {
			try {
				$this->orderModel->storno($subscriptionOrder->order, $reason);
			} catch (Exception $e) {
			}
		}

		$this->subscriptionOrderModel->changeStatus($subscriptionOrder, SubscriptionOrder::STATUS_CANCELLED);
		$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s cancelled. Reason: %s', $subscriptionOrder->id, $reason));

		if ($subscriptionOrder->isCancelled && $subscriptionOrder->subscription->isActive) {
			$this->newOrderToOpenAfterPaidOrSkipped($subscriptionOrder->subscription, $subscriptionOrder);
		}
	}

	public function markSubscriptionOrderAsPaid(SubscriptionOrder $subscriptionOrder): void
	{
		// use only if needed!!! for single order as user paid it differently than usual way
		if ($subscriptionOrder->isPaid) {
			return;
		}

		$subscriptionOrder->order->status = Order::STATUS_NEW;
		$this->processPaymentOrCod($subscriptionOrder, true);

		$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionOrderLog::TYPE_INFO, sprintf('Subscription order %s marked as paid now. MANUALLY CHANGED!!!', $subscriptionOrder->id));
	}

	public function getUniqueHash(): string
	{
		do {
			$hash = Random::generate(32);
		} while ($this->orm->subscription->getBy(['hash' => $hash]) instanceof Subscription);

		return $hash;
	}

	private function processAddressData(Subscription $subscription, array $userAddressData = []): void
	{
		if (!empty($userAddressData['pplParcelCode'])) {
			$code = preg_replace('/^KM/', '', $userAddressData['pplParcelCode']);
			$userAddressData['pplParcelCode'] = $code;
		}

		foreach ($subscription->getMetadata()->getProperties() as $i) {
			$col = $i->name;
			if (in_array($col, ['id', 'hash', 'created', 'user', 'status', 'interval', 'name', 'mutation'], true)) {
				continue;
			}

			if (isset($userAddressData[$col])) {
				$subscription->$col = $userAddressData[$col];
			}
		}
	}

	public function processRecurringPayment(Order $order, SubscriptionOrder $subscriptionOrder): void
	{
		if ($subscriptionOrder->subscription->cashOnDelivery) {
			return;
		}

		if ($this->configService->isEnvLocal()) {
			$this->processPaymentOrCod($subscriptionOrder);
			return;
		}

		$goPayClient = $this->gopayClientFactory->create(
			(string) $order->mutation->langCode,
		);

		try {
			if ($subscriptionOrder->lastPaymentProcessedAt !== null) {
				$subscriptionOrder->paymentIteration++;
			}
			$subscriptionOrder->lastPaymentProcessedAt = new DateTimeImmutable();

			$this->orm->subscriptionOrder->persist($subscriptionOrder);

			$goPayClient->createRecurrencePayment($order);
		} catch (Throwable $e) {
			$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_ERROR, sprintf('Subscription order %s recurring payment failed, error: %s.', $subscriptionOrder->id, $e->getMessage()));

			return;
		}

		$this->subscriptionLogModel->log($subscriptionOrder->subscription, SubscriptionLog::TYPE_INFO, sprintf('Subscription order %s payment processed, payment ID: %s status: %s.', $subscriptionOrder->id, $subscriptionOrder->order->paymentId, $subscriptionOrder->order->paymentStatus));
	}

	public function sendReauthorizationNotification(SubscriptionOrder $subscriptionOrder): void
	{
		if ($subscriptionOrder->isPaid === false && $subscriptionOrder->paymentIteration >= SubscriptionOrder::PAYMENT_MAX_ITERATION) {
			$this->processUnsuccesfulPayments($subscriptionOrder);
			$this->subscriptionOrderLogModel->log($subscriptionOrder, SubscriptionLog::TYPE_ERROR, sprintf('Subscription order %s recurring payment failed and was sent mail with payment gateway link, reached max iteration.', $subscriptionOrder->id));
		}
	}

	public function processUnsuccesfulPayments(SubscriptionOrder $subscriptionOrder): void
	{
		if ($subscriptionOrder->subscription->cashOnDelivery !== true) {
			$this->subscriptionMailService->sendMissingRecurringPayment($subscriptionOrder);
		}
	}

	/**
	 * @param Subscription $subscription
	 * @param SubscriptionOrder|null $lastSubscriptionOrderForCountingDates
	 * @return void
	 */
	private function newOrderToOpenAfterPaidOrSkipped(Subscription $subscription, ?SubscriptionOrder $lastSubscriptionOrderForCountingDates = null): void
	{
		$openSubscriptionOrder = $this->subscriptionOrderModel->setSubscriptionOrderDates($subscription->openSubscriptionOrder, $lastSubscriptionOrderForCountingDates);
		$this->orm->subscriptionOrder->persistAndFlush($openSubscriptionOrder);
		$this->subscriptionOrderModel->changeStatus($openSubscriptionOrder, SubscriptionOrder::STATUS_OPEN);
	}

}

class SubscriptionHasNoItemsException extends Exception
{

}

class SubscriptionHasProcessedOrderException extends Exception
{

}

class SubscriptionHasNoOpenOrderException extends Exception
{

}

class SubscriptionShouldHaveNewNextOrderException extends Exception
{

}

class SubscriptionOrderNotCreatedBecauseLowStockException extends Exception
{

}

class SubscriptionNewIntervalIsInThePastException extends Exception
{

}
