<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasOrmTrait;
use <PERSON>Koderi\hasTranslatorTrait;

/**
 * @property int $id {primary}
 * @property string $hash
 * @property string $name
 * @property int $interval {default 4}
 * @property string $status {enum self::STATUS_*} {default self::STATUS_DRAFT}
 * @property string|null $transportType {enum ORDER::TRANSPORT_*} {default ORDER::TRANSPORT_PPL}
 * @property string|null $paymentType {enum ORDER::PAYMENT_*} {default ORDER::PAYMENT_ONLINE}
 * @property string|null $subPaymentType {enum ORDER::ONLINE_PAYMENT_TYPE*}
 * @property int|null $zasilkovnaId
 * @property string|null $pplParcelCode //we need to remove KM if starting by that
 * @property string|null $transportInfoText
 * @property array $transportData {wrapper JsonContainer}
 * @property string $email {default ''}
 * @property string $firstname {default ''}
 * @property string $lastname {default ''}
 * @property string $phone {default ''}
 * @property string $street {default ''}
 * @property string $city {default ''}
 * @property string $zip {default ''}
 * @property string $state {default ''}
 * @property string|null $company
 * @property string|null $ic
 * @property string|null $dic
 * @property string|null $dFirstname
 * @property string|null $dLastname
 * @property string|null $dCompany
 * @property string|null $dPhone
 * @property string|null $dStreet
 * @property string|null $dCity
 * @property string|null $dZip
 * @property string|null $dState
 * @property string|null $dInfo
 * @property string|null $infotext
 * @property DateTimeImmutable|null $createdSubscriptionNotificationSentAt
 * @property DateTimeImmutable|null $cancelled {default null}
 * @property DateTimeImmutable $updated {default 'now'}
 * @property DateTimeImmutable $created {default 'now'}
 * @property string|null $reason {default null}
 * @property string|null $otherReason {default null}
 *
 * RELATIONS
 * @property User $user {m:1 User::$subscriptions}
 * @property SubscriptionItem[] $items {1:m SubscriptionItem::$subscription}
 * @property SubscriptionOrder[] $subscriptionOrders {1:m SubscriptionOrder::$subscription}
 * @property SubscriptionLog[] $logs {1:m SubscriptionLog::$subscription}
 * @property SubscriptionPayment[] $payments {1:m SubscriptionPayment::$subscription}
 *
 * VIRTUAL
 * @property-read Mutation $mutation {virtual}
 * @property-read Order[] $orders {virtual}
 * @property-read SubscriptionItem[] $subscriptionItems {virtual}
 * @property-read SubscriptionOrder|null $lastSubscriptionOrder {virtual}
 * @property-read SubscriptionOrder|null $newSubscriptionOrder {virtual}
 * @property-read SubscriptionOrder|null $openSubscriptionOrder {virtual}
 * @property-read SubscriptionOrder|null $reservedSubscriptionOrder {virtual}
 * @property-read SubscriptionOrder|null $nextSubscriptionOrder {virtual}
 * @property-read Order|null $lastProcessedOrder {virtual}
 * @property-read DateTimeImmutable|null $nextOrderCreationDate {virtual}
 * @property-read SubscriptionPayment|null $recurringPayment {virtual}
 * @property-read bool $cashOnDelivery {virtual}
 * @property-read bool $isActive {virtual}
 * @property-read float $totalSubscriptionProducts {virtual}
 * @property-read float $saleTotalSubscriptionProducts {virtual}
 * @property-read float $countOfReturnedPackages {virtual}
 */
class Subscription extends Entity
{

	use hasConfigServiceTrait;
	use hasOrmTrait;
	use hasTranslatorTrait;

	public const DAYS_BETWEEN_MISSING_PAYMENTS_NOTIFICATIONS = 7;
	public const DAYS_FOR_STOCK_REPORT = 5;

	public const COUNT_OF_RETURNED_PACKAGES_FOR_CANCELLING = 2;

	public const STATUS_DRAFT = 'draft';
	public const STATUS_ACTIVE = 'active';
	public const STATUS_PENDING = 'pending';
	public const STATUS_DEACTIVATED = 'deactivated';

	public const STATUSES = [
		self::STATUS_DRAFT => self::STATUS_DRAFT,
		self::STATUS_ACTIVE => self::STATUS_ACTIVE,
		self::STATUS_PENDING => self::STATUS_PENDING,
		self::STATUS_DEACTIVATED => self::STATUS_DEACTIVATED,
		//self::STATUS_CANCELLED => self::STATUS_CANCELLED,
	];

	public const PAYMENTS = [
		Order::PAYMENT_ONLINE => Order::PAYMENT_ONLINE,
		Order::PAYMENT_ONDELIVERY => Order::PAYMENT_ONDELIVERY,
	];

	public const SUB_PAYMENTS = [
		Order::ONLINE_PAYMENT_TYPE_CARD => Order::ONLINE_PAYMENT_TYPE_CARD,
		Order::ONLINE_PAYMENT_TYPE_APPLE_PAY => Order::ONLINE_PAYMENT_TYPE_APPLE_PAY,
		Order::ONLINE_PAYMENT_TYPE_GOOGLE_PAY => Order::ONLINE_PAYMENT_TYPE_GOOGLE_PAY,
	];

	public const INTERVALS = [
			2 => '2 weeks_2_4',
			3 => '3 weeks_2_4',
			4 => '4 weeks_2_4',
			5 => '5 weeks_5',
			6 => '6 weeks_5',
			7 => '7 weeks_5',
			8 => '8 weeks_5',
			9 => '9 weeks_5',
			10 => '10 weeks_5',
			11 => '11 weeks_5',
			12 => '12 weeks_5',
	];

	public function getterMutation(): Mutation
	{
		return $this->user->mutation;
	}

	public function getterOrders(): array
	{
		$orders = [];
		foreach ($this->subscriptionOrders as $subscriptionOrder) {

			if ($subscriptionOrder->order !== null) {
				$orders[] = $subscriptionOrder->order;
			}
		}

		return $orders;
	}

	public function getterOpenSubscriptionOrder(): SubscriptionOrder|null
	{
		$order = $this->orm->subscriptionOrder->getBy(['subscription' => $this, 'status' => SubscriptionOrder::STATUS_OPEN]);
		if ($order === null) {
			$order = $this->orm->subscriptionOrder->getBy(['subscription' => $this, 'status' => SubscriptionOrder::STATUS_LOW_STOCK]);
		}
		if ($order === null) {
			$order = $this->orm->subscriptionOrder->getBy(['subscription' => $this, 'status' => SubscriptionOrder::STATUS_NEW]);
		}
		return $order;
	}

	public function getterReservedSubscriptionOrder(): SubscriptionOrder|null
	{
		return $this->orm->subscriptionOrder->getBy(['subscription' => $this, 'status' => SubscriptionOrder::STATUS_RESERVED]);
	}

	public function getterNewSubscriptionOrder(): SubscriptionOrder|null
	{
		return $this->orm->subscriptionOrder->getBy(['subscription' => $this, 'status' => SubscriptionOrder::STATUS_NEW]);
	}

	public function getterNextSubscriptionOrder(): SubscriptionOrder|null
	{
		return $this->newSubscriptionOrder ?: $this->openSubscriptionOrder;
	}

	public function getterLastSubscriptionOrder(): SubscriptionOrder|null
	{
		$subscriptionOrder = $this->orm->subscriptionOrder->getLastForSubscription($this);

		if ($subscriptionOrder instanceof SubscriptionOrder) {
			return $subscriptionOrder;
		}

		return null;
	}

	public function getterLastProcessedOrder(): Order|null
	{
		return $this->orm->order->getLastOrderForSubscription($this);
	}

	public function getterNextOrderCreationDate(): DateTimeImmutable|null
	{
		return $this->nextSubscriptionOrder ? $this->nextSubscriptionOrder->payOrderAfter : null;
	}

	public function getterRecurringPayment(): SubscriptionPayment|null
	{
		return $this->orm->subscriptionPayment->getBy(['isMain' => true, 'subscription' => $this, 'isActive' => true]);
	}

	public function getterCashOnDelivery(): bool
	{
		return $this->paymentType === Order::PAYMENT_ONDELIVERY;
	}

	public function getterSubscriptionItems(): array
	{
		$items = [];
		foreach ($this->items as $item) {
			if ($item->oneTimeSubscriptionOrder === null && $item->isDeleted === false) {
				$items[] = $item;
			}
		}

		return $items;
	}

	public function getterTotalSubscriptionProducts(): float
	{
		$total = 0;
		foreach ($this->subscriptionItems as $item) {
			$total += $item->productVariant->subscriptionPrice->priceDPH * $item->amount;
		}

		return $total;
	}

	public function getterSaleTotalSubscriptionProducts(): float
	{
		$total = 0;
		foreach ($this->subscriptionItems as $item) {
			$total += ($item->productVariant->basicPrice->priceDPH - $item->productVariant->subscriptionPrice->priceDPH) * $item->amount;
		}

		return $total;
	}

	public function getterCountOfReturnedPackages(): int
	{
		$count = 0;
		foreach ($this->orders as $order) {
			if ($order->novikoStatus === \SuperKoderi\Noviko\Order\Order::ID_STATUS_RETURNED_BY_THE_CARRIER) {
				$count++;
			}

		}

		return $count;
	}

	public function getterIsActive(): bool
	{
		return $this->status === self::STATUS_ACTIVE;
	}

}
