<?php

namespace App\Model;

use App\Model\Orm\TraitsEntity\HasFormDefaultData;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\hasConstsTrait;

/**
 * @property int $id {primary}
 * @property string|null $type
 * @property string $name
 * @property DateTimeImmutable|null $createdTime
 * @property int|null $created
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$userGroups}
 * @property OneHasMany|UserGroupDiscount[] $discounts {1:m UserGroupDiscount::$userGroup}
 * @property User[]|ManyHasMany $users {m:m User::$groups}
 *
 * VIRTUAL
 * @property-read bool $isTypeDefault {virtual}
 * @property-read bool $isTypeRegistered {virtual}
 * @property-read bool $isTypeCommon {virtual}
 * @property-read bool $isCanDelete {virtual}
 * @property-read bool $isFirstSubscription {virtual}
 * @property-read bool $isSubscription {virtual}
 *
 */
class UserGroup extends \Nextras\Orm\Entity\Entity
{

	use HasFormDefaultData;
	use hasConstsTrait;

	const TYPE_DEFAULT = 'default'; // defaultni pro neprihlasene
	const TYPE_REGISTERED = 'registered'; // registrovaní
	const TYPE_REGISTERED_BREEDING = 'registered-breeding'; // registrovaní
	const TYPE_COMMON = 'common';
	const TYPE_SUBSCRIPTION = 'subscription';
	const TYPE_FIRST_SUBSCRIPTION = 'first-subscription';

	// seznam základních skupin pro každou mutaci, kam uživatelé patří vždy
	const BASE_DEFAULT_IDS = [
		Mutation::ID_CS_DEFAULT => 2,
		Mutation::ID_EU_INTERNATIONAL => 5,
		Mutation::ID_SK_ESHOP => 8,
		Mutation::ID_PL_ESHOP => 34
	];

	// cenová skupin pro první objednávku v předplatném
	const BASE_DEFAULT_SUBSCRIPTION_FIRST_ORDER_IDS = [
		Mutation::ID_CS_DEFAULT => 2,
		Mutation::ID_EU_INTERNATIONAL => 5,
		Mutation::ID_SK_ESHOP => 8,
		Mutation::ID_PL_ESHOP => 34
	];

	// dtto, ale pro chovatele
	const BASE_DEFAULT_BREEDING_IDS = [
		Mutation::ID_CS_DEFAULT => 9,
		Mutation::ID_EU_INTERNATIONAL => 10,
		Mutation::ID_SK_ESHOP => 11,
		Mutation::ID_PL_ESHOP => 35
	];

	// cenová skupin pro předplatné
	const BASE_DEFAULT_SUBSCRIPTION_IDS = [
		Mutation::ID_CS_DEFAULT => 9,
		Mutation::ID_EU_INTERNATIONAL => 10,
		Mutation::ID_SK_ESHOP => 11,
		Mutation::ID_PL_ESHOP => 35
	];

	protected function getterIsTypeDefault()
	{
		return $this->type === self::TYPE_DEFAULT;
	}

	protected function getterIsTypeRegistered()
	{
		return $this->type === self::TYPE_REGISTERED;
	}

	protected function getterIsTypeCommon()
	{
		return $this->type === self::TYPE_COMMON;
	}

	protected function getterIsCanDelete()
	{
		return $this->type === self::TYPE_COMMON;
	}

	protected function getterIsFirstSubscription()
	{
		return $this->type === self::TYPE_FIRST_SUBSCRIPTION;
	}

	protected function getterIsSubscription()
	{
		return $this->type === self::TYPE_SUBSCRIPTION;
	}

}
