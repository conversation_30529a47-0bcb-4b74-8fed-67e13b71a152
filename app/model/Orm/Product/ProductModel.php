<?php

namespace App\Model;

use App\Model\CustomField\CustomFields;
use App\Model\ElasticSearch\All\Facade;
use Nette\Utils\DateTime;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\CommentService;
use SuperKoderi\ConfigService;
use SuperKoderi\ElasticSearch\Product\Service;
use SuperKoderi\Email\ICommonFactory;
use SuperKoderi\StringHelper;
use const E_USER_WARNING;
use function explode;
use function implode;
use function in_array;
use function strlen;
use function trigger_error;

class ProductModel
{

	private array $vetDietChanges = [];

	public function __construct(
		private readonly Orm $orm,
		private readonly ProductVariantModel $productVariantModel,
		private readonly CommentService $commentService,
		private readonly ParameterValueModel $parameterValueModel,
		private readonly ConfigService $configService,
		private readonly ParameterModel $parameterModel,
		private readonly CustomFields $customFields,
		private readonly ProductTreeModel $productTreeModel,
		private readonly ICommonFactory $commonEmailFactory,
		private readonly \App\Model\ElasticSearch\Product\Facade $productElasticFacade,
		private readonly \App\Model\ElasticSearch\All\Facade $allElasticFacade,
	)
	{
	}

	public function save(Product $product, $data, $userId, $hasStandardParameters)
	{
		//bd([__METHOD__, '$hasStandardParameters', $hasStandardParameters]);

		$intValues = [];
		// $boolValues = zde musí být všechny checkboxy -> když nezaškrtne, tak není $col v $data a neuloží se!! proto pak $product->$col = 0;
		$boolValues = ['public', 'isInStore', 'isInPrepare', 'isOld', 'notSoldSeparately', 'hideFirstImage',
			'isNew', 'isTopProduct', 'isPromotional', 'isBestseller', 'isAction',
			'isBig', 'isFreeTransport', 'isWarranty', 'isSale', 'isGift', 'isBasketGift', 'isSubscription'];
		$noSave = ['id', 'alias', 'aliasHistoryString', 'availableServices', 'articlePages'];

		$this->purchasableVetDietCheck($product);

		//TODO REF - vyresit cas produktu
		if (!isset($data['publicFrom']) || !$data['publicFrom']) {
			$noSave[] = 'publicFrom';
		}

		if (!isset($data['publicFrom']) || !$data['publicTo']) {
			$noSave[] = 'publicTo';
		}

		if (isset($data['prepareDate']) && !$data['prepareDate']) {
			$data['prepareDate'] = NULL;
		}

		$data['edited'] = $userId;
		$data['editedTime'] = DateTime::from('now')->format('Y-m-d H:i:s');

		foreach ($product->getMetadata()->getProperties() as $i) {


			$col = (string)$i->name;

			if (in_array($col, $noSave)) {
				continue;
			}

			if (isset($data[$col])) {
				if (in_array($col, $intValues)) {
					$data[$col] = (int)$data[$col];
				}
				$product->$col = $data[$col];
			} else {
				if (in_array($col, $boolValues)) {
					$product->$col = 0;
				}
			}
		}

		// specialita - dostupne service na produktu
		if (isset($data['availableServices'])) {
			$product->availableServices = implode("|", $data['availableServices']);
		} else {
			// TODO - pozor pri importu je potreba tyto hodnoty nastavit aby se nesmazaly
			$product->availableServices = NULL;
		}

		$this->handleVariants($product, $data, $userId);
		$this->handleReviews($product, $data);

		$this->handleImages($product, $data);
		$this->handleFiles($product, $data);
		$this->handleLinks($product, $data);
		$this->handleVideos($product, $data);

		if (isset($data['customFields'])) {
			$product->cf = $this->customFields->prepareDataToSave($data['customFields']);
		}

//		$this->handleGifts($tree, $data);

		$this->orm->product->persistAndFlush($product);
		$product->flushParam();

		// aliasy se musi resit po persistovani produktu
		$product->setAliases($data['alias'], $data['aliasHistoryString']);


//		$this->handleAlias($product, $data);
		$this->handleCats($product, $data);
		$this->handleComments($product, $data);
		$this->handleParameters($product, $data, $hasStandardParameters);
		$this->handlePartnerParameters($product, $data, $hasStandardParameters);
		$this->handlePages($product, $data);
		$this->handleAccessories($product, $data);
		$this->handleProducts($product, $data);
		$this->handleSimilarProducts($product, $data);
		$this->handleContents($product, $data);
		$this->handleArticles($product, $data);
//		$this->handlePresents($product, $data);

		$flagsGrouped = $this->getFlagsGrouped($data['variants_rows'], $product);
		$this->handleFlagsVariant($flagsGrouped, $product);

		// !!!! specail handling for FreeSample
		if (!$product->parent) {
			// je to hlavni produkt
			$paramValueFreeSample = $this->orm->parameterValue->findBy(['alias' => 'free-sample'])->fetch();
//			bd($paramValueFreeSample);
			if ($paramValueFreeSample && $paramValueFreeSample->id) {
				$isFreeSample = 0;
				if (isset($data['parameterValueId'][$paramValueFreeSample->parameter->id])) {
//					bd($data['parameterValueId'][$paramValueFreeSample->parameter->id]);
					//$product->isSample
					foreach ($data['parameterValueId'][$paramValueFreeSample->parameter->id] as $pvalue) {
						if ($pvalue == $paramValueFreeSample->id) {
							$isFreeSample = 1;
							break;
						}
					}
				}

//				bd("isSample = ".$isFreeSample);
				if ($isFreeSample != $product->isSample) {
					bd("isSample set new value = " . $isFreeSample);
					$product->isSample = $isFreeSample;
					// find all child nad set isSample
					if ($product->clones->count()) {
						foreach ($product->clones as $clone) {
							$clone->isSample = $isFreeSample;
							$this->orm->product->persistAndFlush($clone);
						}
					}
				}
			}
		}


		$this->orm->product->persistAndFlush($product);
		$product->flushParam();
		$product->flushCache();

		// tady se vyresi porametry ktere si chcu presunout na radek k variante nebo k produktu
		$this->handleFlags($product);
		$this->orm->product->persist($product);
		$this->orm->product->flush();

		$this->updateProduct($product);

		return $product;
	}

	/**
	 * <p>1. Call with $product variable only -> set original values</p>
	 * <p>2. Call with both (not null) parameters -> final check of new values and email sending</p>
	 */
	private function purchasableVetDietCheck(Product $product, ParameterValue|null $newParameterValue = null): void
	{
		if (!isset($newParameterValue)) {
			$this->vetDietChanges['isSale'] = (bool) $product->isSale;
			$this->vetDietChanges['veterinaryParameter'] = false;
			foreach ($product->getParametersValues() as $parameterValue) {
				if ($parameterValue instanceof ParameterValue && isset($parameterValue->parameter) && $parameterValue->parameter->uid === 'productLine') {
					$this->vetDietChanges['veterinaryParameter'] = $parameterValue->alias === 'veterinary-diets';
				}
			}
		} else {
			if (!isset($this->vetDietChanges['isSale'], $this->vetDietChanges['veterinaryParameter'])) {
				trigger_error('Can not resolve product changes. Provide original value first. Read annotation...', E_USER_WARNING);

				return;
			}

			$isVeterinaryDiet = $newParameterValue->alias == 'veterinary-diets';

			// Check if product is purchasable and veterinary diet
			// and also something was changed -> send notification email
			if ((bool) $product->isSale && $isVeterinaryDiet
				&& ($this->vetDietChanges['isSale'] !== (bool)$product->isSale || !$this->vetDietChanges['veterinaryParameter']))
			{
				bd('in this situation notice email should be sent');
				$emailData = [
					'productUrl' => $product->mutation->getDomain()."/".$this->configService->getParam('adminAlias')."/product/edit?id=".$product->id,
					'productName' => $product->name,
				];
				$commonEmail = $this->commonEmailFactory->create();
				$email = $product->mutation->getSetting(Orm\MutationSetting\SystemEmail::AdminEmail);
				$commonEmail->send($email, $email, 'Veterinary diet was set as purchasable', 'veterinaryDietNoticeEmail', $emailData);
				bd('sent - OK');
			}
		}
	}

	public function remove(Product $product)
	{
//		foreach ($product->variants as $v) {
//			$this->elasticProductService->deleteFromElastic($this->orm->getLang(), $v);
//			$this->elasticProductService->deleteFromElastic(NULL, $v);
//		}

		$this->productElasticFacade->deleteFromAllMutationNow($product);
		$this->allElasticFacade->deleteNow($product);
		$this->orm->product->removeAndFlush($product);
	}


	private function handleAccessories(Product $product, $data)
	{
		$actualProducts = [];
		foreach ($product->accessoriesAll as $i) {
			$actualProducts[$i->id] = 1;
		}
		if (isset($data['accessories'])) {
			foreach ($data['accessories'] as $k => $attachProductId) {
				if (isset($actualProducts[$attachProductId])) {
					$productAcc = $this->orm->product->getById($attachProductId);
					$this->orm->product->updateToAccessories($product, $productAcc, $data['accessoriesSort'][$k]);
					unset($actualProducts[$attachProductId]);
				} else {
					if ($attachProductId) {
						$productAcc = $this->orm->product->getById($attachProductId);
						$this->orm->product->addToAccessories($product, $productAcc, $data['accessoriesSort'][$k]);
					}
				}
			}
		}

		// remove
		foreach ($actualProducts as $attachProductId => $i) {
			$productAcc = $this->orm->product->getById($attachProductId);
			$this->orm->product->removeFromAccessories($product, $productAcc);
		}
	}


	private function handleSimilarProducts(Product $product, $data)
	{
		$actualProducts = [];
		foreach ($product->similarProductsAll as $i) {
			$actualProducts[$i->id] = 1;
		}
		if (isset($data['similarProducts'])) {
			$sort = 0;
			foreach ($data['similarProducts'] as $k => $attachProductId) {
				if (isset($actualProducts[$attachProductId])) {
					$productAcc = $this->orm->product->getById($attachProductId);
					$this->orm->product->updateToSimilarProducts($product, $productAcc, (string)$sort);
					unset($actualProducts[$attachProductId]);
				} else {
					if ($attachProductId) {
						$productAcc = $this->orm->product->getById($attachProductId);
						$this->orm->product->addToSimilarProducts($product, $productAcc, $data['similarProductsSort'][$k]);
					}
				}
				$sort++;
			}
		}

		// remove
		foreach ($actualProducts as $attachProductId => $i) {
			$productAcc = $this->orm->product->getById($attachProductId);
			$this->orm->product->removeFromSimilarProducts($product, $productAcc);
		}
	}


	private function handleProducts(Product $product, $data)
	{
		$actualProducts = [];
		foreach ($product->productsAll as $i) {
			$actualProducts[$i->id] = 1;
		}
		if (isset($data['products'])) {
			foreach ($data['products'] as $k => $attachProductId) {
				if (isset($actualProducts[$attachProductId])) {
					$productAcc = $this->orm->product->getById($attachProductId);
					$this->orm->product->updateToProducts($product, $productAcc, $data['productsSort'][$k]);
					unset($actualProducts[$attachProductId]);
				} else {
					if ($attachProductId) {
						$productAcc = $this->orm->product->getById($attachProductId);
						$this->orm->product->addToProducts($product, $productAcc, $data['productsSort'][$k]);
					}
				}
			}
		}

		// remove
		foreach ($actualProducts as $attachProductId => $i) {
			$productAcc = $this->orm->product->getById($attachProductId);
			$this->orm->product->removeFromProducts($product, $productAcc);
		}


		// filter values
	}


	private function handlePresents(Product $product, $data)
	{
		$actualProducts = [];
		foreach ($product->presents as $i) {
			$actualProducts[$i->id] = 1;
		}
		if (isset($data['presents'])) {
			foreach ($data['presents'] as $k => $attachProductId) {
				if (isset($actualProducts[$attachProductId])) {
					$productAcc = $this->orm->product->getById($attachProductId);
					$this->orm->product->updateToPresents($product, $productAcc, $data['presentsSort'][$k]);
					unset($actualProducts[$attachProductId]);
				} else {
					if ($attachProductId) {
						$productAcc = $this->orm->product->getById($attachProductId);
						$this->orm->product->addToPresents($product, $productAcc, $data['presentsSort'][$k]);
					}
				}
			}
		}

		// remove
		foreach ($actualProducts as $attachProductId => $i) {
			$productAcc = $this->orm->product->getById($attachProductId);
			$this->orm->product->removeFromPresents($product, $productAcc);
		}
	}


	private function handlePages(Product $product, $data)
	{
		$actualPages = [];
		foreach ($product->pages as $i) {
			$actualPages[$i->id] = 1;
		}
		if (isset($data['pages'])) {
			foreach ($data['pages'] as $k => $attachTreeId) {
				if (isset($actualPages[$attachTreeId])) {
					$page = $this->orm->tree->getById($attachTreeId);
					$this->orm->product->updateToPages($page, $product, $data['pagesSort'][$k]);
					unset($actualPages[$attachTreeId]);
				} else {
					if ($attachTreeId) {
						$page = $this->orm->tree->getById($attachTreeId);
						$this->orm->product->addToPages($page, $product, $data['pagesSort'][$k]);
					}
				}
			}
		}

		// remove
		foreach ($actualPages as $attachTreeId => $i) {
			$page = $this->orm->tree->getById($attachTreeId);
			$this->orm->product->removeFromPages($page, $product);
		}
	}


	private function handleCats(Product $product, $data)
	{

		if (isset($data['categories'])) {
			foreach ($data['categories'] as $value) {
				$data['categoryt'][$value] = 1;
			}
			unset($data['categories']);
		}

		// pro tree snippet
		if (!isset($data['categoryt'])) {
			$catIds = [];
		} else {
			$catIds = array_keys($data['categoryt']);
		}
		$this->productTreeModel->attachTo($product, $catIds);
	}


	private function handleVideos(Product $product, $data)
	{
		$tmp = array();
		if (isset($data['videoUrl'])) {
			foreach ($data['videoUrl'] as $k => $l) {
				if (\Nette\Utils\Validators::isUrl($data['videoUrl'][$k])) {
					$tmp[$data['videoSort'][$k]] = $data['videoUrl'][$k] . "|" . $data['videoName'][$k];
				}
			}
		}
		$product->videos = $tmp;
	}

	private function handleComments(Product $product, $data)
	{
		// TODO - refactor - viz handleReviews
		$this->commentService->setTable("product");

		$this->commentService->deAttachComments($product->id);

		if (isset($data['commentText'])) {
			foreach ($data['commentText'] as $commentNo => $value) {
				if ($value !== '') {
					$this->commentService->attachComments(
						$product->id,
						$commentNo,
						$data['commentName'][$commentNo],
						$data['commentDate'][$commentNo],
						$data['commentText'][$commentNo],
						$data['commentEmail'][$commentNo],
						$data['commentIsWebMaster'][$commentNo]
					);
				}
			}
		}
	}


	private function handleReviews(Product $product, $data)
	{
		if (!isset($data['reviewId'])) {
			$data['reviewId'] = [];
		}

		foreach ($product->reviews as $review) {
			if (!in_array($review->id, $data['reviewId'])) {
				$this->orm->productReview->remove($review);
			}
		}

		foreach ($data['reviewId'] as $key => $reviewId) {
			if ($reviewId) {
				$productReview = $this->orm->productReview->getById($reviewId);
			} else {
				$productReview = new ProductReview();
			}

			if ($data['reviewNameId'][$key]) {
				$productReview->user = $this->orm->user->getById($data['reviewNameId'][$key]);
			} else {
				$productReview->user = $this->orm->user->getById($data['reviewUserId'][$key]);
			}
			$productReview->product = $product;
			$productReview->isMain = $data['reviewIsMain'][$key];
			$productReview->email = $data['reviewEmail'][$key];
			$productReview->isWebMaster = $data['reviewIsWebMaster'][$key];
			$productReview->name = $data['reviewName'][$key];
			$productReview->stars = $data['reviewStars'][$key];
			$productReview->text = $data['reviewText'][$key];
		}

	}


	private function handleFiles(Product $product, $data)
	{
		$actualFiles = [];
		foreach ($product->files as $i) {
			$actualFiles[$i->file] = $i->id;
		}

		if (isset($data['fileName'])) {
			foreach ($data['fileName'] as $fileId => $f) {
//					$this->attachFile($detail->id, $file->id, $data['fileName'][$k], $file->url, $data['fileSort'][$k], $post['fileSize'][$k]);

				if (isset($actualFiles[$fileId])) {
					// edit
					$fileEdit = $this->orm->productFile->getById($actualFiles[$fileId]);
					$fileEdit->name = $data['fileName'][$fileId];
					$fileEdit->sort = $data['fileSort'][$fileId];
					$fileEdit->size = $data['fileSize'][$fileId];
					unset($actualFiles[$fileId]);
					$this->orm->persistAndFlush($fileEdit);

				} else {
					// add
//					$file = $this->orm->file->getById($fileId);
					$file = $this->orm->file->getById($fileId);

					if ($file && $file->id) {
						$newFile = new ProductFile();
						$newFile->name = $data['fileName'][$fileId];
						$newFile->sort = $data['fileSort'][$fileId];
						$newFile->size = $data['fileSize'][$fileId];
						$newFile->url = $file->url;
						$newFile->file = $fileId;

						$product->files->add($newFile);
					}
				}
			}
		}

		// remove
		foreach ($actualFiles as $fileId => $connId) {
			$file = $this->orm->productFile->getById($connId);
			$this->orm->productFile->remove($file);
		}
	}


	private function handleVariants(Product $product, $data, $userId)
	{
		if (isset($data['variants_rows'])) {

//			dump($data['variants_rows']);
//			die;

			// smazaní starych variant
			foreach ($product->variants as $variant) {
				if (!in_array($variant->id, $data['variants_rows']['id'])) {
//					$this->elasticProductService->deleteFromElastic($this->orm->getLang(), $variant);
//					$this->elasticProductService->deleteFromElastic(NULL, $variant);
					$this->orm->productVariant->remove($variant);
				}
			}

			// UPDATE AND CREATE
			$sort = 0;
			foreach ($data['variants_rows']['rowKey'] as $variantRowKey) {

				if (isset($data['variants_rows']['id'][$variantRowKey]) && $data['variants_rows']['id'][$variantRowKey]) {
					$variant = $product->variants->toCollection()->getById($data['variants_rows']['id'][$variantRowKey]);

					$variant->calibraId = empty($data['variants_rows']['calibraId'][$variantRowKey]) ? 0 : (int)$data['variants_rows']['calibraId'][$variantRowKey];

					if (isset($data['variants_rows']['vat'])) {
						$variant->vat = (float)$data['variants_rows']['vat'][$variantRowKey];
					}

					if ($variant->isQuantityDiscount) {
						$qdAmount = empty($data['variants_rows']['qdAmount'][$variantRowKey]) ? 0 : (int)$data['variants_rows']['qdAmount'][$variantRowKey];
						if ($variant->qdAmount != $qdAmount) { // zmenilo se QD mnoztvi = je potreba prepocitat

							$parentVariant = $product->variants->toCollection()->getBy([
								'novikoId' => $variant->novikoId,
								'isQuantityDiscount' => 0,
							]);
							$variant->qdAmount = $qdAmount;
							$variant->stock = ProductVariant::getQdStock($parentVariant->stock, $qdAmount);
							$variant->weight = $parentVariant->weight * $qdAmount;
							$variant->nameWeight = sprintf('%d x %s', $variant->qdAmount, StringHelper::formatUnits($parentVariant->weight));


						}
						$variant->useQuantityUserGroupDiscount = intval(!empty($data['variants_rows']['useQuantityUserGroupDiscount'][$variantRowKey]));
					}
					//$variant->ean = $data['variants_rows']['ean'][$variantRowKey];

				} else {
					$variant = new ProductVariant();
					$variant->product = $product;
					$this->orm->productVariant->attach($variant);
					$variant->createdBy = $userId;

					if (!empty($data['variants_rows']['qdNovikoID'][$variantRowKey]) && !empty($data['variants_rows']['qdAmount'][$variantRowKey])) {
						// vytvarim QD variantu - jednorazove nastavim parent Noviko ID
						$variant->isQuantityDiscount = 1;
						$variant->novikoId = $data['variants_rows']['qdNovikoID'][$variantRowKey];

						$parentVariant = $product->variants->toCollection()->getBy([
							'novikoId' => $variant->novikoId,
							'isQuantityDiscount' => 0,
						]);

						$qdAmount = (int)$data['variants_rows']['qdAmount'][$variantRowKey];
						$variant->qdAmount = $qdAmount;
						$variant->stock = ProductVariant::getQdStock($parentVariant->stock, $qdAmount);
						$variant->weight = $parentVariant->weight * $qdAmount;
						$variant->nameWeight = sprintf('%d x %s', $variant->qdAmount, StringHelper::formatUnits($parentVariant->weight));
						$variant->useQuantityUserGroupDiscount = intval(!empty($data['variants_rows']['useQuantityUserGroupDiscount'][$variantRowKey]));
						$variant->ean = $parentVariant->ean;
						$variant->vat = $parentVariant->vat;
					}
				}

				// adding variants parameters
				$variant->nameDefault = $data['variants_rows']['nameDefault'][$variantRowKey];
				$variant->priceDPH = $data['variants_rows']['priceDPH'][$variantRowKey];

				if ($product->mutation->id == Mutation::ID_CS_DEFAULT) {
					$variant->compensation = empty($data['variants_rows']['compensationId'][$variantRowKey]) || !empty($data['variants_rows']['compensationDelete'][$variantRowKey]) ? null : (int)$data['variants_rows']['compensationId'][$variantRowKey];

					// klony
					$clones = $this->orm->productVariant->findBy([ // vsechny klony varianty
						'novikoId' => $variant->novikoId,
						'product->mutation!=' => Mutation::ID_CS_DEFAULT,
						'isQuantityDiscount' => 0,
					]);

					foreach ($clones as $clone) {
						if ($variant->compensation) {
							$compensationClone = $this->orm->productVariant->getBy([ // hledam nahradu v mutaci klonu
								'novikoId' => $variant->compensation->novikoId,
								'product->mutation' => $clone->product->mutation,
								'isQuantityDiscount' => 0,
							]);
							if ($compensationClone) {
								$clone->compensation = $compensationClone->id;
								$this->orm->productVariant->persistAndFlush($clone);
							}
						} else {
							if ($clone->compensation) {
								$clone->compensation = null;
								$this->orm->productVariant->persistAndFlush($clone);
							}
						}
					}
				}

				// Noviko update - rewriteable data
				//$variant->code = $data['variants_rows']['code'][$variantRowKey];
				//$variant->weight = $data['variants_rows']['weight'][$variantRowKey];
				//$variant->nameWeight = $data['variants_rows']['nameWeight'][$variantRowKey];
				//$variant->stock = $data['variants_rows']['stock'][$variantRowKey];

				if (isset($data['variants_rows']['isFreeTransport'][$variantRowKey]) && $data['variants_rows']['isFreeTransport'][$variantRowKey]) {
					$variant->isFreeTransport = 1;
				} else {
					$variant->isFreeTransport = 0;
				}

				if (isset($data['variants_rows']['isSubscription'][$variantRowKey]) && $data['variants_rows']['isSubscription'][$variantRowKey]) {
					$variant->isSubscription = 1;
				} else {
					$variant->isSubscription = 0;
				}

				if (isset($data['variants_rows']['isOversize'][$variantRowKey]) && $data['variants_rows']['isOversize'][$variantRowKey]) {
					$variant->isOversize = 1;
				} else {
					$variant->isOversize = 0;
				}


				// flags
				if (isset($data['variants_rows']['flags'][$variantRowKey])) {
					$variant->flagsJson = json_encode($data['variants_rows']['flags'][$variantRowKey]);
				} else {
					$variant->flagsJson = NULL;
				}

				if (isset($data['variants_rows']['active'][$variantRowKey]) && $data['variants_rows']['active'][$variantRowKey]) {
					$variant->active = 1;
				} else {
					$variant->active = 0;
				}

				if (isset($data['variants_rows']['isOpenBox'][$variantRowKey]) && $data['variants_rows']['isOpenBox'][$variantRowKey]) {
					$variant->isOpenBox = 1;
				} else {
					$variant->isOpenBox = 0;
				}

				$nameWeightSet = false;
				if (isset($data['variants_rows']['nameWeight'][$variantRowKey])) {
					$variant->nameWeight = $data['variants_rows']['nameWeight'][$variantRowKey];
					$nameWeightSet = strlen($data['variants_rows']['nameWeight'][$variantRowKey]) > 0;
				}

				if (isset($data['variants_rows']['weight'][$variantRowKey])) {
					$weight = filter_var($data['variants_rows']['weight'][$variantRowKey], FILTER_VALIDATE_FLOAT);
					$variant->weight = $weight === false ? null : $weight;
					if (!$nameWeightSet && $variant->weight !== null) {
						$variant->nameWeight = StringHelper::formatUnits($variant->weight);
					}
				}



				$variant->sort = $sort;

				// save parameters
				$i = 1;
				if (isset($data['variants_rows']['parameter'][$variantRowKey])) {
					foreach ($data['variants_rows']['parameter'][$variantRowKey] as $parameterValueId) {
						if ($parameterValueId) {

							$propertyName = 'param' . $i . 'Value';
							$parameterValue = $this->orm->parameterValue->getById($parameterValueId);
							$variant->$propertyName = $parameterValue;
						}
						$i++;
					}
				}

				//$this->handleDiscount($variant, $variantRowKey, $data);


				$this->orm->productVariant->persist($variant);
				$sort++;


//				$this->handleSupplies($variant, $data['variants_rows']['supply'][$variantRowKey]);
			}

			// pokud po aktualizacich nejsou zadne varianty -> zaloz shell variantu
			if (!$product->variants->count()) {
				$shellVariant = $this->productVariantModel->createEmpty($userId);
				$product->variants->set([$shellVariant]);
				$this->orm->product->persist($product);
			}
//
//			foreach ($product->variants as $key => $variant) {
//				$this->handleSupplies($variant, $data['variants_rows']['supply'][$data['variants_rows']['rowKey'][$key]]);
//			}

		} else {
			// nedoslo nic ?
			// vechno smat a nastave shell variantu
			foreach ($product->variants as $variant) {
				$this->orm->productVariant->remove($variant);
			}

			$shellVariant = $this->productVariantModel->createEmpty($userId);
			$product->variants->set([$shellVariant]);
			$this->orm->product->persist($product);

		}

	}


	private function handleSupplies(ProductVariant $variant, $data)
	{
		$activeSuppliesId = [];

		foreach ($data['supplyId'] as $key => $supplyId) {
			if ($supplyId) {
				$supply = $this->orm->supply->getById($supplyId);
			} else {
				// new supply
				$supply = new Supply();

			}
			$supply->variant = $variant;
			$supply->stock = $data['stockId'][$key];
			$supply->amount = $data['amount'][$key];
			$this->orm->persist($supply);

			$activeSuppliesId[] = $supply->id;
		}

		$supplyToDelete = $this->orm->supply->findBy([
			'id!=' => $activeSuppliesId,
			'variant' => $variant
		]);

		foreach ($supplyToDelete as $supply) {
			$this->orm->supply->remove($supply);
		}
	}


	private function handleImages(Product $product, $data)
	{
		$actualImages = [];
		foreach ($product->images as $i) {
			$actualImages[$i->image] = $i->id;
		}

		//bd($actualImages);
		//bd("imageName", $data['imageName']);

		// razeni - nacteni puvodniho poradi
		if (isset($data['imageName'])) {
			foreach ($data['imageName'] as $imageId => $f) {
				if (isset($actualImages[$imageId])) {
					// edit
					$imageEdit = $this->orm->productImage->getById($actualImages[$imageId]);
					$imageEdit->name = $data['imageName'][$imageId];
					$imageEdit->sort = $data['imageSort'][$imageId];

					if (isset($data['imageVariant'][$imageId])) {
						$imageEdit->variants = implode('|', $data['imageVariant'][$imageId]);
					} else {
						$imageEdit->variants = NULL;
					}

					unset($actualImages[$imageId]);
					$this->orm->persistAndFlush($imageEdit);

				} else {
					// add
					$image = $this->orm->libraryImage->getById($imageId);
					if ($image && $image->id) {
						$newImage = new ProductImage();
						$newImage->image = $image->id;
						$newImage->url = $image->url;
						$newImage->name = $data['imageName'][$imageId];
						$newImage->sort = $data['imageSort'][$imageId];
						$product->images->add($newImage);
					}
				}
			}
		}

		// remove
		foreach ($actualImages as $imageId => $connId) {
			$image = $this->orm->productImage->getById($connId);
			$this->orm->productImage->remove($image);
		}
	}


	private function handleDiscount(ProductVariant $variant, $variantRowKey, $data)
	{

		die; // vypnuto u varianty

		if (isset($data['variants_rows']['discount'][$variantRowKey]['id'])) {


			// smza ty ktere nedosly
			foreach ($variant->discounts as $discount) {
				if (!in_array($discount->id, $data['variants_rows']['discount'][$variantRowKey]['id']))
					$this->orm->remove($discount);
			}


			foreach ($data['variants_rows']['discount'][$variantRowKey]['id'] as $discountRowKey => $discountId) {

				$priceDPH = $data['variants_rows']['discount'][$variantRowKey]['priceDPH'][$discountRowKey];
				$percent = $data['variants_rows']['discount'][$variantRowKey]['percent'][$discountRowKey];


				if ($discountId) {
					$discount = $this->orm->discount->getById($discountId);

				} else {
					$discount = new ProductDiscount();
				}

				if (!$priceDPH && !$percent) {
					if ($discount->isPersisted()) {
						$this->orm->remove($discount);
					}
					continue;
				}


				$discount->variant = $variant;
				$discount->type = 'general';

				$discount->priceDPH = $priceDPH;

				$discount->percent = $percent;
				$discount->active = (isset($data['variants_rows']['discount'][$variantRowKey]['active'][$discountRowKey])) ? 1 : 0;

				if ($data['variants_rows']['discount'][$variantRowKey]['from'][$discountRowKey]) {
					$data['variants_rows']['discount'][$variantRowKey]['from'][$discountRowKey] .= ":00:00";
				}

				if ($data['variants_rows']['discount'][$variantRowKey]['to'][$discountRowKey]) {
					$data['variants_rows']['discount'][$variantRowKey]['to'][$discountRowKey] .= ":00:00";
				}

				$discount->from = ($data['variants_rows']['discount'][$variantRowKey]['from'][$discountRowKey] ? $data['variants_rows']['discount'][$variantRowKey]['from'][$discountRowKey] : null);
				$discount->to = ($data['variants_rows']['discount'][$variantRowKey]['to'][$discountRowKey]) ? $data['variants_rows']['discount'][$variantRowKey]['to'][$discountRowKey] : null;

				if (!$discount->priceDPH && !$discount->percent) {
					$this->orm->discount->remove($discount);
				}

				$this->orm->persist($discount);
			}
		} else {
			foreach ($variant->discounts as $discount) {
				$this->orm->remove($discount);
			}
		}
	}


	private function handleLinks(Product $product, $data)
	{
		$tmp = array();
		if (isset($data['linkUrl'])) {
			foreach ($data['linkUrl'] as $k => $l) {
				if (\Nette\Utils\Validators::isUrl($data['linkUrl'][$k])) {
					$tmp[$data['linkSort'][$k]] = $data['linkUrl'][$k] . '|' . $data['linkName'][$k] . '|' . isset($data['linkOpen'][$k]);
				}
			}
		}
		$product->links = $tmp;
	}

	private function handleParameters(Product $product, array $data, bool $hasStandardParameters)
	{
		if (!$hasStandardParameters) {
			return;
		}

		$clones = $product->clones->toCollection()->fetchAll(); // Mutace - klony

		//bd(['----- start handle Parameters ----------', 'clones count: ' . count($clones)]);
		foreach ($product->getParametersValues() as $parameterId => $parametersValues) {
			if (!isset($data['parameterValueId'][$parameterId])) {
				foreach ($parametersValues as $parametersValue) {
					$this->parameterValueModel->removeValue($parametersValue, $product);

					foreach ($clones as $clone) { // Mutace - klony
						if ($parametersValue->parameter->uid == Parameter::UID_PRODUCT_TYPE && in_array($parametersValue->id, [ParameterValue::ID_FREE_SAMPLE, ParameterValue::ID_TASTING_SET])) {
							// dummy
							//bd(['removeValue', 'dummy']);
						} else {
							//bd(['removeValue', 'mutation ' . $clone->mutation->id, $parametersValue->id]);
							$this->parameterValueModel->removeValue($parametersValue, $clone); // vzorek a sada se u klonu neodebere
						}
					}
				}
			}
		}

		if (isset($data['parameterValueId'])) {
			foreach ($data['parameterValueId'] as $parameterId => $parameterValuesData) {
				$parameter = $this->orm->parameter->getById($parameterId);

				if ($parameter?->uid === 'virtualFlag') {
					continue;
				}

				if (!$parameter->isSimple) {
					if ($parameterValuesData) {

						$parameterValue = $this->orm->parameterValue->getById($parameterValuesData);
						if ($parameterValue instanceof ParameterValue && isset($parameterValue->parameter) && $parameterValue->parameter->uid == "productLine") {
							$this->purchasableVetDietCheck($product, $parameterValue);
						}

						if ($parameter->type == 'select') {
							$this->parameterValueModel->handleParameterValuesSelectAttachment($product, $parameter, $parameterValuesData);

							foreach ($clones as $clone) { // Mutace - klony
								$this->parameterValueModel->handleParameterValuesSelectAttachment($clone, $parameter, $parameterValuesData);
							}
						} elseif ($parameter->type == 'multiselect') {
							$this->parameterValueModel->handleParameterValuesMultiSelectAttachment($product, $parameter, $parameterValuesData);

							foreach ($clones as $clone) { // Mutace - klony
								$this->parameterValueModel->handleParameterValuesMultiSelectAttachment($clone, $parameter, $parameterValuesData, true);
							}
						}
					} else {
						// smazat hodnoty selectu/multiselectu
						$this->parameterModel->removeParameterFrom($product, $parameter);

						foreach ($clones as $clone) { // Mutace - klony
							//bd(['removeParameterFrom', 'mutation ' . $clone->mutation->id]);
							$this->parameterModel->removeParameterFrom($clone, $parameter);
						}
					}
				}
			}
		}

		if (isset($data['parameterValue'])) {
			foreach ($data['parameterValue'] as $parameterId => $parameterValuesData) {
				$parameter = $this->orm->parameter->getById($parameterId);

				$parameterValueId = null;
				if (isset($data['parameterValueId'][$parameterId])) {
					$parameterValueId = $data['parameterValueId'][$parameterId];
				}

				$this->parameterValueModel->handleParameterValuesAttachment($product, $parameter, $parameterValueId, $parameterValuesData);

				foreach ($clones as $clone) { // Mutace - klony
					//bd(['handleParameterValuesAttachment', 'mutation ' . $clone->mutation->id, $parameter, $parameterValueId, $parameterValuesData]);
					$this->parameterValueModel->handleParameterValuesAttachment($clone, $parameter, $parameterValueId, $parameterValuesData);
				}
			}
		}

		$this->handleParamIcons($product, $data, 'detail');
		$this->handleParamIcons($product, $data, 'list');
	}

	/**
	 * @param Product $product
	 * @param array $data
	 * @param bool $hasStandardParameters
	 */
	private function handlePartnerParameters(Product $product, array $data, bool $hasStandardParameters)
	{
		if ($hasStandardParameters) {
			return;
		}

		$parametersValue = $this->orm->parameterValue->getById(ParameterValue::ID_FREE_SAMPLE);
		if (empty($data['isSample'])) {
			$this->parameterValueModel->removeValue($parametersValue, $product);
		} else {
			$this->parameterValueModel->addValue($parametersValue, $product);
		}

		$parametersValue = $this->orm->parameterValue->getById(ParameterValue::ID_TASTING_SET);
		if (empty($data['isTastingSet'])) {
			$this->parameterValueModel->removeValue($parametersValue, $product);
		} else {
			$this->parameterValueModel->addValue($parametersValue, $product);
		}
	}

	private function handleParamIcons(Product $product, $data, $type)
	{
		$key = 'showParam' . ucfirst($type);
		if (!isset($data[$key])) {
			$data[$key] = [];
		}

		$inserted = [];
		foreach ($product->paramsIcons as $p) {
			if ($type == "detail" && $p->detail == 1) {
				$inserted[$type][$p->parameter->id] = 1;
				if (!isset($data[$key][$p->parameter->id])) {
					$this->orm->paramsIcon->remove($p);
				}
			}

			if ($type == "list" && $p->list == 1) {
				$inserted[$type][$p->parameter->id] = 1;
				if (!isset($data[$key][$p->parameter->id])) {
					$this->orm->paramsIcon->remove($p);
				}
			}

		}

		// vlozeni novych
		foreach ($data[$key] as $pId => $ii) {
			if (!isset($inserted[$type][$pId])) {
				$ppI = new ProductParameterIcon();
				$ppI->product = $product;
				$ppI->parameter = $pId;
				if ($type == "detail") {
					$ppI->detail = 1;
					$ppI->list = 0;
				} else {
					$ppI->detail = 0;
					$ppI->list = 1;
				}
			}
		}
	}


	private function handleContents(Product $product, $data)
	{
		if (!isset($data['contentId'])) {
			$data['contentId'] = [];
		}

		foreach ($product->contents as $content) {
			if (!in_array($content->id, $data['contentId'])) {
				$this->orm->productContent->remove($content);
			}
		}

		$maxSort = 0;
		foreach ($data['contentId'] as $key => $contentId) {
			if ($maxSort < $data['contentSort'][$key]) {
				$maxSort = $data['contentSort'][$key];
			}

			if ($contentId) {
				$productContent = $this->orm->productContent->getById($contentId);
				$productContent->sort = $data['contentSort'][$key];

				$imageKeyId = $contentId;

			} else {
				$productContent = new ProductContent();
				$productContent->createdTime = new \DateTime();
				$imageKeyId = $key;
				$maxSort++;
				$productContent->sort = $maxSort;
			}

			if ($data['contentAuthorId'][$key]) {
				$productContent->created = $data['contentAuthorId'][$key];
			}

			$productContent->product = $product;
			$productContent->name = $data['contentName'][$key];
			$productContent->count = $data['contentCount'][$key];
//			$productContent->text = $data['contentText'][$key];

			if (isset($data['imageNameSpecial'][$imageKeyId])) {
				foreach ($data['imageNameSpecial'][$imageKeyId] as $imageId => $imageName) {
					$productContent->image = $imageId;
				}
			}

			if (isset($data['imageDelete'][$imageKeyId])) {
				$productContent->image = NULL;
			}
		}
	}


	public function handleFlags(Product $product)
	{
		$product->flushParam();
		foreach ($product->variants as $variant) {
			$variant->priceDPHRange = $variant->priceFinalDPH;
			$variant->isInStore = ($variant->totalSupplyCount && $variant->totalSupplyCount > 0) ? 1 : 0;
			$variant->isInDiscount = (int)($variant->priceFinalDPH != $variant->priceDPH);
//			$variant->isFreeTransport = $product->isFreeTransport;

			$variant->isNewBox = (int)!$variant->isOpenBox;
		}


		if (is_array($this->configService->get('flagToVariantRow'))) {
			foreach ($this->configService->get('flagToVariantRow') as $flag) {
				foreach ($product->variants as $variant) {
					$variant->$flag = $product->$flag;
				}
			}
		}

		if (is_array($this->configService->get('parametersToVariantRow'))) {
			foreach ($this->configService->get('parametersToVariantRow') as $parameterUid) {

				$parameterValues = $product->getParameterValueByUid($parameterUid);

				foreach ($product->variants as $variant) {

					$variant->priceDPHRange = $variant->priceFinalDPH;
					if (isset($parameterValues->value)) {
						$variant->$parameterUid = $parameterValues->value;
					} else {
						$variant->$parameterUid = null;
					}
				}
			}
		}

		if (is_array($this->configService->get('parametersToProductRow'))) {
			foreach ($this->configService->get('parametersToProductRow') as $parameterUid) {
				$parameterValues = $product->getParameterValueByUid($parameterUid);

				if (isset($parameterValues->value)) {

					$product->$parameterUid = $parameterValues->value;
				} else {
					$product->$parameterUid = null;
				}
			}
		}

		$product->mainCategoryUID = null;


		$mainPageId = null;
		if (isset($product->path[2])) {
			$mainPageId = $product->path[2];
		} elseif (isset($product->path[1])) {
			$mainPageId = $product->path[1];
		}

		if ($mainPageId) {
			$this->orm->tree->setPublicOnly(false);
			$mainCat = $this->orm->tree->getById($mainPageId);
			if ($mainCat) {
				$product->mainCategoryUID = $mainCat->uid;
			}
			$this->orm->tree->setPublicOnly(true);
		}


//		$product->isInStore = 0;
//		$firstVariant = $product->variants->toCollection()->fetch();
//		if ($firstVariant->totalSupplyCount) {
//			$product->isInStore = (bool)$firstVariant->totalSupplyCount;
//		}

//		$product->isInDiscount = 0;
//		if ($firstVariant->hasDiscount) {
//			$product->isInDiscount = (bool)$firstVariant->totalSupplyCount;
//		}
	}


	private function handleArticles(Product $product, $data)
	{
		if (isset($data['articlePages'])) {

			$data['articlePages'] = array_unique($data['articlePages']);

			// smaze stare nepouzite
			foreach ($this->orm->productPage->findBy(['type' => ProductPage::TYPE_ARTICLE, 'product' => $product]) as $articlePageRel) {
				if (!in_array($articlePageRel->tree->id, $data['articlePages'])) {
					// delete relation
					$this->orm->productPage->remove($articlePageRel);
				}
			}
			// aktualizuje poradi a vytvori nove
			$sort = 0;
			foreach ($data['articlePages'] as $articlePageId) {
				if ($articlePageId) {

					$articlePageRel = $this->orm->productPage->findBy(['type' => ProductPage::TYPE_ARTICLE, 'product' => $product])->getBy(['tree' => $articlePageId]);
					//budu zakladat novy
					if (!$articlePageRel) {
						$articlePageRel = new ProductPage();
						$this->orm->productPage->attach($articlePageRel);

						$articlePageRel->product = $product;
						$articlePageRel->tree = $articlePageId;
					}
					$articlePageRel->type = ProductPage::TYPE_ARTICLE;

					$articlePageRel->sort = $sort;
					$this->orm->productPage->persist($articlePageRel);
					$sort++;
				}
			}

		} else {
			// smaze stare nepouzite
			foreach ($this->orm->productPage->findBy(['type' => ProductPage::TYPE_ARTICLE, 'product' => $product]) as $articlePageRel) {
				// delete relation
				$this->orm->productPage->remove($articlePageRel);
			}
		}
	}


	/**
	 * @param Product $product
	 * @return Mutation[]|ICollection
	 */
	public function getMutationsPossibleForCloning(Product $product)
	{
		$clones = $product->masterParent->clones->toCollection()->fetchPairs(null, 'mutation');
		$mutations = [Mutation::ID_CS_DEFAULT, $product->mutation->id]; // klonovat nelze na CS a vlasntí mutaci
		foreach ($clones as $mutation) {
			$mutations[] = $mutation->id;
		}
		$mutations = array_unique($mutations);
		return $this->orm->mutation->findBy(['id!=' => $mutations]);
	}


	public function cloneIntoNewMutation(Product $originProduct, Mutation $mutation, int $idUserCreated): Product
	{
		$product = new Product();
		$this->orm->product->attach($product);

		$product->mutation = $mutation;
		$product->public = 0;
		$product->parent = $originProduct->isMaster ? $originProduct : $originProduct->parent; // klon z klonu má parenta master ID

		$simpleProperties = ['name', 'nameLang', 'nameTitle', 'description', 'annotation', 'annotationBox', 'content', 'hideFirstImage', 'isSale', 'customFieldsJson'];
		$this->orm->copyScalarProperties($originProduct, $product, [], $simpleProperties);

		// variants
		$variantOriginIds = [];
		foreach ($originProduct->variants as $variant) {
			if ($variant->isQuantityDiscount > 0) {
				continue;
			}

			$productVariant = new ProductVariant();

			//m:1 relations
			$productVariant->product = $variant->product;
			$productVariant->param1Value = $variant->param1Value;
			$productVariant->param2Value = $variant->param2Value;

			//non-relation properties
			$this->orm->copyScalarProperties($variant, $productVariant);

			$productVariant->active = 1;
			$productVariant->priceDPH = 0;
			$productVariant->retailPriceVat = 0;
			$productVariant->retailPrice = 0;
			$productVariant->vat = $mutation->vatDefault;
			$productVariant->stock = 0;
			$productVariant->edited = new DateTimeImmutable();
			$productVariant->editedBy = null;
			$productVariant->novikoEdited = null;
			$productVariant->created = new DateTimeImmutable();
			$productVariant->createdBy = $idUserCreated;

			// find compensation in the new mutation
			if (isset($variant->compensation)) {
				$compensationClone = $this->orm->productVariant->getBy([
					'novikoId' => $variant->compensation->novikoId,
					'product->mutation' => $mutation,
					'isQuantityDiscount' => 0,
				]);

				$productVariant->compensation = $compensationClone;
			}

			//copy supplies
			if ($variant->supplies->count() > 0) {
				foreach ($variant->supplies as $supply) {
					$newSupply = new Supply();
					$newSupply->amount = $supply->amount;
					$newSupply->stock = $supply->stock;
					$productVariant->supplies->add($newSupply);
				}
			}

			//prices, discounts and presents are not copied

			$product->variants->add($productVariant); // attach to wrap
			$this->orm->product->persist($product);
			$variantOriginIds[$variant->id] = $productVariant->id;
		}

		// images
		foreach ($originProduct->images as $image) {
			$productImage = new ProductImage();
			$this->orm->copyScalarProperties($image, $productImage);

			if (strlen($image->variants ?? '') > 0) {
				$iVariants = explode('|', $image->variants);
				foreach ($iVariants as &$iVar) {
					$iVar = $variantOriginIds[$iVar];
				}

				$productImage->variants = implode('|', $iVariants);
			}

			$product->images->add($productImage);
		}

		// alias - must be set after persisting product
		$product->alias = $originProduct->name; //phpcs:ignore - alias has special setter

		// parameters
		foreach ($originProduct->parametersValues as $parameterValue) {
			$this->parameterValueModel->addValue($parameterValue, $product);
		}

		// alt. products
		$sort = 10000;
		foreach ($originProduct->similarProducts as $similarProduct) {
			$clone = $similarProduct->masterParent->clones->toCollection()->getBy([
				'mutation' => $mutation,
			]);

			if (isset($clone)) {
				$this->orm->product->addToSimilarProducts($product, $clone, $sort);
				$sort++;
			}
		}

		$this->orm->product->persistAndFlush($product);

		// add to elastic
		$this->addProduct($product);

		return $product;
	}

	/**
	 * Obsluha kompenzaci pro novy klon - zpetne doplneni u varianty, pokud obsahuje kompenzaci, ktera ale v klonovane mutaci jeste neexistovala
	 * Je potreba volat az po realnem vzniku klonu!!
	 *
	 * @param Product $newProduct nove vznikly klon
	 */
	public function handleNewCloneCompensation(Product $newProduct)
	{
		foreach ($newProduct->variants as $variant) {
			if ($variant->isQuantityDiscount > 0 || $variant->novikoId === null) {
				continue;
			}

			// najdu vsechny CS varianty, ktere maji jako nahradu nove vznikly klon
			$originVariantsWithCompensation = $this->orm->productVariant->findBy([
				'compensation->novikoId' => $variant->novikoId,
				'product->mutation' => Mutation::ID_CS_DEFAULT,
			]);

			foreach ($originVariantsWithCompensation as $originVariantWithCompensation) {
				// najdu mutaci varianty, ktera by mela mit jako nahradu nove vznikly klon (= tzn. jeste ho nema)
				$mutationVariantWithoutCompensation = $this->orm->productVariant->getBy([
					'novikoId' => $originVariantWithCompensation->novikoId,
					'product->mutation' => $newProduct->mutation,
					'compensation' => null,
				]);

				//	dump(['$mutationVariantWithCompensation' => $mutationVariantWithoutCompensation]);
				if ($mutationVariantWithoutCompensation) { // a nastavim nahradu
					$mutationVariantWithoutCompensation->compensation = $variant;
					$this->orm->productVariant->persistAndFlush($mutationVariantWithoutCompensation);
				}
			}
		}
	}

	public function addProduct(Product $product)
	{
		$this->allElasticFacade->updateNow($product);
		$this->productElasticFacade->updateAllMutationsNow($product);
	}


	public function updateProduct(Product $product)
	{
		$this->allElasticFacade->updateNow($product);
		$this->productElasticFacade->updateAllMutationsNow($product);
	}

	public function handleFlagsVariant(array $flags, Product $product): void
	{
		$flagEntities = $this->orm->flag->findBy(['id' => $flags]);
		$connectedParameterValuesToFlags = [];
		foreach ($flagEntities as $flagEntity) {
			if ($flagEntity->virtualFlag !== null) {
				$connectedParameterValuesToFlags[] = $flagEntity->virtualFlag;
			}
		}

		$virtualFlagParameter = $this->orm->parameter->getBy(['uid' => 'virtualFlag']);
		if (isset($virtualFlagParameter)) {
			$this->parameterValueModel->handleParameterValuesMultiSelectAttachment($product, $virtualFlagParameter, $connectedParameterValuesToFlags);
		}
	}


	private function getFlagsGrouped(array $variants_rows, Product $product): array
	{
		$flagsGrouped = [];

		foreach ($variants_rows['rowKey'] as $variantRowKey) {
			// Simple flags -> virtualFlag parameter values
			if (($variants_rows['active'][$variantRowKey] ?? 0) == 1) {
				if (isset($variants_rows['flags'][$variantRowKey])) {
					foreach ($variants_rows['flags'][$variantRowKey] as $flagId) {
						$flagsGrouped[$flagId] = $flagId;
					}
				}

				// Unique virtualFlags parameter value
				if (($variants_rows['isFreeTransport'][$variantRowKey] ?? 0) == 1) {
					$transportFreeFlagId = $this->orm->flag->getBy([
						'type' => Flag::TYPE_TRANSPORT_FREE,
						'mutation' => $product->mutation,
					])?->id;

					if (isset($transportFreeFlagId)) {
						$flagsGrouped[$transportFreeFlagId] = $transportFreeFlagId;
					}
				}
			}
		}

		// Maybe TODO flags connected to gift/discount
		//		foreach ($product->activeVariants as $variant) {
		//			$variant->isInDiscount
		//		}

		return $flagsGrouped;
	}

}


