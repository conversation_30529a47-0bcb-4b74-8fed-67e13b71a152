<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\Orm\TraitsEntity\HasCustomContent;
use App\Model\Orm\TraitsEntity\HasCustomFields;
use App\Model\Orm\TraitsEntity\HasFormDefaultData;
use App\Model\Orm\TraitsEntity\HasParameterRepository;
use App\Model\Orm\TraitsEntity\HasParameters;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\ConfigService;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\ParamText;
use function count;

/**
 * @property int $id {primary}
 * @property string|null $pathString
 * @property string|null $template {default ''}
 * @property int|null $showOnHomepage
 * @property int $last
 * @property int|null $level
 * @property int|null $sort
 * @x_property string|null $sortTmp
 * @property int $public {default 0}
 * @property int|null $created
 * @property int|null $edited
 * @property int|null $hideFirstImage
 * @property int|null $hideBigImage
 * @property int|null $hideInSearch {default 0}
 * @property int|null $hideInSitemap {default 0}
 * @property int $hideCrossroad {default 0}
 * @property int $priorityArticle {default 0}
 * @property int|null $showContactForm
 * @property string $uid {default ''}
 * @property string $name {default ''}
 * @property string $nameTitle {default ''}
 * @property string $nameAnchor {default ''}
 * @property string|null $annotation
 * @property string|null $description
 * @property string|null $keywords
 * @property string|null $content
 * @property DateTimeImmutable $createdTime {default 'now'}
 * @property DateTimeImmutable $createdTimeOrder {default 'now'}
 * @property DateTimeImmutable|null $editedTime
 * @x_property DateTimeImmutable|null $sync
 * @property DateTimeImmutable $publicFrom {default 'now'}
 * @property DateTimeImmutable $publicTo {default '+100 year'}
 * @property string|null $linksString
 * @property string|null $videosString
 * @property stdClass $customFieldsJson {container CfJsonContainer}
 * @property array $customContentSchemeJson {container JsonArrayContainer}
 * @property stdClass $customContentJson {container CfJsonContainer}
 * @property string|null $seoTitleFilter
 * @property string|null $seoAnnotationFilter
 * @property string|null $seoDescriptionFilter
 * @property int|null $productAttachedId
 * @property int|null $forceNoIndex {default 0}
 * @property string|null $icon {default ''}
 *
 *
 * RELATIONS
 * @property Tree[]|OneHasMany $crossroadAll {1:m Tree::$parent, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property TreeImage[]|OneHasMany $images {1:m TreeImage::$tree, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property TreeFile[]|OneHasMany $files {1:m TreeFile::$tree, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property TreeComment[]|OneHasMany $comments {1:m TreeComment::$tree, orderBy=[date=ASC], cascade=[persist, remove]}
 * @property TreeFaq[]|OneHasMany $faqs {1:m TreeFaq::$tree, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property TreeReview[]|OneHasMany $reviews {1:m TreeReview::$tree, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property ParameterValue[]|ManyHasMany $parametersValues {m:m ParameterValue::$trees, isMain=true, orderBy=[parameterSort=ASC, sort=ASC]}
 * @property ProductTree[]|OneHasMany $productTrees {1:m ProductTree::$tree, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property Tree|null $parent {m:1 Tree::$crossroadAll}
 * @property Tree[]|OneHasMany $itemsByAuthor {1:m Tree::$authorR, orderBy=[sort=ASC], cascade=[persist]}
 * @property Tree|null $authorR {m:1 Tree::$itemsByAuthor}
 * @property OneHasMany|ProductPage[] $productPages {1:m ProductPage::$tree}
 *
 *
 * VIRTUAL
 * @property array|null $path {virtual}
 * @property string $modul {virtual}
 * @property Alias|null $alias {virtual}
 * @property AliasHistory[]|null $aliasHistory {virtual}
 * @property string|null $aliasHistoryString {virtual}
 * @property Tree[]|ICollection $crossroad {virtual}
 * @property array|null $links {virtual}
 * @property array|null $videos {virtual}
 * @property stdClass $cf {virtual}
 * @property stdClass $cc {virtual}
 * @property string|null $firstImage {virtual}
 * @property array $childsId {virtual}
 * @property Tree[]|ICollection $crossroadCats {virtual}
 * @property Tree[]|ICollection $crossroadItems {virtual}
 * @property Tree[]|ICollection $crossroadItemsArticles {virtual}
 * @property string $crossroadItemsNoSort {virtual}
 * @property Product[]|null $products {virtual}
 * @property Product[]|null $productsAll {virtual}
 * @property Product[]|null $productsReview {virtual}
 * @property Tree[]|null $pages {virtual}
 * @property string|null $parentId {virtual}
 * @property Tree|null $author {virtual}
 * @property string|null $items {virtual}
 * @property string|null $readingTime {virtual}
 * @x_property Tree|null $prev {virtual}
 * @x_property Tree|null $next {virtual}
 * @property array|null $paramLinks {virtual}
 * @property array|null $rating {virtual}
 * @property-read array $possibleTemplates {virtual}
 * @property-read array|null $possibleTemplatesForAdmin {virtual}
 * @property-read array $templates {virtual}
 * @property-read bool $hasBadTemplate {virtual}
 * @property-read Parameter[]|ICollection $getParametersInRS {virtual}
 * @property array|null $pathItems {virtual}
 * @property-read bool $isBlog {virtual}
 */
final class Tree extends Entity
{

	use HasParameters;
	use HasFormDefaultData;
	use HasCustomFields;
	use HasParameterRepository;
	use hasOrmTrait;
	use HasCustomContent;

	/** @var  UserRepository */
	private $userRepository;

	private $productRepository;

	private $libraryImageRepository;

	/** @var  ConfigService */
	private $configService;

	private $siblings = null;

	/** @var  \App\Model\TreeModel */
	private $treeModel;

	/** @var  \App\Model\AliasModel */
	private $aliasModel;

	/** @var  \App\Model\AliasHistoryModel */
	private $aliasHistoryModel;

	/** @var ParamText */
	private $paramText;

	protected TreeRepository $treeRepository;

	public function injectService(ProductRepository $productRepository, LibraryImageRepository $libraryImageRepository,
	                              ConfigService $configService, TreeModel $treeModel, AliasModel $aliasModel, AliasHistoryModel $aliasHistoryModel,
									ParamText $paramText, UserRepository $userRepository, TreeRepository $treeRepository)
	{
		$this->productRepository = $productRepository;
		$this->libraryImageRepository = $libraryImageRepository;
		$this->configService = $configService;
		$this->userRepository = $userRepository;
		$this->treeModel = $treeModel;
		$this->aliasModel = $aliasModel;
		$this->aliasHistoryModel = $aliasHistoryModel;
		$this->paramText = $paramText;
		$this->treeRepository = $treeRepository;
	}

	public function flushCache(): void
	{
		$this->cache = [];
	}


	/**
	 * @return Alias|null
	 */
	protected function getterAlias()
	{

		if (!$this->isPersisted()) {
			return null;
		}


		if (!isset($this->cache['alias'])) {
			/** @var \App\Model\AliasRepository $aliasRepository */
			$aliasRepository = $this->getRepository()->getModel()->alias;
			$this->cache['alias'] = $aliasRepository->getBy([
				'idref' => $this->id,
				'modul' => 'tree',
				'lg' => $this->getRootLang(),
			]);
		}

		return $this->cache['alias'];
	}


	protected function setterAlias($alias): void
	{
		if ($alias instanceof Alias) {
			$alias = $alias->alias;
		}

		$this->aliasModel->handleAliasChange($this, $alias, $this->getRepository()->getModel()->getMutation());
	}

	protected function getterPath(): array|null
	{
		if (!isset($this->id)) {
			return null;
		}

		$path = preg_split('~\|~', $this->pathString ?? '', -1, PREG_SPLIT_NO_EMPTY);
		$path = array_map('intval', $path);

		return $path;
	}

	protected function setterPath($path = [])
	{
		if ($path) {
			$this->pathString = implode('|', $path) . '|';
		} else {
			$this->pathString = null;
		}
	}


	protected function getterFirstImage()
	{
		return $this->images->toCollection()->fetch();
	}


	public function getImageByType($type = NULL, $onlyFirst = TRUE)
	{
		$ret = [];
		foreach ($this->images as $image) {
			if ($type && $image->type == $type) {
				$ret[] = $image;
			} else if (!$type && !$image->type) {
				$ret[] = $image;
			}

			if ($onlyFirst && count($ret)) {
				return $ret[0];
			}
		}
		return $ret;
	}


	protected function getterPublicFrom($publicFrom)
	{
		return new DateTimeImmutable($publicFrom->format("Y-m-d H:i:s"));
	}


	protected function getterCreatedTimeOrder($createdTimeOrder)
	{
		return new DateTimeImmutable($createdTimeOrder->format("Y-m-d H:i:s"));
	}


	protected function getterPublicTo($publicTo)
	{
//		$date = new DateTimeImmutable();
//		$date->setDate($publicTo->format('Y'), $publicTo->format('m'), $publicTo->format('d'));
//		$date->setTime($publicTo->format('H'), $publicTo->format('i'), $publicTo->format('s'));
//		return $date;
		return new DateTimeImmutable($publicTo->format("Y-m-d H:i:s"));

	}


	public function getterParentId()
	{
		if ($this->parent) {
			return $this->parent->id;
		} else {
			return NULL;
		}
	}


//	public function getterAuthorId()
//	{
//		if ($this->author) {
//			return $this->author->id;
//		} else {
//			return NULL;
//		}
//	}


	public function getterAuthor()
	{
		if ($this->authorR && $this->authorR->id) {
			$test = $this->getRepository()->findById($this->authorR->id)->getBy($this->getRepository()->getPublicOnlyWhere());
			if ($test) {
				return $test;
			} else {
				return NULL;
			}
		} else {
			return NULL;
		}
	}

	public function setAliases(string $alias, ?string $aliasHistoryString = null): void
	{
		if ($this->alias != null) {
			$oldAlias = $this->alias->alias;
			$aliasHistoryString .= "\n" . $oldAlias;
			$aliasHistoryString .= "\n" . $alias;
		}

		$this->setterAlias($alias);
		if ($aliasHistoryString) {
			$this->setterAliasHistoryString($aliasHistoryString);
		}
	}

	public function setterAliasHistoryString($aliasHistoryString)
	{
		$this->aliasHistoryModel->saveString($aliasHistoryString, $this, $this->getRepository()->getModel()->getMutation());
	}


	public function getterAliasHistory()
	{
		if (!isset($this->cache['aliasHistory'])) {
			$this->cache['aliasHistory'] = $this->getRepository()->getModel()->aliasHistory->findBy([
				'idref' => $this->id,
				'modul' => 'tree',
				'lg' => $this->getRootLang(),
			])->orderBy('id', ICollection::DESC);
		}
		return $this->cache['aliasHistory'];
	}


	public function getterAliasHistoryString()
	{
		$ret = [];
		foreach ($this->aliasHistory as $aliasHistory) {
			$ret[] = $aliasHistory->alias;
		}
		return implode("\n", $ret);
	}


	public function getCreatedBy()
	{
		return $this->userRepository->getById($this->created);
	}


	public function getEditedBy()
	{
		return $this->userRepository->getById($this->edited);
	}


	protected function getterChildsId(): array
	{
		$ret = [];
		foreach ($this->crossroadItems as $child) {
			$ret[] = $child->id;
		}
		return $ret;
	}


	protected function getterReadingTime()
	{
		$words_per_minute = 220;
		$words_per_second = $words_per_minute / 60;

//		$word_count = mb_strlen( strip_tags( $this->content ) , "UTF-8");
		$word_count = count(explode(" ", strip_tags( $this->content ?? '')));

		// How many seconds (total)?
		$seconds = floor( $word_count / $words_per_second );

		if ($seconds < 60) {
			$minutes = 0;
		} else {
			$minutes = floor($seconds/60);
		}

		return $minutes;
	}


	protected function getterCrossroadCats()
	{
		if (!isset($this->cache['getterCrossroadCats'])) {
			$this->cache['getterCrossroadCats'] = $this->getRepository()->findByParent($this->id, "Article:detail")->orderBy('sort')->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterCrossroadCats'];
	}


	protected function getterCrossroadItems()
	{

//		if (!is_numeric($id)) $id = $this->fetchByUid($id)->id;
//
//		$cfg['where']['last = %i'] = 1;
//		$cfg['where']['path LIKE %~like~'] = '|'.$id.'|';
//		$cfg['orderBy'] = isset($cfg['orderBy'])?$cfg['orderBy']:array('sort');
//		return $this->fetchAll($cfg);

		if (!isset($this->cache['getterCrossroadItems'])) {
			$this->cache['getterCrossroadItems'] = $this->getRepository()->findLastInPath($this->id)->orderBy('sort')->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterCrossroadItems'];
	}


	protected function getterCrossroadItemsArticles()
	{
		if (!isset($this->cache['getterCrossroadItemsArticles'])) {
			$this->cache['getterCrossroadItemsArticles'] = $this->getRepository()->findLastInPath($this->id, "Article:category")->orderBy('sort')->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterCrossroadItemsArticles'];
	}


	protected function getterCrossroadItemsNoSort()
	{
		if (!isset($this->cache['getterCrossroadItemsNoSort'])) {
			$this->cache['getterCrossroadItemsNoSort'] = $this->getRepository()->findLastInPath($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterCrossroadItemsNoSort'];
	}


	protected function getterLinks()
	{
		$links = $this->linksString;
		if (!isset($this->cache['links'])) {
			$rows = preg_split("~\n~", $links ?? '', -1, PREG_SPLIT_NO_EMPTY);
			$this->cache['links'] = [];
			foreach ($rows as $r) {
				$tmp = explode('|', $r);
				$this->cache['links'][] = [
					'url' => $tmp[0],
					'name' => isset($tmp[1]) ? $tmp[1] : NULL,
					'open' => isset($tmp[2]) ? $tmp[2] : NULL,
				];
			}
		}
		return $this->cache['links'];
	}


	protected function setterLinks($links)
	{
		$this->linksString = implode("\n", $links);
	}


	protected function getterVideos()
	{
		$videos = $this->videosString;
		if (!isset($this->cache['videos'])) {
			$rows = preg_split("~\n~", $videos ?? '', -1, PREG_SPLIT_NO_EMPTY);

			$iteratorArray = [];
			foreach ($rows as $r) {
				$video = explode("|", $r);

				$tmp = parse_url($video[0]);
				if (!isset($tmp['query'])) {
					continue;
				}
				parse_str($tmp['query'], $tmp);

				if (!isset($tmp['v'])) {
					continue;
				}

				$iteratorArray[] = [
					'url' => $video[0],
					'name' => isset($video[1]) ? trim($video[1]): "",
					'id' => $tmp['v'],
				];

			}
			$this->cache['videos'] = new \ArrayIterator($iteratorArray);
		}
		return $this->cache['videos'];
	}


	protected function setterVideos($videos)
	{
		$this->videosString = implode("\n", $videos);
	}


	protected function getterPrev()
	{
		$prev = NULL;
		if (isset($this->id) && isset($this->parent) && $this->parent) {
			foreach ($this->getSiblings() as $item) {
				if ($item->id != $this->id) {
					$prev = $item;
				}
				if ($item->id == $this->id) {
					break;
				}
			}
		}
		return $prev;
	}


	protected function getterNext()
	{
		$next = NULL;
		if (isset($this->id) && isset($this->parent) && $this->parent) {
			$thisIsFound = FALSE;
			foreach ($this->getSiblings() as $item) {
				if ($thisIsFound) {
					$next = $item;
					break;
				}
				if ($item->id == $this->id) {
					$thisIsFound = TRUE;
				}
			}
		}
		return $next;
	}


	private function getSiblings()
	{
		if (!is_null($this->siblings)) {
			return $this->siblings;
		} else if (isset($this->id) && isset($this->parent) && $this->parent) {
			return $this->parent->crossroad->get();
		} else {
			return [];
		}
	}


	public function setSiblings($siblings)
	{
		if (is_array($siblings)) {
			$siblings = new \ArrayIterator($siblings);
		}
		$this->siblings = $siblings;
	}


	public function getRootId()
	{
		if (($this->getRawValue('pathString') ?? '') === '') {
			return $this->id;
		}

		$path = preg_split('~\|~', $this->pathString ?? '', -1, PREG_SPLIT_NO_EMPTY);

		return (int) $path[0];
	}


	public function getRootLang(): string
	{
		return $this->getLangByTreeId($this->getRootId());
	}

	public function getLangByTreeId(int $webId): string
	{
		$configLangs = $this->configService->getParam('lang');
		foreach ($configLangs as $lgKey => $lang) {
			if ((int) $lang['treeId'] === $webId) {
				return $lgKey;
			}
		}

		return 'cs';
	}

	public function getRootMutation(): Mutation
	{
		return $this->orm->mutation->getByCodeChecked($this->getRootLang());
	}

	protected function getterPages()
	{
		if (!$this->isAttached()) {
			return new \ArrayIterator();
		}

		if (!isset($this->cache['getterPages'])) {
			$this->cache['getterPages'] = $this->getRepository()->findPagesById($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterPages'];
	}


	// u clanku - produkty zminene v clanku - jen s cenou
	protected function getterProducts()
	{
		if (!isset($this->cache['getterProducts'])) {
//			$this->cache['getterProducts'] = $this->getRepository()->findProductsById($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
			$this->cache['getterProducts'] = $this->productRepository->findProductsAttToPageById($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterProducts'];
	}

	// u clanku - produkty zminene v clanku
	protected function getterProductsAll()
	{
		if (!isset($this->cache['getterProductsAll'])) {
//			$this->cache['getterProducts'] = $this->getRepository()->findProductsById($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
			$this->cache['getterProductsAll'] = $this->productRepository->findProductsAttToPageByIdAll($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterProductsAll'];
	}


	// pro tyto produkty je to recenze
	protected function getterProductsReview()
	{
		if (!isset($this->cache['getterProductsReview'])) {
//			$this->cache['getterProducts'] = $this->getRepository()->findProductsById($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
			$this->cache['getterProductsReview'] = $this->productRepository->findProductsAttToPageReviewById($this->id)->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['getterProductsReview'];
	}


	public function getPageByUID($uid)
	{
		return $this->getRepository()->getByUid($uid, $this->getRepository()->getModel()->getMutation()?->langCode);
	}


	protected function getterModul()
	{
		return 'tree';
	}


	/**
	 * @return ICollection|Tree[]
	 */
	public function getterCrossroad()
	{
		if (!isset($this->cache['crossroad'])) {
			$this->cache['crossroad'] = $this->crossroadAll->toCollection()->findBy($this->getRepository()->getPublicOnlyWhere());
		}
		return $this->cache['crossroad'];
	}


	/**
	 * @return array
	 */
	protected function getterPossibleTemplates()
	{
		$ret = $this->templates;

		if (!in_array($this->template, $ret)) {
			// pokud nahodou dojde k tomu ze entita ma jinaci tempalte než je povovoleno
			// je nutne tento template pridat (aby naspadl formular na povolenych hodnotach atributu template)
			$ret[$this->template] = $this->configService->get('templates', $this->template);
		}

		return $ret;
	}


	/**
	 * @return array
	 */
	protected function getterPossibleTemplatesForAdmin()
	{
		$ret = null;

		// by parent UID
		if ($this->parent && $this->parent->uid) {
			$allowEditTemplateForAdmin = $this->configService->get('allowEditTemplateForAdmin');
			if (isset($allowEditTemplateForAdmin[$this->parent->uid])) {
				$ret = $allowEditTemplateForAdmin[$this->parent->uid];

				if (!isset($ret[$this->template])) {
					// pokud nahodou dojde k tomu ze entita ma jinaci tempalte než je povovoleno
					// je nutne tento template pridat (aby naspadl formular na povolenych hodnotach atributu template)
					$ret[$this->template] = $this->configService->get('templates', $this->template);
				}
			}
		}

		// by template
		$allowEditTemplateForAdmin2 = $this->configService->get('allowEditTemplateForAdminPerTemplate');
		if (isset($allowEditTemplateForAdmin2[$this->template])) {
			$ret = $allowEditTemplateForAdmin2[$this->template];

			if (!isset($ret[$this->template])) {
				// pokud nahodou dojde k tomu ze entita ma jinaci tempalte než je povovoleno
				// je nutne tento template pridat (aby naspadl formular na povolenych hodnotach atributu template)
				$ret[$this->template] = $this->configService->get('templates', $this->template);
			}
		}

		return $ret;
	}

	/**
	 * @return array
	 */
	protected function getterTemplates()
	{
		$ret = $this->configService->get('templates');

		if ($this->isPersisted() && $this->parent) {
			// pokud je entita ulozena a ma rodice
			// prekontroluje pole predpisu pro tempaltes

			$templateNames = $this->configService->get('templatesParentsRules', $this->parent->template);

			if ($templateNames && is_array($templateNames)) {
				// podle klice vyfiltruj jen ty pouzitelne
				$ret =  array_filter($ret, function ($value, $key) use ($templateNames)  {
					return (in_array($key, $templateNames));
				}, ARRAY_FILTER_USE_BOTH);
			}
		}

		return $ret;
	}


	protected function getterHasBadTemplate()
	{
		return (!isset($this->templates[$this->template]));
	}


	protected function getterGetParametersInRS()
	{
		// pro vsechny pages jsou parametry vypnute
		return $this->parameterRepository->getCatsParams('tree');
	}

	// trees, tree-$this->id


	public function onBeforeRemove(): void
	{
		$lang = $this->getRootLang();
		$this->aliasModel->removeForEntity($this, $lang);
		$this->aliasHistoryModel->removeForEntity($this, $lang);
	}





	protected function getterRating()
	{
		if (!$this->isPersisted()) {
			return [
				'stars' => 0,
				'percent' => 0,
				'count' => 0,
			];
		}

		if (!isset($this->cache['rating'])) {
			/** @var \App\Model\AliasRepository $aliasRepository */
			$res = $this->getRepository()->getRating($this);
			$stars = $res->rating;
			$rating = [
				'stars' => $stars ? $stars : 0,
				'percent' => $stars ? (int) (($stars * 100) / 5) : 0,
				'count' => $res->count,
			];
			bd($rating);
			$this->cache['rating'] = $rating;
		}

		return $this->cache['rating'];
	}


	public function getSeoTitleFilterWithReplace($cleanFilter)
	{
		return $this->paramText->getText($this->seoTitleFilter, $cleanFilter);
	}


	public function getSeoAnnotationFilterWithReplace($cleanFilter)
	{
		return $this->paramText->getText($this->seoAnnotationFilter, $cleanFilter);
	}


	public function getSeoDescriptionFilterWithReplace($cleanFilter)
	{
		return $this->paramText->getText($this->seoDescriptionFilter, $cleanFilter);
	}

	public function getMutation(): Mutation
	{
		$rootId = isset($this->path) && (count($this->path) > 0) ? $this->path[0] : $this->id;

		return $this->orm->mutation->getByChecked(['rootId' => $rootId]);
	}

	public function getPathSentence(string $sep = '>', int $fromLevel = -1): string
	{
		$parts = [];
		foreach ($this->pathItems as $pathItem) {
			if ($pathItem->level > $fromLevel) {
				$parts[] = $pathItem->name;
			}
		}

		return implode(" {$sep} ", $parts);
	}

	public function getPathSentenceWithMyself(string $sep = '>', int $fromLevel = -1): string
	{
		$prefix = $this->getPathSentence($sep, $fromLevel);
		$parts = [];
		if ($prefix) {
			$parts[] = $prefix;
		}

		$parts[] = $this->name;
		return implode(" {$sep} ", $parts);
	}

	protected function getterPathItems(): array
	{
		$ret = [];
		foreach ($this->path as $pathId) {
			$ret[] = $this->treeRepository->getById($pathId);
		}

		return $ret;
	}

	protected function getterIsBlog(): bool
	{
		$currentParent = $this->parent;
		while ($currentParent) {
			if (isset($currentParent->uid) && $currentParent->uid === 'articlesMain') {
				return true;
			}
			$currentParent = $currentParent->parent;
		}
		return false;
	}
}
