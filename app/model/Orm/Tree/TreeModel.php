<?php

namespace App\Model;

use App\Model\CustomContent\CustomContent;
use App\Model\CustomField\CustomFields;
use App\Model\ElasticSearch\All\Facade;
use Nette\SmartObject;
use Nette\Utils\DateTime;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use SuperKoderi\CommentService;
use SuperKoderi\ConfigService;
use SuperKoderi\ElasticSearch\Tree\Service;


class TreeModel
{
	use SmartObject;

	/** @var array<callable(int, int): void>  */
	public array $onDuplicate = [];

	function __construct(
		private readonly Orm $orm,
		private readonly ConfigService $configService,
		private readonly ParameterValueModel $parameterValueModel,
		private readonly CommentService $commentService,
		private readonly AliasModel $aliasModel,
		private readonly ParameterModel $parameterModel,
		private readonly CustomFields $customFields,
		private readonly CustomContent $customContent,
		private readonly Facade $allElasticFacade,
		private readonly \App\Model\ElasticSearch\Common\Facade $commonElasticFacade,
	)
	{
	}

	public function getNewestArticle(int $limit = 4): IEntity|null
	{
		$blog = $this->orm->tree->getByUid('articlesMain');

		return $blog?->crossroadItems->resetOrderBy()->orderBy('publicFrom', ICollection::DESC)->limitBy($limit)->fetch();
	}

	public function getNewestArticles(int $limit = 4): ICollection|EmptyCollection
	{
		$blog = $this->orm->tree->getByUid('articlesMain');
		if ($blog === null) {
			return new EmptyCollection();
		}

		return $blog->crossroadItemsArticles->resetOrderBy()->orderBy(['priorityArticle' => ICollection::DESC, "createdTimeOrder" => ICollection::DESC])->limitBy($limit, 0);
	}

	public function handleDuplicate($sourceId, $destinationId, $userId = NULL, $sourceLang = NULL, $destLang = NULL, $copyAllData = FALSE, $skipFirst = FALSE)
	{
		$this->orm->tree->setPublicOnly(FALSE);
		if (isset($sourceLang)) {
			$this->orm->setMutation($this->orm->mutation->getByCodeChecked($sourceLang));
		}

		$x = $this->duplicate($sourceId, $destinationId, $userId, 0, $sourceLang, $destLang, $copyAllData, $skipFirst);
		return $x;
	}

	private function duplicate($sourceId, $parentId, $userId = NULL, $cnt = 0, $sourceLang = NULL, $destLang = NULL, $copyAllData = FALSE, $skipFirst = FALSE)
	{
		$cnt++;

		if (isset($sourceLang)) {
			$this->orm->setMutation($this->orm->mutation->getByCodeChecked($sourceLang));
		}

		$source2 = $this->orm->tree->getById($sourceId);

		if ($skipFirst && $cnt == 1) {
			$newId = $parentId;
		} else {

			$newTree = new Tree();
			$newTree->name = $source2->name."";
			$newTree->uid = $source2->uid;
			$newTree->nameAnchor = $source2->nameAnchor;
			$newTree->nameTitle = $source2->nameTitle;
			$newTree->template = $source2->template;
			$newTree->last = $source2->last;
			$newTree->annotation = $source2->annotation;
			$newTree->description = $source2->description;
			$newTree->content = $source2->content;
			$newTree->public = $source2->public;


			$newTree->created = $source2->created;
			$newTree->parent = $this->orm->tree->getById($parentId);
			$this->orm->tree->attach($newTree);

			$this->orm->tree->persistAndFlush($newTree);

			$parent = $this->orm->tree->getById($parentId);
			$this->addChild($parent, $newTree);

			$newId = $newTree->id;

			$data2 = $source2->toArray();

			$data = [];
//			$data = (array)$source2;
			$data['alias'] = $source2->alias;
//			$data['name'] = $data['name'] ." - kopie";
			$data['id'] = $newId;
//			if ($userId) {
//				$data['created'] = $userId;
//			}
//			$data['parentId'] = $parentId;
			$data['public'] = $source2->public;
//			$data[''] = $parentId;
//			$data['aliasHistory'] = "";
			$data['publicFrom'] = $data2['publicFrom']->format('Y-m-d H:i:s');
			$data['publicTo'] = $data2['publicTo']->format('Y-m-d H:i:s');


			$data['customfieldsDuplicate'] = $source2->customFieldsJson;
//			$data['productAttachedId'] = $source2->productAttachedId;
//			$data['authorR'] = $source2->authorR;
//			echo $data['customFieldsJson'] ;

//			$data['customFields'] = $source2->customFieldsJson;

//			print_r($source->customFields);
//			print_r($source2->customFields);

			if (isset($data2['createdTimeOrder'])) {
				$data['createdTimeOrder'] = $data2['createdTimeOrder']->format('Y-m-d H:i:s');
			} else {
				$data['createdTimeOrder'] = new DateTimeImmutable();
			}

			//bd($cnt. " ".$data['sort']);
			if ($cnt == 1) {
				// nactu max sort a nastavim jej
				// TODO - refaktor - do ORM
//			$sql = "SELECT MAX(sort) as sort FROM tree WHERE parentId = {$parentId}";
//				$data['sort'] = $this->treeService->getMaxSort($parentId);
			}

			// params
			//	foreach ($source->getParameters() as $ff) {
			//		foreach ($ff->items as $f) {
			//			$data['parameter'][$f->id] = $this->treeService->fetchParamValue($source->id, $f->id);
			//		}
			//	}



			if ($copyAllData) {

				if ($source2->pages->count()) {
					foreach ($source2->pages as $f) {
						$data['pages'][] = $f->id;
						$data['pagesSort'][] = $f->sort;
					}
				}

//				if ($source2->faqs->count()) {
//					foreach ($source2->faqs as $f) {
//						$data['faqId'][] = "";
//						$data['faqAuthorId'][] = $f->created;
//						$data['faqQuestion'][] = $f->question;
//						$data['faqAnswer'][] = $f->answer;
//						$data['faqSort'][] = $f->sort;
//					}
//				}

				if ($source2->images->count()) {
					foreach ($source2->images as $f) {
						$data['imageName'][$f->image] = $f->name;
						$data['imageSort'][$f->image] = $f->sort;
					}
				}

//				if ($source2->files->count()) {
//					foreach ($source2->files as $f) {
//						$data['fileName'][$f->file] = $f->name;
//						$data['fileSort'][$f->file] = $f->sort;
//						$data['fileSize'][$f->file] = $f->size;
//					}
//				}

				if ($source2->videos->count()) {
//				    echo "videos";
					foreach ($source2->videos as $k => $f) {
						$data['videoUrl'][$k] = $f['url'];
						$data['videoSort'][$k] = $k;
						$data['videoName'][$k] = $f['name'];
					}
				}

				if ($source2->links) {
//				    echo "videos";
					foreach ($source2->links as $k => $f) {
						$data['linkUrl'][$k] = $f['url'];
						$data['linkSort'][$k] = $k;
						$data['linkName'][$k] = $f['name'];
						$data['linkOpen'][$k] = $f['open'];
					}
				}

			}

			$treeObject = $this->orm->tree->getById($newId);
			$this->onDuplicate($sourceId, $newId);
			if (isset($destLang)) {
				$this->orm->setMutation($this->orm->mutation->getByCodeChecked($destLang));
			} else {
				$lgByTree = $treeObject->getRootLang();
				$this->orm->setMutation($this->orm->mutation->getByCodeChecked($lgByTree));
			}

			//$this->treeService->save($newId, $data, $data);

			$this->save($treeObject, $data, $userId);
		}

//		$x = $source2->getCrossroad();
//		if ($x->count > 0) {
//			foreach ($x->data as $c) {
//				$this->duplicate($c->id, $newId, $userId, $cnt, $sourceLang, $destLang, $copyAllData);
//			}
//		}

		if ($source2->crossroadAll->count() > 0) {
			foreach ($source2->crossroadAll as $c) {
				$this->duplicate($c->id, $newId, $userId, $cnt, $sourceLang, $destLang, $copyAllData);
			}
		}

		return $newId;
	}


	/** Add Child to parent - handle parent, level, last, path*/
	public function addChild(Tree $parent, Tree $child, $save = true, $skipSort = FALSE)
	{
		$child->parent = $parent;
		$child->level = $parent->level + 1;
		$child->last = 1;

		//path
		$path = $parent->path;
		$path[] = $parent->id;
		$child->path = $path;

		if (!$skipSort) {
			$lastSort = 0;
			foreach ($parent->crossroadAll as $item) {
				$lastSort = $item->sort;
			}
			$child->sort = $lastSort + 1;
		}

		$parent->last = 0;

		if ($save) {
			$this->orm->tree->persistAndFlush($child);
		}
		return $child;
	}


	public function move(Tree $movedTree, Tree $targetTree, $action)
	{
		// toto musis prepocitat
		$oldMovedParent = $movedTree->parent;
		switch ($action) {
			case "after":
			case "before":
				$tmp = [];
				$i = 0;

				if ($targetTree->parent) {

					foreach ($targetTree->parent->crossroadAll as $child) {
						if ($child->id == $movedTree->id) {
							continue;
						}

						if ($action == 'before' && $child->id == $targetTree->id) {
							$i++;
							$movedTree->sort = $i;
							$tmp[] = $movedTree;
						}

						$i++;
						$child->sort = $i;
						$tmp[] = $child;

						if ($action == 'after' && $child->id == $targetTree->id) {
							$i++;
							$movedTree->sort = $i;
							$tmp[] = $movedTree;
						}
					}
					$movedTree->parent = $targetTree->parent;
					$this->orm->persistAndFlush($targetTree->parent);
				}
				break;

			case "inside":
			case "last":

				$i = 0;
				foreach ($targetTree->crossroadAll as $item) {
					$i++;
					$item->sort = $i;
				}
				$i++;
				$movedTree->sort = $i;
				$targetTree->crossroadAll->add($movedTree);
				$this->orm->persistAndFlush($targetTree);
				break;
		}

		if ($oldMovedParent && $oldMovedParent->crossroadAll->count() == 0) {
			$oldMovedParent->last = 1;
			$this->orm->persistAndFlush($oldMovedParent);
		}

		if ($targetTree->parent) {
			$this->recalculateTreePath($targetTree->parent);
		} else {
			$this->recalculateTreePath($targetTree);
		}

		$this->orm->persistAndFlush($targetTree);
	}


	public function recalculateTreePath(Tree $tree)
	{
		if ($tree->crossroadAll->count()) {
			$tree->last = 0;
		} else {
			$tree->last = 1;
		}

		if ($tree->parent) {
			$tree->level = $tree->parent->level + 1;
			if ($tree->parent) {
				$tmpPath = $tree->parent->path;
				$tmpPath[] = $tree->parent->id;
				$tree->path = $tmpPath;
			} else {
				$tree->path = [$tree->parent->id];
			}
		} else {
			$tree->level = 0;
			$tree->path = [];
		}
		foreach ($tree->crossroadAll as $item) {
			$this->recalculateTreePath($item);
		}
	}


	public function save(Tree $tree, $data, int|null $userId = NULL): Tree
	{
		$templateRules = $this->configService->get("templatesParentsRules");

		// tyto hodnoty jsou bool a DB je chce v integeru
		$intValues = ['showContactForm', 'public', 'hideFirstImage', 'hideCrossroad', 'showOnHomepage', 'hideInSearch', 'forceNoIndex', 'priorityArticle', 'hideBigImage'];
		$boolValues = ['showContactForm', 'public', 'hideFirstImage', 'hideCrossroad', 'showOnHomepage', 'hideInSearch','forceNoIndex', 'hideInSitemap', 'priorityArticle', 'hideBigImage'];
		$noSave = ['links', 'products', 'alias', 'aliasHistoryString'];

		if (isset($userId)) {
			$data['edited'] = $userId;
		}
		$data['editedTime'] = DateTime::from('now')->format('Y-m-d H:i:s');

		foreach ($tree->getMetadata()->getProperties() as $i) {
			$col = (string)$i->name;

			if (isset($data[$col])) {
				if (in_array($col, $intValues)) {
					$data[$col] = (int)$data[$col];
				}

				if (!in_array($col, $noSave)) {
					if ($col == "template") {
						// automatic set template according to parent

						// je to skoleni - kteremu se pridala podkategorie -> udelam z nej kategorii skoleni
						if ($tree->template == 'Pages:training' && $tree->crossroad->count()) {
							$tree->$col = 'Pages:servicesSchool'; // ma potomky
							continue;
						}

						// je to kategorie skoleni -> ale nama potomky -> udelam z nej  skoleni
						if ($tree->template == 'Pages:servicesSchool' && !$tree->crossroad->count()) {
							$tree->$col = 'Pages:training'; // nema potomky
							continue;
						}

						if ($tree->parent && $tree->parent->template) {
							if (isset($templateRules[$tree->parent->template]) && is_array($templateRules[$tree->parent->template]) && count($templateRules[$tree->parent->template]) == 1) {
								$tree->$col = $templateRules[$tree->parent->template][0];
								continue;
							}
						}
					}

					$tree->$col = $data[$col];
				}
			} else {
				if (in_array($col, $boolValues)) {
					$tree->$col = 0;
				}
			}
		}

		if (isset($data['customFields'])) {
			$tree->cf = $this->customFields->prepareDataToSave($data['customFields']);
		}

		if (isset($data['customContent']) && isset($data['customContentScheme'])) {
			$tree->customContentJson = $this->customContent->prepareDataToSave($data['customContent'], $data['customContentScheme']);
			$tree->customContentSchemeJson = $this->customContent->prepareSchemeToSave($data['customContentScheme']);
		}

		// jen pro ucely duplikace
		if (isset($data['customfieldsDuplicate'])) {
			$tree->cf = $data['customfieldsDuplicate'];
		}

		if ($data['alias']) {
			$tree->setAliases($data['alias'], $data['aliasHistoryString'] ?? null);
		}

		$this->handleImages($tree, $data);
		$this->handleFiles($tree, $data);
		$this->handleLinks($tree, $data);
		$this->handleVideos($tree, $data);
		$this->handlePages($tree, $data);
		$this->handleProducts($tree, $data);
		$this->handleProductsReview($tree, $data);
		$this->handleParameters($tree, $data);
		$this->handleComments($tree, $data);
		$this->handleFaqs($tree, $data);
		$this->handleReviews($tree, $data);

		$this->orm->persistAndFlush($tree);
		$tree->flushCache();
		$tree->flushParam();


		$this->commonElasticFacade->updateNow($tree, $tree->getMutation());
		$this->allElasticFacade->updateNow($tree);

		return $tree;
	}


	private function handleFaqs($tree, $data)
	{
		if (!isset($data['faqId'])) {
			$data['faqId'] = [];
		}

		foreach ($tree->faqs as $faq) {
			if (!in_array($faq->id, $data['faqId'])) {
				$this->orm->treeFaq->remove($faq);
			}
		}
		$maxSort = 0;
		foreach ($data['faqId'] as $key => $faqId) {
			if ($maxSort < $data['faqSort'][$key]) {
				$maxSort = $data['faqSort'][$key];
			}

			if ($faqId) {
				$treeFaq = $this->orm->treeFaq->getById($faqId);
				$treeFaq->sort = $data['faqSort'][$key];

			} else {
				$treeFaq = new TreeFaq();
				$treeFaq->createdTime = new \DateTime();
				$maxSort++;
				$treeFaq->sort = $maxSort;
			}

			if ($data['faqAuthorId'][$key]) {
				$treeFaq->created = $data['faqAuthorId'][$key];
			}

			$treeFaq->tree = $tree;
			$treeFaq->question = $data['faqQuestion'][$key];
			$treeFaq->answer = $data['faqAnswer'][$key];
		}
	}


	private function handleReviews($tree, $data)
	{
		if (!isset($data['reviewId'])) {
			$data['reviewId'] = [];
		}

		foreach ($tree->reviews as $review) {
			if (!in_array($review->id, $data['reviewId'])) {
				$this->orm->treeReview->remove($review);
			}
		}

		$maxSort = 0;
		foreach ($data['reviewId'] as $key => $reviewId) {
			if ($maxSort < $data['reviewSort'][$key]) {
				$maxSort = $data['reviewSort'][$key];
			}

			if ($reviewId) {
				$treeReview = $this->orm->treeReview->getById($reviewId);
				$treeReview->sort = $data['reviewSort'][$key];

				$imageKeyId = $reviewId;

			} else {
				$treeReview = new TreeReview();
				$treeReview->createdTime = new \DateTime();
				$imageKeyId = $key;
				$maxSort++;
				$treeReview->sort = $maxSort;
			}

			if ($data['reviewAuthorId'][$key]) {
				$treeReview->created = $data['reviewAuthorId'][$key];
			}

			$treeReview->tree = $tree;
			$treeReview->name = $data['reviewName'][$key];
			$treeReview->position = $data['reviewPosition'][$key];
			$treeReview->firm = $data['reviewFirm'][$key];
			$treeReview->text = $data['reviewText'][$key];

			if (isset($data['imageNameSpecial'][$imageKeyId])) {
				foreach ($data['imageNameSpecial'][$imageKeyId] as $imageId => $imageName) {
					$treeReview->image = $imageId;
				}
			}

			if (isset($data['imageDelete'][$imageKeyId])) {
				$treeReview->image = NULL;
			}
		}
	}


	private function handleComments($tree, $data)
	{
		// TODO - refactor - viz handleReviews v productu

		$this->commentService->setTable("tree");

		$this->commentService->deAttachComments($tree->id);

		if (isset($data['commentText'])) {
			foreach ($data['commentText'] as $commentNo => $value) {
				if ($value !== '') {
					$this->commentService->attachComments(
						$tree->id,
						$commentNo,
						$data['commentName'][$commentNo],
						$data['commentDate'][$commentNo],
						$data['commentText'][$commentNo],
						$data['commentEmail'][$commentNo],
						$data['commentIsWebMaster'][$commentNo]
					);
				}
			}
		}
	}


	private function handleVideos($tree, $data)
	{
		$tmp = array();
		if (isset($data['videoUrl'])) {
			foreach ($data['videoUrl'] as $k => $l) {
				if (\Nette\Utils\Validators::isUrl($data['videoUrl'][$k])) {
					$tmp[$data['videoSort'][$k]] = $data['videoUrl'][$k] . "|" . $data['videoName'][$k];

				}
			}
		}
		$tree->videos = $tmp;
	}


	private function handleProductsReview(Tree $tree, $data)
	{
		$actualProducts = [];
		foreach ($tree->productsReview as $i) {
			$actualProducts[$i->id] = 1;
		}
		if (isset($data['productsReview'])) {

			$done = []; // duclicity check
			foreach ($data['productsReview'] as $k => $attachProductId) {

				if (!isset($done[$attachProductId])) {
					$done[$attachProductId] = 1;
				} else {
					continue;
				}

				if (isset($actualProducts[$attachProductId])) {
					$product = $this->orm->product->getById($attachProductId);
					$this->orm->tree->updateToProducts($product, $tree, $data['productsReviewSort'][$k], "tree_product_review");
					unset($actualProducts[$attachProductId]);
				} else {
					if ($attachProductId) {
						$product = $this->orm->product->getById($attachProductId);
						$this->orm->tree->addToProducts($product, $tree, $data['productsReviewSort'][$k], "tree_product_review");
					}
				}
			}
		}

		// remove
		foreach ($actualProducts as $attachProductId => $i) {
			$product = $this->orm->product->getById($attachProductId);
			$this->orm->tree->removeFromProducts($product, $tree, "tree_product_review");
		}
	}


	private function handleProducts(Tree $tree, $data)
	{
		$actualProducts = [];
		foreach ($tree->productsAll as $i) {
			$actualProducts[$i->id] = 1;
		}
		if (isset($data['products'])) {

			$done = []; // duclicity check
			foreach ($data['products'] as $k => $attachProductId) {

				if (!isset($done[$attachProductId])) {
					$done[$attachProductId] = 1;
				} else {
					continue;
				}

				if (isset($actualProducts[$attachProductId])) {
					$product = $this->orm->product->getById($attachProductId);
					$this->orm->tree->updateToProducts($product, $tree, $data['productsSort'][$k]);
					unset($actualProducts[$attachProductId]);
				} else {
					if ($attachProductId) {
						$product = $this->orm->product->getById($attachProductId);
						$this->orm->tree->addToProducts($product, $tree, $data['productsSort'][$k]);
					}
				}
			}
		}

		// remove
		foreach ($actualProducts as $attachProductId => $i) {
			$product = $this->orm->product->getById($attachProductId);
			$this->orm->tree->removeFromProducts($product, $tree);
		}
	}


	private function handlePages(Tree $tree, $data)
	{
		$actualPages = [];
		foreach ($tree->pages as $i) {
			$actualPages[$i->id] = 1;
		}
		if (isset($data['pages'])) {

			$done = []; // duclicity check
			foreach ($data['pages'] as $k => $attachTreeId) {

				if (!isset($done[$attachTreeId])) {
					$done[$attachTreeId] = 1;
				} else {
					continue;
				}

				if (isset($actualPages[$attachTreeId])) {
					$page = $this->orm->tree->getById($attachTreeId);
					$this->orm->tree->updateToPages($page, $tree, $data['pagesSort'][$k]);
					unset($actualPages[$attachTreeId]);
				} else {
					if ($attachTreeId) {
						$page = $this->orm->tree->getById($attachTreeId);
						$this->orm->tree->addToPages($page, $tree, $data['pagesSort'][$k]);
					}
				}
			}
		}

		// remove
		foreach ($actualPages as $attachTreeId => $i) {
			$page = $this->orm->tree->getById($attachTreeId);
			$this->orm->tree->removeFromPages($page, $tree);
		}
	}


	private function handleLinks($tree, $data)
	{
		$tmp = array();
		if (isset($data['linkUrl'])) {
			foreach ($data['linkUrl'] as $k => $l) {
//				if (\Nette\Utils\Validators::isUrl($data['linkUrl'][$k])) {
				$tmp[$data['linkSort'][$k]] = $data['linkUrl'][$k] . '|' . $data['linkName'][$k] . '|' . isset($data['linkOpen'][$k]);
//				}
			}
		}
		$tree->links = $tmp;
	}


	private function handleFiles($tree, $data)
	{
		$actualFiles = [];
		foreach ($tree->files as $i) {
			$actualFiles[$i->file] = $i->id;
		}

		if (isset($data['fileName'])) {
			$newSort = 0;
			foreach ($data['fileName'] as $fileId => $f) {
//					$this->attachFile($detail->id, $file->id, $data['fileName'][$k], $file->url, $data['fileSort'][$k], $post['fileSize'][$k]);

				if (isset($actualFiles[$fileId])) {
					// edit
					$fileEdit = $this->orm->treeFile->getById($actualFiles[$fileId]);
					$fileEdit->name = $data['fileName'][$fileId];
					$fileEdit->sort = $newSort;
					$fileEdit->size = $data['fileSize'][$fileId];
					unset($actualFiles[$fileId]);
//					$this->orm->persistAndFlush($fileEdit);

				} else {
					// add
//					$file = $this->orm->file->getById($fileId);
					$file = $this->orm->file->getById($fileId);

					if ($file && $file->id) {
						$newFile = new TreeFile();
						$newFile->name = $data['fileName'][$fileId];
						$newFile->sort = $newSort;
						$newFile->size = $data['fileSize'][$fileId];
						$newFile->url = $file->url;
						$newFile->file = $fileId;

						$tree->files->add($newFile);
					}
				}
				$newSort++;
			}
		}

		// remove
		foreach ($actualFiles as $fileId => $connId) {
			$file = $this->orm->treeFile->getById($connId);
			$this->orm->treeFile->remove($file);
		}
	}


	private function handleImages($tree, $data)
	{
		//bd("img:");
		//bd($tree->images);

		$actualImages = [];
		foreach ($tree->images as $i) {
			$actualImages[$i->image] = $i->id;
		}
		//bd($actualImages);
		//bd("imageName", $data['imageName']);

		// razeni - nacteni puvodniho poradi
		if (isset($data['imageName'])) {
			foreach ($data['imageName'] as $imageId => $f) {
				if (isset($actualImages[$imageId])) {
					// edit
					$imageEdit = $this->orm->treeImage->getById($actualImages[$imageId]);
					$imageEdit->name = $data['imageName'][$imageId];
					$imageEdit->sort = $data['imageSort'][$imageId];
					unset($actualImages[$imageId]);
					$this->orm->persistAndFlush($imageEdit);

				} else {
					// add
					$image = $this->orm->libraryImage->getById($imageId);
					if ($image && $image->id) {
						$newImage = new TreeImage();
						$newImage->image = $image->id;
						$newImage->url = $image->url;
						$newImage->name = $data['imageName'][$imageId];
						$newImage->sort = $data['imageSort'][$imageId];
						$tree->images->add($newImage);
					}
				}
			}
		}

		// remove
		foreach ($actualImages as $imageId => $connId) {
			$image = $this->orm->treeImage->getById($connId);
			$this->orm->treeImage->remove($image);
		}
	}


	private function handleParameters(Tree $tree, $data)
	{

		foreach ($tree->getParametersValues() as $parameterId => $parametersValues) {
			if (!isset($data['parameterValueId'][$parameterId])) {
				foreach ($parametersValues as $parametersValue) {
					$this->parameterValueModel->removeValue($parametersValue, $tree);
				}
			}
		}


		if (isset($data['parameterValueId'])) {
			foreach ($data['parameterValueId'] as $parameterId => $parameterValuesData) {
				$parameter = $this->orm->parameter->getById($parameterId);
				if (!$parameter->isSimple) {
					if ($parameterValuesData) {
						if ($parameter->type == 'select') {
							$this->parameterValueModel->handleParameterValuesSelectAttachment($tree, $parameter, $parameterValuesData);
						} elseif ($parameter->type == 'multiselect') {
							$this->parameterValueModel->handleParameterValuesMultiSelectAttachment($tree, $parameter, $parameterValuesData);
						}
					} else {
						// smazat hodnoty selectu/multiselectu
						$this->parameterModel->removeParameterFrom($tree, $parameter);
					}
				}
			}
		}

		if (isset($data['parameterValue'])) {
			foreach ($data['parameterValue'] as $parameterId => $parameterValuesData) {
				$parameter = $this->orm->parameter->getById($parameterId);

				$parameterValueId = null;
				if (isset($data['parameterValueId'][$parameterId])) {
					$parameterValueId = $data['parameterValueId'][$parameterId];
				}

				$this->parameterValueModel->handleParameterValuesAttachment($tree, $parameter, $parameterValueId, $parameterValuesData);
			}
		}
	}


	public function attachImage($treeId, $images)
	{
//		echo "produkt: $productId..... ";
		$tree = $this->orm->tree->getById($treeId);

		$actualImages = [];
		$actualImagesO = [];
		foreach ($tree->images as $i) {
			$actualImages[$i->image] = $i->id;
			$actualImagesO[$i->image] = $i;
		}

		// razeni - nacteni puvodniho poradi
		if ($images) {
			$sort = 0;
			foreach ($images as $type => $imageId) {
				if (isset($actualImages[$imageId])) {
					// edit
					$actualImagesO[$imageId]->sort = $sort;
					unset($actualImages[$imageId]);
				} else {
					// add
					$image = $this->orm->libraryImage->getById($imageId);
					if ($image && $image->id) {
						$newImage = new TreeImage();
						$newImage->image = $image->id;
						$newImage->url = $image->url;
						$newImage->name = $image->name;
						$newImage->sort = $sort;
						if (!is_numeric($type)) {
							$newImage->type = $type;
						}

						$tree->images->add($newImage);
					}
				}
				$sort++;
			}
		}

		// remove
		foreach ($actualImages as $imageId => $connId) {
			$image = $this->orm->treeImage->getById($connId);
			$this->orm->treeImage->remove($image);
		}

		$this->orm->persistAndFlush($tree);
	}


	public function remove(Tree $tree)
	{
//		$idToRemove = [];
//
//		$idToRemove[] = $tree->id;
//		foreach ($tree->crossroad as $item) {
//			$idToRemove[] = $item->id;
//		}


		$this->commonElasticFacade->deleteNow($tree, $tree->getMutation());
		$this->allElasticFacade->deleteNow($tree);

		$rels = $this->orm->productPage->findBy(['tree' => $tree->id]);
		foreach ($rels as $rel) {
			$this->orm->productPage->remove($rel);
		}

		$this->orm->tree->remove($tree, true);
		$this->orm->tree->persistAndFlush($tree);
	}


	public function getAllSubItems(Tree $tree, $onlyIds = false)
	{
		$ids = [];
		foreach ($tree->crossroadItemsNoSort as $leafItems) {

			$pathIds = $leafItems->path;
			$pathIds[] = $leafItems->id;

			$i = 0;
			while ($pathIds && $pathIds[$i] != $tree->id) {
				unset($pathIds[$i]);
				$i++;
			}

			foreach ($pathIds as $pathId) {
				$ids[$pathId] = $pathId;
			}
		}

		if ($onlyIds) {
			return $ids;
		} else {
			return $this->orm->tree->findBy(['id' => $ids]);
		}
	}

	public function createNew(Tree $parent, $name)
	{
		$templateRules = $this->configService->get("templatesParentsRules");

		if (isset($templateRules[$parent->template]) && is_array($templateRules[$parent->template]) && count($templateRules[$parent->template]) == 1) {
			$template = $templateRules[$parent->template][0];
		} elseif (isset($templateRules[$parent->template]) && is_array($templateRules[$parent->template]) && count($templateRules[$parent->template]) > 1) {
			$template = $templateRules[$parent->template][0];
		} else {
			$template = $parent->template;
		}

		$child = new Tree();
		$child->name = $name;
		$child->nameTitle = $name;
		$child->nameAnchor = $name;
		$child->public = 0;
		$child->template = $template;
		$child->created = 0;
		$child->hideCrossroad = 0;
		$page = $this->addChild($parent, $child);

		return $page;
	}


	public function getClasses()
	{
		$classesRoot = $this->orm->tree->getByUid('school');
		return $classesRoot->crossroad->orderBy('name')->fetchPairs('id', 'name');
	}

	public function getQuickTree($id, \SuperKoderi\User\Security\User $user)
	{
		if ($user->isDeveloper()) {
			$all = $this->orm->tree->findBy([])->orderBy('sort')->orderBy('id');
		} else {
			$all = $this->orm->tree->findBy(['uid!=' => 'systemPageId'])->orderBy('sort')->orderBy('id');
		}

		$sorted = [];
		foreach ($all as $a) {
			$tmp = new \stdClass();
			$tmp->name = $a->name;
			$tmp->id = $a->id;
			$tmp->public = $a->public;
			$sorted[$a->parentId][] = $tmp;
		}
		$tree =	$this->buildTree('', $id, $sorted);

		if ($user->getPartnerAllowedMutationIds()) {
			$mutationRootIds = $this->orm->mutation->findBy(['id' => $user->getPartnerAllowedMutationIds()])->fetchPairs('rootId', 'id');
			foreach ($tree as $k => $t) {
				if (!isset($mutationRootIds[$t->id])) {
					unset($tree[$k]); // ACL !!!! -> zatim se TreeId shoduji s ID mutace, ale musi se porovnavat s rootId mutace !!!!
				}
			}
		}

		return $tree;
	}

	private function buildTree($level, $selected, $sorted)
	{
		$ret = null;
		if (isset($sorted[$level])) {
			foreach ($sorted[$level] as $k => $r) {
				$ret[$k] = $r;
				$ret[$k]->active = false;
				if ($r->id == $selected) {
					$ret[$k]->active = true;
				}
				$ret[$k]->items = $this->buildTree($r->id, $selected, $sorted);
			}
		}
		return $ret;
	}

}
