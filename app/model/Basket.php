<?php

namespace SuperKoderi;

use App\Model\LastVisitCookie;
use App\Model\Mutation;
use App\Model\Order;
use App\Model\OrderItem;
use App\Model\OrderItemModel;
use App\Model\OrderModel;
use App\Model\Orm;
use App\Model\ProductVariant;
use App\Model\Subscription;
use App\Model\User;
use App\Model\UserModel;
use App\Model\Voucher;
use App\Model\VoucherCode;
use App\Model\VoucherCodeException;
use App\Model\VoucherCodeModel;
use App\Model\VoucherCodeUsesModel;
use ArrayIterator;
use Nette;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use function max;

class Basket
{

	use Nette\SmartObject;

	private float $dph;

	public ArrayHash $transports;

	public ArrayHash $payments;

	public ArrayHash $paymentsForSubscription;

	protected SessionSection $basket;

	protected float $productsPriceDPH = 0;

	protected ?float $transportPriceDPH = null;

	protected float $servicesPriceDPH = 0;

	protected float $bothPriceDPH = 0;

	protected float $totalPriceDPH = 0;

	protected int $transportFreeFrom = 0;

	protected bool $allowMoreVouchers = false;

	protected bool $isFirstSubscriptionOrder = false;

	protected ?string $transport;

	protected ?string $payment;

	protected ?string $paymentSubType = null;

	protected ?string $zasilkovna;

	protected ?string $subscriptionHash = null;

	protected ?Subscription $subscription = null;

	protected ?User $user = null;

	/** puvodni amount pri volani addProductVariant() - hack pro predkosik! >( */
	public int $amountOrigin = 0;

	private array $cache = [];

	public bool $isSubscriptionEnabled = false;

	public bool $hasOversizedProducts = false;

	private Mutation $mutation;

	public function __construct(
		private readonly Nette\Http\Session $session,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
		private readonly VoucherCodeModel $voucherCodeModel,
		private readonly TransportService $transportService,
		private readonly PaymentService $paymentService,
		private readonly IBasketItemFactory $basketItemFactory,
		private readonly OrderModel $orderModel,
		private readonly LastVisitCookie $lastVisitCookie,
		private readonly OrderItemModel $orderItemModel,
		private readonly UserModel $userModel,
		private readonly Gtm $gtm,
		private readonly VoucherCodeUsesModel $voucherCodeUsesModel
	)
	{
		$this->basket = $this->session->getSection('basket');
		$this->subscriptionHash = $this->session->getSection('subscription')->hash ?? null;
		$this->dph = $this->configService->getParam('dph');

		$this->isFirstSubscriptionOrder = $this->isFirstSubscriptionOrder();
	}


	public function isEmpty(): bool
	{
		return !isset($this->basket->items);
	}


	public function isAllowMoreVouchers(): bool
	{
		return $this->allowMoreVouchers;
	}


	protected function removeItemFromKey($uniqueKey, $keySubToRemove)
	{
		$tmp = explode("-", $uniqueKey);
		foreach ($tmp as $k => $i) {
			if ($keySubToRemove == $i) {
				unset($tmp[$k]);
				break;
			}
		}

		return implode("-", $tmp);
	}


	public function cleanSubscriptionProducts(): void
	{
		if ($this->isSubscriptionEnabled === false && $this->containsSubscriptionProducts()) {
			foreach ($this->getItems() as $uniqueKey => $orderItems) {
				foreach ($orderItems as $orderItem) {
					if ($orderItem->type === OrderItem::TYPE_SUBSCRIPTION) {
						$this->remove($uniqueKey, false);
					}
				}
			}
		}

	}

	public function addProductVariant(ProductVariant $variant = null, $amount = 1, $params = NULL, ?string $type = OrderItem::TYPE_PRODUCT, ?int $sort = null)
	{
		$amount = max(1, (int)$amount);

		$data = [];
		$uniqueKey = [];


		if ($variant && $variant->isSubscription && $type === OrderItem::TYPE_SUBSCRIPTION) {
			$uniqueKey[BasketItem::PARSE_KEY_SUBSCRIPTION . $variant->id] = BasketItem::PARSE_KEY_SUBSCRIPTION . $variant->id;
		}

		if ($variant && $variant->presents->count()) {
			foreach ($variant->presents as $present) {
				$uniqueKey[BasketItem::PARSE_KEY_PRESENT . $present->productVariantGift->id] = BasketItem::PARSE_KEY_PRESENT . $present->productVariantGift->id;
			}
		}

//		if ($variant && !isset($params['forceAddVariant']) && $variant->isSampleAndAvailableFree()) {
		if ($variant && isset($params['forceAddSample'])) {
			$uniqueKey[BasketItem::PARSE_KEY_SAMPLE . $variant->id] = BasketItem::PARSE_KEY_SAMPLE . $variant->id;
			$variant = null;
		}

		if ($variant && isset($params['testAdd'])) {
			$uniqueKey['P' . $variant->id] = 'P' . $variant->id;
			$variant = null;
		}

//		if ($variant && $variant->product->voucher && $variant->product->voucher->public) {
//			// product - voucher
//			$data['productVoucher'] = 1;
//		}

		if (isset($params[BasketItem::TYPE_GIFTEXTRA]) && $params[BasketItem::TYPE_GIFTEXTRA]) {
			$uniqueKey[BasketItem::PARSE_KEY_GIFTEXTRA . $params[BasketItem::TYPE_GIFTEXTRA]] = BasketItem::PARSE_KEY_GIFTEXTRA . $params[BasketItem::TYPE_GIFTEXTRA];
		}

		// vlozeni sluzby
//		if (isset($params['service']) && $params['service']) {
//			foreach ($params['service'] as $serviceId) {
//				$uniqueKey['S' . $serviceId] = 'S' . $serviceId;
//			}
//		}
//
//		// vlozeni zaruky
//		if (isset($params['warranty']) && $params['warranty']) {
//			$uniqueKey['W' . $params['warranty']] = 'W' . $params['warranty'];
//		}
//
//		// vlozeni pojisteni
//		if (isset($params['insurance']) && $params['insurance']) {
//			$uniqueKey['I' . $params['insurance']] = 'I' . $params['insurance'];
//		}


		if ($variant && $variant->isSubscription && $type === OrderItem::TYPE_SUBSCRIPTION) {
			array_unshift($uniqueKey, BasketItem::PARSE_KEY_SUBSCRIPTION.$variant->id);
			$this->basket->subscriptionHash = $this->getBasketSubscriptionHash();
		} else if ($variant) {
			array_unshift($uniqueKey, BasketItem::PARSE_KEY_VARIANT.$variant->id);
		}

		if ($type !== OrderItem::TYPE_SUBSCRIPTION) {
			ksort($uniqueKey);
		}
		$uniqueKey = array_unique($uniqueKey);
		$uniqueKey = implode('-', $uniqueKey);

		if (!isset($this->basket->items[$uniqueKey])) {
			$this->basket->items[$uniqueKey] = [ 'amount' => 0]; // TODO pridani info ohledne skoleni a pujceni
		} else {

		}
		$this->amountOrigin = $this->basket->items[$uniqueKey]['amount'];
		$this->basket->items[$uniqueKey]['amount'] += $amount;


//		if (isset($this->basket->items[$uniqueKey]) && preg_match('/^V([0-9]+)/', $uniqueKey, $matches)
//			&& $variant = $this->orm->productVariant->getActiveVariant($matches[1])) {
//			$this->basket->items[$uniqueKey]['amount'] = $this->getPossibleAmount($variant, $this->basket->items[$uniqueKey]['amount']);
//		}
		// hlidani poctu kusu
		if ($variant) {
			bd([__METHOD__,$amount]);
			if ($this->basket->items[$uniqueKey]['amount'] > $variant->stock) {
				$this->basket->items[$uniqueKey]['amount'] = $variant->stock;
			}
		}


		// dodatecna data v kosiku k polozkam
		if (isset($data) && $data) {
			foreach ($data as $valueName => $value) {
				if ($valueName == 'productVoucher') {
					$this->basket->items[$uniqueKey]['data'][$valueName] = $value;
				}
			}
		}

		if ($sort === null) {
			$sortValues = array_column($this->basket->items, 'sort');
			if (empty($sortValues)) {
				$sort = 0;
			} else {
				$sort = max($sortValues) + 1;
			}
		}
		$this->basket->items[$uniqueKey]['sort'] = $sort;

		// editace produktu k pujceni - smazu puodni zaznam
//		if (isset($params['uniqueKey']) && $params['uniqueKey'] != $uniqueKey) {
//			unset($this->basket->items[$params['uniqueKey']]);
//		}

		$this->saveToDbBasket();

		if (isset($variant) && $this->gtm->isEventEnabled(GtmEecAddToCartEvent::NAME)) {
			$list = $params['list'] ?? null;
			if ($list !== Gtm::BASKET_PRODUCT_LIST) { //basket products are processed in onClick
				$position = $params['position'] ?? null;
				$event = (new GtmEecAddToCartEvent($this->gtm))->setup($variant, $amount, null, $list, $position);
				$this->gtm->pushEvent($event);
			}
		}

		return $uniqueKey;
	}


	public function getMaxAmount(ProductVariant $variant, $uniqueKey, $desireAmountOnKey)
	{
		// nalezeni kolikrat jejiz produkt v kosiku
		$countSum = 0; // pocet kusu co chci pridat
		// nactu pocet kusu co je jiz v kosiku
		foreach ($this->basket->items as $k=>$e) {
			if ($uniqueKey == $k) {
				continue;
			}
			if (strpos($k, BasketItem::PARSE_KEY_VARIANT.$variant->id) !== false) {
				$countSum += $e['amount'];
			}
			if (strpos($k, BasketItem::PARSE_KEY_SUBSCRIPTION.$variant->id) !== false) {
				$countSum += $e['amount'];
			}
		}

		// zistim max mozne kusy
		$maxPossibleAmount = $this->getPossibleAmount($variant, $countSum+$desireAmountOnKey);

		// dostupne kusy minus jiz ty vlozene
		$final = $maxPossibleAmount - $countSum;

//		bd("desire: ". $desireAmountOnKey);
//		bd("actual: ". $countSum);
//		bd("maxPossibleAmount: ".$maxPossibleAmount);
//		bd("final: ".$final);

		return $final;
	}


//	public function removeProductVariant(ProductVariant $variant, $amount = 1)
//	{
//		if (isset($this->basket->items[$variant->product->id][$variant->id])) {
//			$amountInBasket = $this->basket->items[$variant->product->id][$variant->id];
//			$this->setProduct($variant, $amountInBasket - $amount);
//		}
//
//		$this->saveToDbBasket($variant);
//	}


	public function changeProductAmount($key, $amount, ?int $sort = null)
	{
		$previousAmount = 0;
		if (isset($this->basket->items[$key])) {
			$previousAmount = $this->basket->items[$key]['amount'];
			$this->basket->items[$key]['amount'] += $amount;

			if ($this->basket->items[$key]['amount'] == 0) {
				$this->remove($key, false);
			}
		} elseif ($amount) {
			$this->basket->items[$key]['amount'] = $amount;
		}

		if (isset($this->basket->items[$key]) && preg_match('/^V([0-9]+)/', $key, $matches)
			&& $variant = $this->orm->productVariant->getActiveVariant($matches[1])) {
			$this->basket->items[$key]['amount'] = $this->getPossibleAmount($variant, $this->basket->items[$key]['amount']);
		}

		if (isset($variant) && $amount > 0 && $this->gtm->isEventEnabled(GtmEecAddToCartEvent::NAME)) {
			$event = (new GtmEecAddToCartEvent($this->gtm))->setup($variant, $amount);
			$this->gtm->pushEvent($event);
		} elseif (isset($variant) && $amount < 0) {
			$amount = min($previousAmount, -$amount);
			$event = (new GtmEecRemoveFromCartEvent($this->gtm))->setup($variant, -$amount);
			$this->gtm->pushEvent($event);
		}

		if (isset($this->basket->items[$key])) {
			$this->basket->items[$key]['sort'] = $sort ?? $this->basket->items[$key]['sort'];
		}

		$this->saveToDbBasket();
	}

	/**
	 * @param string $uniqueKey
	 * @param int $amount
	 * @return bool|string
	 */
	public function setProduct(string $uniqueKey, int $amount)
	{
		$result = true;
		$amountDiff = max(0, $amount) - $this->basket->items[$uniqueKey]['amount'] ?? 0;
		$variant = null;
		if (isset($this->basket->items[$uniqueKey])) {
			if (preg_match('/^V([0-9]+)/', $uniqueKey, $matches)) {
				$variant = $this->orm->productVariant->getActiveVariant($matches[1]);
			}
			if (preg_match('/^X([0-9]+)/', $uniqueKey, $matches)) {
				$variant = $this->orm->productVariant->getActiveVariant($matches[1]);
			}
		}

		if ($amount <= 0) {
			$this->remove($uniqueKey, false);
		} elseif (isset($this->basket->items[$uniqueKey])) {
			$this->basket->items[$uniqueKey]['amount'] = $amount;

			if ($variant && $amount > $variant->stock) {
				$amountDiff = $variant->stock - $this->basket->items[$uniqueKey]['amount'];
				$this->basket->items[$uniqueKey]['amount'] = $variant->stock;
				$result = $variant->nameDefault;
			}
		} else {
			$result = false;
		}

		//GTM
		if (isset($variant)) {
			if ($amountDiff > 0 && $this->gtm->isEventEnabled(GtmEecAddToCartEvent::NAME)) {
				$event = (new GtmEecAddToCartEvent($this->gtm))->setup($variant, $amountDiff);
				$this->gtm->pushEvent($event);
			} elseif ($amountDiff < 0) {
				$event = (new GtmEecRemoveFromCartEvent($this->gtm))->setup($variant, -$amountDiff);
				$this->gtm->pushEvent($event);
			}
		}

		$this->validateGifts();
		$this->saveToDbBasket();

		return $result;
	}

	public function containsSubscriptionProducts(): bool
	{
		$contains = false;
		foreach ($this->getItems() as $uniqueKey => $orderItems) {
			if ($uniqueKey === 'errors') {
				continue;
			}
			foreach ($orderItems as $orderItem) {
				if ($orderItem->type === OrderItem::TYPE_SUBSCRIPTION) {
					$contains = true;
				}
			}
		}

		return $contains;
	}

	public function getSubscriptionHash(): ?string
	{
		return $this->subscriptionHash;
	}

	public function getBasketSubscriptionHash(): ?string
	{
		return $this->basket->subscriptionHash;
	}


	public function setBasketSubscriptionHash(string $hash): void
	{
		$this->basket->subscriptionHash = $hash;
	}

	// projde polozky kosiku a pokud zjisti ze jde o vzorky zdarma a mam na ne narok,
	// prevede je na vzorky zdarma
	public function handleFreeSample($source = "")
	{
		// projdu polozky kosiku a hledam vzorky zdarma - od nejdrazsisch

		$samplesBought = $this->userModel->getSampleBoughtFreeCount();
		$freeSampleCountMax = 0;
		if ($samplesBought !== false) {
			$freeSampleCountMax = User::FREE_SAMPLE_COUNT_LIMIT - $samplesBought;
		}

		if ($freeSampleCountMax <= 0) {
			return;
		}

//		bd($this->basket->items);
//		die;

		$toAddVariants = [];
		foreach ($this->getItems() as $uniqueKey => $orderItems) {
			//	vloučíme produkt s dárkem zdarma. Dárek může být totiž i "vzorek zdarma" (sample)
			$count = count($orderItems);
			if ($count > 1 && $orderItems[1]->type === 'present') {
				continue;
			}

			foreach ($orderItems as $orderItem) {

				if ($orderItem->variant && $orderItem->variant->isSampleAndAvailableFree()) {
					$priceKey = (int) $orderItem->variant->priceFinalDPH;
					$toAddVariants[$priceKey][] = [
						'amount' => $orderItem->amount,
						'variant' => $orderItem->variant,
//						'uk' => $uniqueKey,
					];

					$this->remove($uniqueKey);
				}
			}
		}

		krsort($toAddVariants);
//		bd($toAddVariants);

		if (!count($toAddVariants)) {
			return;
		}

		foreach ($toAddVariants as $priceLevels) {
			foreach ($priceLevels as $i) {
				$amount = $i['amount'];
//				$uniqueKey = $i['uk'];
				$amountRest = 0;

				if ($freeSampleCountMax && $freeSampleCountMax >= $amount) {
					// je dostatecne mnozstvi
					$amountFinal = $amount;
					$freeSampleCountMax = $freeSampleCountMax - $amount;

				} else {
					// nestaci
					if ($freeSampleCountMax > 0) {
						$amountFinal = $freeSampleCountMax;
						$amountRest = $amount - $freeSampleCountMax;
						$freeSampleCountMax = $freeSampleCountMax - $amountFinal;
					} else {
						$amountFinal = 0;
						$amountRest = $amount;
					}

				}


				if ($amountFinal) {
					// vzorek
					$this->addProductVariant(variant: $i['variant'], amount: $amountFinal, params: [ 'forceAddSample' => true ]);
				}

				if ($amountRest) {
					// klasicky produkt
					$this->addProductVariant(variant: $i['variant'], amount: $amountRest);
				}
			}
		}

	}


	public function remove($key, $pushToGtm = true)
	{
		if ($pushToGtm && $this->gtm->isEventEnabled(GtmEecRemoveFromCartEvent::NAME)) {
			if (isset($this->basket->items[$key]) && preg_match('/^V([0-9]+)/', $key, $matches)
				&& ($variant = $this->orm->productVariant->getActiveVariant($matches[1])) !== null) {
				$amount = $this->basket->items[$key]['amount'];
				$this->gtm->pushEvent((new GtmEecRemoveFromCartEvent($this->gtm))->setup($variant, $amount));
			}
		}

		unset($this->basket->items[$key]);
		if (empty($this->basket->items[$key])) {
			unset($this->basket->items[$key]);
		}

		$this->validateGifts();

		$this->saveToDbBasket();
	}

	public function changeProductToSubscription(string $key, int $amount): void
	{
		if (array_key_exists($key, $this->basket->items)) {
			if (str_starts_with($key, BasketItem::PARSE_KEY_VARIANT)) {
				$productVariantId = substr($key, 1);
			} else {
				return;
			}

			$variant = $this->orm->productVariant->getActiveVariant($productVariantId);
			$amount = $this->basket->items[$key]['amount'];
			$sort = $this->getBasketItemPosition($key) ?? 0;

			$this->remove($key);

			$subscriptionKey = BasketItem::PARSE_KEY_SUBSCRIPTION . $productVariantId;
			if (isset($this->basket->items[$subscriptionKey])) {
				$this->changeProductAmount($subscriptionKey, $amount, $sort);
			} else {
				$this->addProductVariant(variant: $variant, amount: $amount, type: OrderItem::TYPE_SUBSCRIPTION, sort: $sort);
			}

		}
	}

	public function changeSubscriptionProductToOneTime(string $key, int $amount): void
	{
		if (array_key_exists($key, $this->basket->items)) {
			if (str_starts_with($key, BasketItem::PARSE_KEY_SUBSCRIPTION)) {
				$productVariantId = substr($key, 1);
			} else {
				return;
			}

			$variantKey = BasketItem::PARSE_KEY_VARIANT . $productVariantId;
			$sort = $this->getBasketItemPosition($key) ?? 0;

			$this->remove($key);

			$variant = $this->orm->productVariant->getActiveVariant($productVariantId);

			if (isset($this->basket->items[$variantKey])) {
				$this->changeProductAmount($variantKey, $amount, $sort);
			} else {
				$this->addProductVariant(variant: $variant, amount: $amount, sort: $sort);
			}
		}
	}

	private function getBasketItemPosition(string $key): ?int
	{
		return $this->basket->items[$key]['sort'] ?? null;
	}

	/**
	 * @return bool
	 */
	private function hasGifts(): bool
	{
		return !empty($this->getAttachedGiftsForBasket()['available']);
	}


	/**
	 * @return void
	 */
	private function removeGifts(): void
	{
		if (!is_iterable($this->basket->items)) {
			return;
		}

		foreach ($this->basket->items as $basketItemKey => &$basketItem) {
			if (!Strings::startsWith($basketItemKey, 'G')) {
				continue;
			}

			unset($this->basket->items[$basketItemKey]);
		}
	}


	/**
	 * @return void
	 */
	private function validateGifts(): void
	{

		$basketGiftData = $this->getAttachedGiftsForBasket();

		if ($basketGiftData) {
			if ($basketGiftData['currentLevel'] === 0 && !$this->hasGifts()) {
				return;
			}

			if ($basketGiftData['currentLevel'] < $this->getProductsPriceDPH()) {
				return;
			}
		}

		$this->removeGifts();
	}


	// odstraneni sluzby, zaruky, skoleni, ...
	public function removeSub($uniqueKey, $keySubToRemove)
	{
		if  (isset($this->basket->items[$uniqueKey])) {
			$newKey = $this->removeItemFromKey($uniqueKey, $keySubToRemove);
			if (isset($this->basket->items[$newKey])) {
				// nove vznikly klic jiz existuje -> prictu kusy, TODO pridani dalsich veci, pokud jsou (info o skoleni, pujceni)
				$this->basket->items[$newKey]['amount'] += $this->basket->items[$uniqueKey]['amount'];
			} else {
				// zalozim novy
				$this->basket->items[$newKey] = $this->basket->items[$uniqueKey];
			}

			// smazai puvodniho zaznamu
			unset($this->basket->items[$uniqueKey]);
		}
	}


	public function getProductAmount($uniqueKey)
	{
		if (isset($this->basket->items[$uniqueKey])) {
			return $this->basket->items[$uniqueKey];
		} else {
			return 0;
		}
	}


	/**
	 * @return BasketItem[]
	 */
	public function getProducts(): array
	{
		$ret = array();
		if (isset($this->basket->items)) {
			$sort = 0;
			foreach ($this->basket->items as $uniqueKey => $data) {
				$basketItems = $this->parseKey($uniqueKey, $data['amount']);
				if (!$basketItems) {
					unset($this->basket->items[$uniqueKey]);
					continue;
				}

				if (isset($basketItems[BasketItem::TYPE_VARIANTS])) {
					foreach ($basketItems[BasketItem::TYPE_VARIANTS] as $variantId => $variant) {
						if ($variant) {
							$ret[] = $this->basketItemFactory->create($variant, $data['amount'], sort: $sort, firstSubscriptionOrder: $this->isFirstSubscriptionOrder());
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_SUBSCRIPTIONS])) {
					foreach ($basketItems[BasketItem::TYPE_SUBSCRIPTIONS] as $variantId => $variant) {
						if ($variant) {
							$ret[] = $this->basketItemFactory->create($variant, $data['amount'], type: OrderItem::TYPE_SUBSCRIPTION, sort: $sort, firstSubscriptionOrder: $this->isFirstSubscriptionOrder());
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_SERVICES])) {
					foreach ($basketItems[BasketItem::TYPE_SERVICES] as $variantId => $variant) {
						if ($variant) {
							$ret[] = (object)$variant;
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_WARRANTIES])) {
					foreach ($basketItems[BasketItem::TYPE_WARRANTIES] as $variantId => $variant) {
						if ($variant) {
							$ret[] = (object)$variant;
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_INSURANCES])) {
					foreach ($basketItems[BasketItem::TYPE_INSURANCES] as $variantId => $variant) {
						if ($variant) {
							$ret[] = (object)$variant;
						}
					}
				}

				$sort++;
			}
		}

		return $ret;
	}


	public function getServices()
	{
		$ret = array();

		if (isset($this->basket->items)) {
			foreach ($this->basket->items as $uniqueKey => $data) {
				$basketItems = $this->parseKey($uniqueKey, $data['amount']);

				if (!$basketItems) {
					unset($this->basket->items[$uniqueKey]);
					continue;
				}

//				if (isset($basketItems[BasketItem::TYPE_VARIANTS])) {
//					foreach ($basketItems[BasketItem::TYPE_VARIANTS] as $variantId => $variant) {
//						if ($variant) {
//							$ret[] = $this->basketItemFactory->create($variant, $data['amount']);
//						}
//					}
//				}

			}
		}

		return $ret;
	}


	public function getSessionItems()
	{
		return $this->basket->items;
	}

	public function setSessionItems($items, string|null $subscription = null): void
	{
		$this->basket->items = $items;
		$this->basket->subscription = $subscription;
	}

	public function changeItemKey(string $oldKey, string $newKey): void
	{
		$this->basket->items[$newKey] = $this->basket->items[$oldKey];
		unset($this->basket->items[$oldKey]);
	}

	public function getItems(): array
	{
		$ret = array();

		$ret['errors'] = [];
		if (isset($this->basket->items)) {
			foreach ($this->basket->items as $uniqueKey => $data) {
				$basketItems = $this->parseKey($uniqueKey, $data['amount']);

				if (!$basketItems) {
					$ret['errors'][] = $uniqueKey;
					unset($this->basket->items[$uniqueKey]);
					continue;
				}

				if (isset($basketItems[BasketItem::TYPE_VARIANTS])) {
					foreach ($basketItems[BasketItem::TYPE_VARIANTS] as $variantId => $variant) {
						if ($variant) {
							$ret[$uniqueKey][] = $this->basketItemFactory->create($variant, $data['amount'], sort: $data['sort'] ?? 0);
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_SUBSCRIPTIONS])) {
					foreach ($basketItems[BasketItem::TYPE_SUBSCRIPTIONS] as $variantId => $variant) {
						if ($variant) {
							$ret[$uniqueKey][] = $this->basketItemFactory->create($variant, $data['amount'], 'subscription', sort: $data['sort'] ?? 0, firstSubscriptionOrder: $this->isFirstSubscriptionOrder());
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_SAMPLES])) {
					foreach ($basketItems[BasketItem::TYPE_SAMPLES] as $variantId => $variant) {
						if ($variant) {
							$ret[$uniqueKey][] = $this->basketItemFactory->create($variant, $data['amount'], 'sample');
						}
					}
				}


				if (isset($basketItems[BasketItem::TYPE_GIFTEXTRA])) {
					if ($basketItems[BasketItem::TYPE_GIFTEXTRA]) {
						$ret[$uniqueKey][] = (object) $basketItems[BasketItem::TYPE_GIFTEXTRA];
					}
				}

				if (isset($basketItems[BasketItem::TYPE_SERVICES])) {
					foreach ($basketItems[BasketItem::TYPE_SERVICES] as $variantId => $variant) {
						if ($variant) {
							$ret[$uniqueKey][] = (object) $variant;
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_WARRANTIES])) {
					foreach ($basketItems[BasketItem::TYPE_WARRANTIES] as $variantId => $variant) {
						if ($variant) {
							$ret[$uniqueKey][] = (object) $variant;
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_INSURANCES])) {
					foreach ($basketItems[BasketItem::TYPE_INSURANCES] as $variantId => $variant) {
						if ($variant) {
							$ret[$uniqueKey][] = (object)$variant;
						}
					}
				}

				if (isset($basketItems[BasketItem::TYPE_PRESENTS])) {
					foreach ($basketItems[BasketItem::TYPE_PRESENTS] as $presentId => $present) {
						if ($present) {
							$ret[$uniqueKey][] = (object) $present;
						}
					}
				}

			}
		}

		return $ret;
	}


	private function parseKey($key, $amount)
	{
		if (true || !isset($this->cache['parseKey'])) { // to nefunguje !
			$ret = [];
			$items = explode("-", $key);
			$product = NULL;

			foreach ($items as $itemId) {

				if (!isset($itemId[0])) {
					return NULL;
				}

				$id =  substr($itemId, 1);

				switch ($itemId[0]) {
					case "S":
						$ret[BasketItem::TYPE_SAMPLES][$id] = $this->orm->productVariant->getActiveVariant($id);
						break;
					case BasketItem::PARSE_KEY_VARIANT:
						$ret[BasketItem::TYPE_VARIANTS][$id] = $this->orm->productVariant->getActiveVariant($id);
						if (!$ret[BasketItem::TYPE_VARIANTS][$id]) {
//							dump($ret[BasketItem::TYPE_VARIANTS][$id]);
//							$this->easyMessages->send()
//							die;
							return false;
						}
//					bd($ret[BasketItem::TYPE_VARIANTS][$id]);
						$product = $ret[BasketItem::TYPE_VARIANTS][$id]->product;
						break;
					case BasketItem::PARSE_KEY_SUBSCRIPTION:
						$ret[BasketItem::TYPE_SUBSCRIPTIONS][$id] = $this->orm->productVariant->getActiveVariant($id);
						if (!$ret[BasketItem::TYPE_SUBSCRIPTIONS][$id]) {
							return false;
						}
						break;

					case "P":
						if ($presentVariant = $this->orm->productVariant->getById($id)) {
							$ret[BasketItem::TYPE_PRESENTS][$presentVariant->id] = [
								'id' => $presentVariant->id,
								'variant' => $presentVariant,
								'type' => \App\Model\OrderItem::TYPE_PRESENT,
								'amount' => min($amount, $presentVariant->stock), // vlozim jen tolik ks co je skladem
								'priceDPH' => 0,
								'price' => 0,
//								'extraText' => ($amount > $presentVariant->stock) ? '_present_stock_sold_out': null
							];
						}
						break;

					case "G":
						if ($presentProduct = $this->orm->product->getById($id)) {
							$ret[BasketItem::TYPE_GIFTEXTRA] = [
								'id' => $presentProduct->id,
								'variant' => $presentProduct->firstActiveVariant,
								'type' => \App\Model\OrderItem::TYPE_PRESENT_EXTRA,
								'amount' => $amount,
								'priceDPH' => 0,
								'price' => 0,
							];
						}
						break;

//					case "S":
//						$ret[BasketItem::TYPE_SERVICES][$id] = $this->getSpecialService($id, $amount, $product);
//
//						break;
//
//					case "W":
//						$ret[BasketItem::TYPE_WARRANTIES][$id] = $this->getSpecialWarranty($id, $amount, $product);
//
//						break;
//
//					case "I":
//						$ret[BasketItem::TYPE_INSURANCES][$id] = $this->getSpecialInsurance($id, $amount, $product);

						break;
				}
			}

			$this->cache['parseKey'] = $ret;
		}

		return $this->cache['parseKey'];
	}


	public function getProductsCount(): int
	{
		$ret = 0;
		if (isset($this->basket->items)) {
			foreach ($this->basket->items as $key => $i) {
				$ret += $i['amount'];
			}
		}

		return $ret;
	}


	public function setTransport(string $key = null): void
	{
		if (isset($this->transports[$key])) {
			$this->basket->transport = $key;
			return;
		}

		$this->basket->transport = null;
	}

	public function getTransport(): Nette\Utils\ArrayHash|null
	{
		$defaultVat = isset($this->mutation)
			? $this->mutation->vatDefault
			: $this->configService->getParam('shop', 'defaultVat');

		if (isset($this->transports[$this->basket->transport])) {
			$ret = $this->transports[$this->basket->transport];
			$ret['price'] = MoneyHelper::getPriceWithoutDPH($ret['priceDPH'], $defaultVat);
			$ret['key'] = $this->basket->transport;

			return Nette\Utils\ArrayHash::from((array) $ret);
		}

		return null;
	}

	public function getTransportKey(): ?string
	{
		return $this->basket->transport;
	}


	public function setPayment(string $key = null): void
	{
		if (isset($this->payments[$key])) {
			if (Order::isOnlinePayment($key)) {
				$this->basket->payment = Order::PAYMENT_ONLINE;
				$this->basket->subPaymentType = $key;
			} else {
				$this->basket->payment = $key;
				$this->basket->subPaymentType = null;
			}
			return;
		}

		$this->basket->payment = null;
	}


	public function getPayment(): ?Nette\Utils\ArrayHash
	{
		if (isset($this->basket->payment)) {
			$ret = $this->payments[$this->basket->payment];
			$ret['price'] = $ret['priceDPH'] / $this->dph;

			return Nette\Utils\ArrayHash::from((array)$ret);
		}

		return null;
	}


	public function getPaymentKey(): ?string
	{
		return $this->basket->payment;
	}

	public function getSubPaymentType(): ?string
	{
		return $this->basket->subPaymentType;
	}


	public function cleanAll($dbClean = true)
	{
		$this->cache = [];
		unset($this->basket->customerService);
		unset($this->basket->posteRestante);
		unset($this->basket->transport);
		unset($this->basket->payment);
		unset($this->basket->zasilkovna);
		unset($this->basket->subscriptionHash);
		if (isset($this->basket->vouchers)) {
			unset($this->basket->vouchers);
		}

		$this->basket->items = [];

		if ($dbClean) {
			$this->saveToDbBasket();
		}

		return true;
	}


	public function cleanStep1Data(): void
	{
		unset($this->basket->posteRestante);
		unset($this->basket->transport);
		unset($this->basket->payment);
	}


	public function isFreeTransportNotice()
	{
		// SUPCAL-682 smazat
		if ($this->mutation->id == Mutation::ID_CS_DEFAULT && TransportService::isFreeDeliveryPromoActive($this->mutation->id)) {
			return false;
		}

		$isFreeTr = false;
		foreach ($this->getItems() as $orderItems) {
			foreach ($orderItems as $orderItem) {
				if ($orderItem->variant) {
					if ($orderItem->variant->isFreeTransport) {
						$isFreeTr = true;
						break 2;
					}
				}
			}
		}
		if ($isFreeTr) {
			return false;
		} else {
			if ($this->getProductsPriceDPH() < $this->getTransportFreeFrom()) {
				return true;
			} else {
				return false;
			}
		}



	}

	public function getTotalPriceDPH()
	{
		if ($this->totalPriceDPH == 0) {
			$this->totalPriceDPH = 0;
			foreach ($this->getProducts() as $basketItem) {
				$this->totalPriceDPH += $basketItem->priceDPH;
			}

			if ($this->getTransportFreeFrom() > $this->totalPriceDPH) {
				if (($transport = $this->getTransport()) != null) {
					$this->totalPriceDPH += $transport['priceDPH'];
				}

				if (($payment = $this->getPayment()) != null) {
					$this->totalPriceDPH += $payment['priceDPH'];
				}
			}

			if ($voucherPrice = $this->getVoucherPrice()) {
				$this->totalPriceDPH += $voucherPrice;
			}
		}

		return $this->totalPriceDPH;
	}


	public function getTransportFreeFrom(): float
	{
		if ($this->transportFreeFrom == 0 && $this->transports->count() > 0) {
			foreach ($this->transports as $t) {
				$freeFrom = $t['freeFrom'];
				$this->transportFreeFrom = $freeFrom;
//				if (date('Y-m-d H:i:s') > MutationTransports::BREAK_DATE_FOR_DELIVERY_PRICE) {
//					$freeFrom = MutationTransports::DELIVERY_PRICE_AFTER_BREAKDATE[$this->mutation->id];
//				}
				if (isset($freeFrom) && ($freeFrom < $this->transportFreeFrom || $this->transportFreeFrom == 0)) {
					$this->transportFreeFrom = $freeFrom;
				}
			}
		}

		return $this->transportFreeFrom;
	}


	public function getTransportPriceDPH(): float
	{
		if ($this->getTransportFreeFrom() < $this->getProductsPriceDPH()) {
			return 0;
		}

		$transport = $this->getTransport();
		return $transport['priceDPH'] ?? 0;
	}

	public function getPaymentPriceDPH(): float
	{
		if (($payment = $this->getPayment()) != null) {
			return $payment['priceDPH'] ?? 0;
		}
		return 0;
	}

	/**
	 * for voucher
	 * @return float
	 */
	public function getProductsPriceDPH(): float
	{
		// cena produktu + priobednanych sluzeb, prodlozene zaruky a pojisteni
		if ($this->productsPriceDPH == 0) {
			foreach ($this->getProducts() as $basketItem) {
				$this->productsPriceDPH += $basketItem->priceDPH;
			}
		}

		return $this->productsPriceDPH;
	}

	public function setProductsPriceDPH(): void
	{
		$priceDPH = 0;
		foreach ($this->getProducts() as $basketItem) {
			$priceDPH += $basketItem->priceDPH;
		}
		$this->productsPriceDPH = $priceDPH;
	}


	public function resetAllPriceDPH(): void
	{
		$this->resetProductsPriceDPH();
		$this->resetServicesPriceDPH();
		$this->resetTransportPriceDPH();
		$this->resetBothPriceDPH();
	}


	public function resetProductsPriceDPH(): void
	{
		$this->productsPriceDPH = 0;
	}


	// pro voucher - pujceni, skoleni
	public function getServicesPriceDPH(bool $erase = false): float
	{
		if ($erase) {
			$this->servicesPriceDPH = 0;
		}

		// cena produktu + priobednanych sluzeb, prodlozene zaruky a pojisteni
		if ($this->servicesPriceDPH == 0) {
			foreach ($this->getServices() as $basketItem) {
				$this->servicesPriceDPH += $basketItem->priceDPH;
			}
		}

		return $this->servicesPriceDPH;
	}


	public function resetServicesPriceDPH(): void
	{
		$this->servicesPriceDPH = 0;
	}


	public function resetTransportPriceDPH(): void
	{
		$this->transportPriceDPH = 0;
	}


	// pro voucher - pujceni, skoleni
	public function getBothPriceDPH(bool $erase = false): float
	{
		if ($erase) {
			$this->bothPriceDPH = 0;
		}

		// cena produktu + priobednanych sluzeb, prodlozene zaruky a pojisteni
		if ($this->bothPriceDPH == 0) {
			foreach ($this->getItems() as $basketItems) {
				foreach ($basketItems as $basketItem) {
//					if (isset($basketItem->priceDPH)) {
						$this->bothPriceDPH += $basketItem->priceDPH;
//					}
				}
			}
		}

		$this->bothPriceDPH += $this->getPaymentPriceDPH();
		$this->bothPriceDPH += $this->getTransportPriceDPH();
		return $this->bothPriceDPH;
	}

	public function resetBothPriceDPH(): void
	{
		$this->bothPriceDPH = 0;
	}


	public function addVoucherCode(VoucherCode $voucherCode): void
	{
		if ($voucherCode->voucher->useOnceByUser)
		{
			if (!$this->userModel->getUser()->isLoggedIn()) {
				throw new VoucherCodeException(VoucherCodeModel::ERROR_NOT_LOGGED);
			}

			if ($this->voucherCodeUsesModel->usedByUser($voucherCode, $this->getUserModel()->getUser())){
			throw new VoucherCodeException(VoucherCodeModel::ERROR_USED);
			}
		}

		if ($voucherCode->voucher->useCodeOnce)
		{
			// Voucher se smi pouzit pouze jednou
			if ($voucherCode->isUsed) {
				// voucher je jiz jednou pouzit, neni ho mozne pouzit vicekrat
				throw new VoucherCodeException(VoucherCodeModel::ERROR_USED);
			}
		}

		$this->basket->vouchers[$voucherCode->id] = $voucherCode->id;
	}

	/**
	 * @return EmptyCollection|(VoucherCode[]|ICollection)
	 */
	public function getVoucherCodes(array $filter = []): ICollection|EmptyCollection
	{
		if (isset($this->basket->vouchers) && (bool) $this->basket->vouchers) {
			return $this->orm->voucherCode->findBy(['id' => $this->basket->vouchers] + $filter);
		}

		return new EmptyCollection();
	}

	public function getVoucherDiscount(VoucherCode $voucherCode)
	{
		$discount = 0;

		// podle typu pouzito voucheru - rozhodnout jakou sumu odecteme
		switch ($voucherCode->voucher->application) {
			case 'product':
				$sum = $this->getProductsPriceDPH();
				break;

			case 'service':
//				$sum = $this->getServicesPriceDPH();
				$sum = $this->getTransportPriceDPH();
				break;

			default:
				$sum = $this->getBothPriceDPH();
				break;
		}

		if ($voucherCode->specialPriceDPH) {
			// voucher má svoji vlastni cenu
			$discount = $voucherCode->specialPriceDPH;
		} else {
			if ($voucherCode->voucher->type == "amount") {
				$discount += $voucherCode->voucher->discountAmount;

			} elseif ($voucherCode->voucher->type == "percent") {
				$discount += $sum * ($voucherCode->voucher->discountPercent / 100);
			}
		}

		if ($discount > $sum) {
			$discount = $sum;
		}

		return round($discount, $this->mutation->roundPositions);
	}


	private function getVoucherPrice()
	{

		if ($this->basket->vouchers) {
			$priceDPH = 0;
			foreach ($this->basket->vouchers as $voucherCodeId) {
				$voucherCode = $this->orm->voucherCode->getById($voucherCodeId);
				if ($voucherCode) {
					$priceDPH += $this->getVoucherDiscount($voucherCode);
				}
			}

			return -$priceDPH;
		}
		return false;
	}

	public function recalculateVoucherPrice()
	{
		$this->getVoucherPrice();
	}


	public function removeVoucher(VoucherCode $voucherCode): void
	{
		unset($this->basket->vouchers[$voucherCode->id]);
	}


	public function validateVouchers(): string|null
	{
		$voucherCodes = $this->getVoucherCodes();
		if (!($voucherCodes instanceof EmptyCollection)) {
			foreach ($voucherCodes as $voucherCode) {
				try {
					$this->voucherCodeModel->isValid($voucherCode, $this, true);
				} catch (VoucherCodeException $e) {
					$this->removeVoucher($voucherCode);

					return $e->getMessage();
				}
			}
		}

		return null;
	}

	public function setPosteRestante($selectedPost)
	{
		$this->basket->posteRestante = $selectedPost;
	}


	public function getPosteRestante()
	{
		return $this->basket->posteRestante;
	}



	public function getCheapestTransport()
	{
		$possibleTransports = $this->mutation->transportsArray;

		if (isset($possibleTransports[$this->configService->getParam('shop', 'cheapestTransport')])) {
			return $possibleTransports[$this->configService->getParam('shop', 'cheapestTransport')];
		} else {
			return false;
		}
	}


	public function isPaymentAndTransportValid(): bool
	{
		$payments = $this->mutation->paymentsArray;
		if (isset($payments[$this->getPaymentKey()])) {
			$transportArr = explode(' ', $payments[$this->getPaymentKey()]['transports']);

			$transportKey = $this->getTransportKey();
			if ((bool)preg_match('/^personal-[0-9]+/', (string)$transportKey)) {
				$transportKey = 'personal';
			}

			if (in_array($transportKey, $transportArr, true)) {
				return true;
			}
		}

		return false;
	}


	public function getFutureOrder() : Order
	{
		if (!isset($this->cache['futureOrder'])) {
			$this->cache['futureOrder'] = $this->orderModel->getNewOrder([], $this, $this->mutation);
		}
		return $this->cache['futureOrder'];
	}


	public function cleanAbandonedBasket(): void
	{
		$hash = $this->lastVisitCookie->getUserUniqId();
		$items = $this->orm->basketItem->findBy(['hash' => $hash]);
		foreach ($items as $item) {
			$this->orm->basketItem->remove($item);
		}
		$this->orm->basketItem->flush();
	}


	private function saveToDbBasket()
	{
		$hash = $this->lastVisitCookie->getUserUniqId();

		if (!$hash) {
			return '';
		}

		$basketItemMap = $this->orm->basketItem->findBy(['hash' => $hash])->fetchPairs('productHash', null);

		foreach ($this->basket->items as $productHash => $data) {
			if (isset($basketItemMap[$productHash])) {
				// produkt je nastaveny zmen jeho amount
				$basketItem = $basketItemMap[$productHash];
				unset($basketItemMap[$productHash]);

			} else {
				// neni nastaveny vytvor novy
				$basketItem = new \App\Model\BasketItem();
				$this->orm->basketItem->attach($basketItem);
			}
			$basketItem->amount = $data['amount'];
			$basketItem->sort = $data['sort'] ?? 0;
			if (isset($data['data'])) {
				$basketItem->params = json_encode($data['data']);
			} else {
				$basketItem->params = '{}';
			}

			$basketItem->hash = $hash;
			$basketItem->productHash = $productHash;
			$basketItem->created = new \DateTimeImmutable();
			$this->orm->persistAndFlush($basketItem);
		}

		if ($this->isSubscriptionEnabled && !$this->containsSubscriptionProducts()) {
			$this->basket->subscriptionHash = null;
		}

		foreach ($basketItemMap as $oldBasketItem) {
			$this->orm->basketItem->removeAndFlush($oldBasketItem);
		}

		$this->setProductsPriceDPH();

		$this->setIsOversizedBasket();
	}


	public function validateData()
	{
		$order = $this->getFutureOrder();

		foreach ($order->products as $product) {
			$test = $this->orderItemModel->validateProductOrderItem($product);
			if ($test['status'] != 'ok') {
				if (isset($test['presentToRemove']) && $test['presentToRemove']) {
					$this->removeSub($product->uniqueKey, $test['presentToRemove']);
				}
				return $test;
			}
		}

		return true;
	}


	private function getPossibleAmount(ProductVariant $variant, $amount)
	{
		if (!$variant->voucher && $variant->totalSupplyCount < $amount) {
			$amount = $variant->totalSupplyCount;
		}
		return $amount;

	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;

		$testingMode = false;
		if (isset($_COOKIE['CLIENT_TESTING']) && (bool)$_COOKIE['CLIENT_TESTING']) {
			$testingMode = true;
		}

		if ($this->isSubscriptionEnabled && $this->containsSubscriptionProducts()) {
			$this->transports = $this->transportService->getAllForSubscription($this->mutation, true, $testingMode, $this->getProductsPriceDPH());
		} else {
			$this->transports = $this->transportService->getAll($this->mutation, true, $testingMode, $this->getProductsPriceDPH());
		}
		$this->payments = $this->paymentService->getAll($this->mutation, true, $testingMode);
		$this->paymentsForSubscription = $this->paymentService->getSubscriptionPayments($this->mutation, true, $testingMode);

		$this->getTransportPrices();
	}


	public function setOnlyValidTransports(): void
	{
		$this->orderModel->recalculateTransportByOrderItems($this, $this->getFutureOrder());
		if ($this->transports->count() == 1) {
			$this->transport =  (string)array_key_first((array)$this->transports);
		}
	}


	public function getTransportPrices()
	{
		foreach ($this->transports as $deliveryType => $transport) {
			if ( $transport['priceDPH'] == 0) {
				continue;
			}

			// projiti polozek a secteni ceny
			$isFreeTr = false;
			foreach ($this->getProducts() as $product) {
				//foreach ($orderItems as $orderItem) {
					if ($product->variant) {
						if ($product->variant->isFreeTransport) {
							$isFreeTr = true;
							break 2;
						}
					}
				//}
			}
			if ($isFreeTr) {
				$this->transports[$deliveryType]['priceDPH'] = 0;
			}
		}
	}

	public function getAttachedGiftsForBasket(): array|null
	{
		$productGifts = $this->orm->product->findBy([
			'isBasketGift' => 1,
			'mutation' => $this->mutation,
		])->resetOrderBy()->orderBy('minPriceOrderBasketGift', ICollection::ASC);

		if ($productGifts->count() === 0) {
			return null;
		}

		$productGiftsGrouped = [
			'available' => [],
			'desire' => [],
		];

		$currentLevel = 0;

		$minBorder = null;
		foreach ($productGifts as $product) {
			if ($product->minPriceOrderBasketGift === null || $this->getProductsPriceDPH() >= $product->minPriceOrderBasketGift) {
				$productGiftsGrouped['available'][] = $product;
				$currentLevel = max($product->minPriceOrderBasketGift ?? 0, $currentLevel);
			} else {
				if ($minBorder === null) {
					$minBorder = $product->minPriceOrderBasketGift;
				}

				$productGiftsGrouped['desire'][$product->minPriceOrderBasketGift][] = $product;
			}
		}

		foreach ($productGiftsGrouped as $k => $items) {
			$productGiftsGrouped[$k] = new ArrayIterator($items);
		}

		return [
			'currentLevel' => $currentLevel,
			'border' => $minBorder,
			'available' => $productGiftsGrouped['available'],
			'desire' => $productGiftsGrouped['desire'],
		];
	}

	public function getSelectedGiftExtra()
	{
		$ret = array();

		if (isset($this->basket->items)) {
			foreach ($this->basket->items as $uniqueKey => $data) {
				$basketItems = $this->parseKey($uniqueKey, $data['amount']);
				if (isset($basketItems[BasketItem::TYPE_GIFTEXTRA])) {
					$ret[$basketItems[BasketItem::TYPE_GIFTEXTRA]['id']] = $basketItems[BasketItem::TYPE_GIFTEXTRA];
				}

			}
		}

		return $ret;
	}


	public function getSampleCount()
	{
		$cnt = 0;
		if (isset($this->basket->items)) {
			foreach ($this->basket->items as $uniqueKey => $data) {
				if (preg_match('/^S([0-9]+)/', $uniqueKey)) {
					$cnt += $data['amount'];
				}
			}
		}

		return $cnt;
	}


	public function isValidTransport(): bool
	{
		$transport = $this->getTransport();
		if (isset($transport) && $transport['key'] == Order::TRANSPORT_ZASILKOVNA) {
			if (!isset($this->getZasilkovna()['id'])) {

				return false;
			}
		}

		if (isset($transport) && $transport['key'] == Order::TRANSPORT_PPL_PARCEL) {
			if (!isset($this->getPplParcel()['code'])) {
				return false;
			}
		}

		return true;
	}

	public function setFirstSubscriptionOrder(bool $firstSubscriptionOrder): void
	{
		$this->firstSubscriptionOrder = $firstSubscriptionOrder;
	}

	public function isFirstSubscriptionOrder(): bool
	{
		return $this->isSubscriptionEnabled && $this->getUser() === null || ($this->getUser() instanceof User && $this->getUser()->firstSubscriptionOrder);
	}

	public function isOversizedBasket(): bool
	{
		return $this->hasOversizedProducts;
	}

	public function setIsOversizedBasket(): void
	{
		foreach ($this->getProducts() as $product) {
			if ($product->variant->isOversize) {
				$this->hasOversizedProducts = true;
				$this->setTransport();
				return;
			}
		}
	}

	public function setPplParcel(string $code = null, ?string $locationInfo = null, ?bool $codAllowed = false): void
	{
		$country = $this->mutation->langCode;

		if ($code === null) {
			$this->basket->pplParcel = null;
			return;
		}
		// we need to remove KM if starting by that
		$code = preg_replace('/^KM/', '', $code);
		$this->basket->pplParcel[$country]['code'] = $code;
		$this->basket->pplParcel[$country]['infoText'] = $locationInfo;
		$this->basket->pplParcel[$country]['codAllowed'] = $codAllowed;
	}


	public function getPplParcel(): array
	{
		$country = $this->mutation->langCode;
		if (isset($this->basket->pplParcel[$country])) {
			return $this->basket->pplParcel[$country];
		}

		return [];
	}

	public function setZasilkovna(string $key = null, string $desc = null): void
	{
		if ($key === null) {
			$this->basket->zasilkovna = null;
			return;
		}

		$country = $this->mutation->langCode;
		$this->basket->zasilkovna[$country]['id'] = $key;
		$this->basket->zasilkovna[$country]['infoText'] = $desc;
	}


	public function getZasilkovna(): array
	{
		$country = $this->mutation->langCode;
		if (isset($this->basket->zasilkovna[$country])) {
			return $this->basket->zasilkovna[$country];
		}

		return [];
	}

	/**
	 * @return UserModel
	 */
	public function getUserModel(): UserModel
	{
		return $this->userModel;
	}

	public function getUser(): ?User
	{
		return $this->user;
	}

	public function setUser(?User $user): void
	{
		$this->user = $user;

		$this->isFirstSubscriptionOrder = $this->isFirstSubscriptionOrder();
	}

	public function getSubscription(): ?Subscription
	{
		bd('basket subsHash');
		bd($this->getBasketSubscriptionHash());
		if ($this->containsSubscriptionProducts()) {
			return $this->orm->subscription->getBy(['hash' => $this->getBasketSubscriptionHash()]);
		}
		return null;
	}

}


