<?php declare(strict_types=1);

namespace SuperKoderi;

use App\Model\Mutation;
use App\Model\Order;
use App\Model\Orm;
use App\Model\Task;
use App\Model\TaskProcessor;
use Exception;
use Nette\Http\SessionSection;
use stdClass;
use SuperKoderi\Email\ICommonFactory;
use SuperKoderi\User\Security\User;
use ZipArchive;

class InvoiceExporter implements TaskProcessor
{

	/** @var Mutation $mutation */
	private $mutation;

	protected const EXPORT_DIR = 'invoice_export';

	public function __construct(
		private readonly Orm $orm,
		private readonly User $user,
		private readonly InvoiceService $invoiceService,
		private readonly ICommonFactory $commonEmailFactory,
		private readonly ConfigService $configService,
		private readonly MutationHolder $mutationHolder)
	{
		$this->mutation = $this->orm->mutation->getById(1);
	}

	protected function getDateFormat(): string
	{
		return 'j.n.Y';
	}

	public function getZipFilePath(Task $task): string
	{
		return TEMP_DIR . '/' . self::EXPORT_DIR . '/invoices_' . $task->id . '.zip';
	}

	public function getOrderCount(Task $task): int
	{
		$data = $task->dataUnserialized;
		return $this->orm->order->findByFilter($data, $this->user, 'id desc')->countStored();
	}

	public function processTask(Task $task): string
	{
		$output = 'Invoice export: ';
		$this->orm->task->setProcessing($task);

		//get orders
		$data = $task->dataUnserialized;


		$filteredCount = $this->getOrderCount($task);
		$maxCount = (int)$this->configService->getParam('invoiceExport', 'limit');

		/** @var Order[] $orders */
		$orders = $this->orm->order->findByFilter($data, $this->user, 'id')->limitBy($maxCount)->fetchAll();

		//prepare tmp dir
		$dir = TEMP_DIR . '/' . self::EXPORT_DIR;
		$csvFilePath = $dir . '/invoices.csv';

		if (!file_exists($dir)) {
			mkdir($dir, 0777);
		} else {
			$this->clearFiles($dir, $csvFilePath, true);
		}


		//generate invoices
		foreach ($orders as $order) {
			$this->mutationHolder->setMutation($order->mutation);
			if ($order->invoiceNumber !== null) {
				$pdfInvoice = $this->invoiceService->create($order);
				$pdfInvoice->Output($dir . '/' . $order->invoicePdfFileName, 'F');
			}
		}

		//create CSV
		$csvData = [[
			'č. objednávky', 'č.faktury',
			'datum objednávky', 'datum vystavení faktury', 'datum splatnosti faktury',
			'zákazník',
			'celková cena s DPH', 'DPH', 'celková cena bez DPH'
		]];

		foreach ($orders as $order) {
			$this->mutationHolder->setMutation($order->mutation);
			if ($order->invoiceNumber !== null) {
				$csvData[] = [
					$order->number,
					$order->invoiceNumber,
					$order->created->format($this->getDateFormat()),
					$order->invoiceDate->format($this->getDateFormat()),
					$order->dueDate->format($this->getDateFormat()),
					$order->firstname . ' ' . $order->lastname,
					round($order->totalPriceDPH, 2),
					round($order->totalPriceDPH, 2) - round($order->totalPrice, 2),
					round($order->totalPrice, 2),
				];
				$output .= $order->invoiceNumber . ', ';
			}
		}
		if ($maxCount < $filteredCount) {
			$csvData[] = [
				'Vyfiltrovaný počet objednávek byl  ' . $filteredCount . '. Bylo vyexportováno pouze prvních ' . $maxCount . ' objednávek.',
			];
		}

		$csvFile = fopen($csvFilePath, 'w');
		foreach ($csvData as $row) {
			fputcsv($csvFile, $row);
		}
		if (!fclose($csvFile)) {
			throw new Exception('Cannot save CSV file: ' . $csvFilePath);
		}

		//pack it all
		$zipFilePath = $this->getZipFilePath($task);
		$zip = new ZipArchive();
		if ($zip->open($zipFilePath, ZipArchive::CREATE) !== TRUE) {
			throw new Exception('Cannot create ZIP file: ' . $zipFilePath);
		}

		$pdfFiles = glob($dir . '/*.pdf');
		foreach ($pdfFiles as $pdfFile) {
			$zip->addFile($pdfFile, basename($pdfFile));
		}
		$zip->addFile($csvFilePath, basename($csvFilePath));

		$zip->close();

		$this->clearFiles($dir, $csvFilePath, false);

		//send email
		$commonEmail = $this->commonEmailFactory->create();

		$statuses = array_keys(Order::getConstsByPrefix('STATUS_', 'order_status_'));
		$statuses = isset($data->data->status) ? array_filter($statuses, function ($item) use ($data) {
			return in_array($item, $data->data->status);
		}) : [];

		$mailData = [
			'taskId' => $task->id,
			'filter' => [
				'from' => (isset($data->data->fromDate) && is_object($data->data->fromDate)) ? $data->data->fromDate->format('j. n. Y') : null,
				'to' => (isset($data->data->toDate) && is_object($data->data->fromDate)) ? $data->data->toDate->format('j. n. Y') : null,
				'mutation' => isset($data->data->mutation) ? $this->orm->mutation->getById($data->data->mutation)->name : null,
				'status' => count($statuses) > 0 ? implode(',', $statuses) : null,
				'fulltext' => (isset($data->data->fulltext) && strlen($data->data->fulltext) > 0) ? $data->data->fulltext : null,
			],
		];

		$commonEmail->send('', $this->mutation->getSetting(Orm\MutationSetting\SystemEmail::OrderExportEmail), 'Export faktur', '', $mailData, 'invoiceExport');

		$this->orm->task->setDone($task);
		return trim($output, ' ,');
	}

	public function storeFilter2TaskData(SessionSection $filterSession): stdClass
	{
		$data = new stdClass();
		$data->data = $filterSession->data;
		$data->defaults = $filterSession->defaults;

		return $data;
	}

	protected function clearFiles(string $dir, string $csvFilePath, bool $zipFile): void
	{
		if (is_array($oldFiles = glob($dir . '/*.pdf'))) {
			foreach ($oldFiles as $oldFile) {
				@unlink($oldFile);
			}
		}
		@unlink($csvFilePath);

		if ($zipFile) {
			if (is_array($oldFiles = glob($dir . '/*.zip'))) {
				foreach ($oldFiles as $oldFile) {
					@unlink($oldFile);
				}
			}
		}
	}

}
