	services:
		identityMap: SuperKoderi\IdentityMap

		authenticator: SuperKoderi\Authenticator
		acl: SuperKoderi\Acl
		user: SuperKoderi\User\Security\User

#		odkomentovani: vypne cache
#		cacheStorage:
#			factory: Nette\Caching\Storages\DevNullStorage

#		cacheStorage:
#			factory: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

		translatorDB:
			class: SuperKoderi\TranslatorDB

		mailChimpWrapper: SuperKoderi\MailChimpWrapper()

		translator:
			factory: SuperKoderi\Translator(%config.adminLang%, %config%)

		configService: SuperKoderi\ConfigService(%config%)
		database:
			factory: <PERSON>bi\Connection(%database%)

		routerFactory: RouterFactory
		router: @routerFactory::createRouter

		menuService:
			class: SuperKoderi\MenuService

		commentService:
			class: SuperKoderi\CommentService

		reviewService:
			class: SuperKoderi\ReviewService

		imageResizer: SuperKoderi\ImageResizer(%config.imageSizes%)

		newsletterService: SuperKoderi\NewsletterService
		newsletterCampaignService: App\Model\NewsletterCampaign\NewsletterCampaignService
		newsletterQueueService: SuperKoderi\NewsletterQueueService
		newsletterEmailService: SuperKoderi\NewsletterEmailService

		stringService: SuperKoderi\StringService
		invoiceService: SuperKoderi\InvoiceService
		documentService: App\Model\Document\DocumentService

		# search
		searchServiceLike:
			class: SuperKoderi\SearchService

		searchService:
			class: SuperKoderi\ISearchService
			factory: SuperKoderi\SearchHelper::getSearchService()

		searchHelper: SuperKoderi\SearchHelper

		templateFactory: SuperKoderi\TemplateFactory

		VisualPaginator:
			class: \VisualPaginator
			autowired: no

		- \SuperKoderi\CzechPost
		- SuperKoderi\IFileLockFactory

		- \Curl\Curl
		- \SuperKoderi\INewsletterFactory
		- \SuperKoderi\INewsletterEmailFactory
		- \SuperKoderi\INewsletterQueueFactory
		- \SuperKoderi\Components\INewsletterBlacklistFactory

		- \SuperKoderi\PosteRestanteService
		- \SuperKoderi\Basket
		- \SuperKoderi\TransportService
		- \SuperKoderi\PaymentService

		- App\Model\ProductTreeModel
		- \App\Model\ProductReviewModel
		- \App\Model\TreeModel
		- \App\Model\FileModel
		- \App\Model\VoucherCodeUsesModel
		- \App\Model\VoucherCodeModel
		- \App\Model\VoucherModel
		- \App\Model\OrderModel
		- \App\Model\EntityLogModel
		- \App\Model\OrderItemModel
		- \App\Model\CreditNoteModel
		- \App\Model\AliasModel
		- \App\Model\BannerModel
		- \App\Model\AliasHistoryModel
		- \App\Model\UserModel
		- \App\Model\UserGroupModel
		- \App\Model\UserGroupDiscountModel
		- \App\Model\UserHashModel
		- \App\Model\UserAnimalModel
		- \App\Model\LibraryImageModel
		- \App\Model\LibraryTreeModel
		- \App\Model\ProductVariantModel
		- \App\Model\ProductDiscountModel
		- \App\Model\ProductUserAnimalQueueModel
		- \App\Model\ProductUserScoreModel
		- \App\Model\ProductModel
		- \App\Model\EmailTemplateModel
		- \App\Model\MutationBreedsModel
		- \App\Model\MutationAgreementModel
		- \App\Model\Orm\MutationSetting\MutationSettingModel
		- \App\Model\SubscriptionModel
		- \App\Model\SubscriptionLogModel
		- \App\Model\SubscriptionItemModel
		- \App\Model\SubscriptionOrderModel
		- \App\Model\SubscriptionOrderLogModel
		- \App\Model\SubscriptionPaymentModel
		- \App\Model\StringModel
		- \App\Model\LotteryTicketModel

		- SuperKoderi\Model\PPL\PplApiClient(%config.pplApi.url%, %config.pplApi.keys%)
		- SuperKoderi\DeliveryStatusService

		- \SuperKoderi\Model\Pdf\IGeneratePdfFactory
		- \SuperKoderi\IPagesFactory
		- \SuperKoderi\IParametersFactory
		- \SuperKoderi\IBasketItemFactory


		- SuperKoderi\Model\Router\IRouterFilterFactory
		- App\Model\Link\SkLinkRender
		- \App\Model\ParameterModel
		- \App\Model\ParameterValueModel
		- \SuperKoderi\IFilterFactory
		- \SuperKoderi\IDbCacheStorageFactory
		- \SuperKoderi\IFilterCacheFactory
		- \SuperKoderi\Heureka
		- \SuperKoderi\Template\Creator
		- \SuperKoderi\ICacheServiceFactory
		- \SuperKoderi\DbalLog("%appDir%/../nettelog/", "mysql")
		- App\Model\LastVisitCookie
		- \SuperKoderi\SkLinkGenerator
		- \SuperKoderi\EasyMessages
		- App\Model\Orm\BasketItem\BasketItemModel

		# Feed
		- \SuperKoderi\Feed\IHeurekaFactory
		- \SuperKoderi\Feed\IGoogleFactory

		- \SuperKoderi\Export\IPublishedProductsExportFactory

		- App\Model\Link\LinkSeo
		- \SuperKoderi\Console\Model\Sitemap
		- \SuperKoderi\Console\Model\Robots
		- \SuperKoderi\CacheTags
		- \SuperKoderi\OrderMailService
		- \SuperKoderi\SubscriptionMailService
		- \SuperKoderi\MutationHolder
		- SuperKoderi\Model\Router\Filter
		- SuperKoderi\Model\Router\FilterLang
		- SuperKoderi\Model\Router\FilterAlias
		- SuperKoderi\Model\Router\FilterCommonParameters
		- SuperKoderi\Model\Router\FilterFilterParams
		- SuperKoderi\Model\Router\FilterSeolink
		- SuperKoderi\Model\Router\FilterVariantId

#		- \SuperKoderi\ElasticSearch\IndexModel()
#
#		- \SuperKoderi\ElasticSearch\Tree\Model(%config.elasticSearch.indexes.tree%)
#		- \SuperKoderi\ElasticSearch\Tree\Service
#		- \SuperKoderi\ElasticSearch\Tree\Repository
#
#		- \SuperKoderi\ElasticSearch\Product\Model(%config.elasticSearch.indexes.product%)
#		- \SuperKoderi\ElasticSearch\Product\Service
#		- \SuperKoderi\ElasticSearch\Product\Repository
#
#		- \SuperKoderi\ElasticSearch\Seolink\Model(%config.elasticSearch.indexes.seolink%)
#		- \SuperKoderi\ElasticSearch\Seolink\Service
#		- \SuperKoderi\ElasticSearch\Seolink\Repository
#
#		- \SuperKoderi\ElasticSearch\ConfigurationHelper


		- \SuperKoderi\LinkFactory
		- App\Model\RedirectModel
		- App\Model\PlaceModel
		- App\Model\MutationModel
		- \SuperKoderi\ImageResizerWrapper
		- App\Model\Url\UrlChecker
		- \SuperKoderi\InvoiceExporter
		- App\Model\NewsletterCampaignMailer
		- \SuperKoderi\Gtm
		- App\Model\CustomField\LazyValueFactory
		- \SuperKoderi\CreditNoteExporter
		-
			factory: \SuperKoderi\ParamText
			inject: true

		-
			factory: App\Model\SeolinkModel
			inject: true

		-
			factory: \App\Model\MessageModel
			inject: true
		# TODO resolve autoloading
		- SuperKoderi\MailChimp\MailChimpApiClient
		- SuperKoderi\MailChimp\MailChimpEcommerceWrapper

		- App\Model\Mutation\MutationsHolder

#		EMAILY
		- \SuperKoderi\Mailer\Base
		- \App\Model\GeneralMailFeatureService

		- \SuperKoderi\Email\ICommonFactory
		- \SuperKoderi\Email\IOrderConfirmationFactory
		- \SuperKoderi\Email\ISubscriptionMailFactory
		- \SuperKoderi\Email\ISendVoucherFactory
		- \SuperKoderi\Email\ISendGuideFactory

		- App\Model\Orm\FirewallItem\FirewallItemModel
		- App\Model\Rotator

#		PRICE LOGS
		- \SuperKoderi\Noviko\PriceLogs\ProductVariantPriceLogger

		nette.latteFactory:
			setup:
			- addFilter(timeAgoInWords, [SuperKoderi\Templating\Helpers, timeAgoInWords])
			- addFilter(plural, [SuperKoderi\Templating\Helpers, plural])
			- addFilter(niceDate, [SuperKoderi\Templating\Helpers, niceDate])
			- addFilter(price, [SuperKoderi\Templating\Helpers, price])
			- addFilter(priceFormat, [SuperKoderi\Templating\Helpers, priceFormat])
			- addFilter(texy, [SuperKoderi\Templating\Helpers, texy])
			- addFilter(skDate, [SuperKoderi\Templating\Helpers, skDate])
			- addFilter(prepareStrJs, [SuperKoderi\Templating\Helpers, prepareStrJs])
			- addFilter(clear, [SuperKoderi\Templating\Helpers, clear])
			- addFilter(copyright, [SuperKoderi\Templating\Helpers, copyright])
			- addFilter(icon, [SuperKoderi\Templating\Helpers, icon])
			#- addFilter(lineExploder, [SuperKoderi\Templating\Helpers, lineExploder])
			#- addFilter(exploder, [SuperKoderi\Templating\Helpers, exploder])
			- addFilter(stock, [SuperKoderi\Templating\Helpers, stock])
			- addFilter(phoneFormat, [SuperKoderi\Templating\Helpers, phoneFormat])
			- addFilter(obfuscateEmailAddresses, [SuperKoderi\Templating\Helpers, obfuscateEmailAddresses])
#			- setTempDirectory("") # vypnuti cache u latte
