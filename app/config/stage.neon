redis:
	connection!: []

parameters:
	stageName: "stage"

	basicAuth:
		superadmin: "$2y$10$RGiAZ852VKJ.YEwKtorOseYv9rnR6PO7JEVYd9u.hPdONgTgekyfm"

	config:
		isDev: true
		env: stage

		adminMail: [<EMAIL>, <EMAIL>]
		orderMail: [<EMAIL>]
		emailTo:
			allowedDomains: ['superkoders.com', 'covetrus.com'] #'mojecalibra.cz', 'mojacalibra.sk' - this is dangerous on dev
			allowedEmails: ['<EMAIL>']
			defaultEmail: <EMAIL>

		domainUrl: "https://calibra-stage.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena
		domainUrlPdf: "https://calibra-stage.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena
		adminAlias: superadmin
		adminTitle: Superadmin

		robots: "noindex, nofollow" #noindex, nofollow TODO zmenit jak se spusti ostra domena
		googleAnalyticsCode: "" # PRODUKČNÍ KOD
		googleAnalyticsName: ""

		emailFromName: "Calibra stage"
		emailFrom: <EMAIL> # musi byt - SMTP

		orderEmail:
			mailFrom: "<EMAIL>"
			mailFromName: "superadmin"

		gtm:
			enabled: true

		installmentCode: "..."

		elasticSearch:
			connection:
				host: localhost
				port: 9200
			enabled: true

		#splatky
#		installmentUrl: "https://www.cetelem.cz:8654/cetelem2_webshop.php/zadost-o-pujcku/on-line-zadost-o-pujcku"
		#ostra url
		#installmentUrl: "https://www.cetelem.cz/cetelem2_webshop.php/zadost-o-pujcku/on-line-zadost-o-pujcku"
		installmentUrl: ""
		newsletter:
			mailFrom: "<EMAIL>"
			mailFromName: "XXX"

#		crispSiteId: '23c479f5-e815-4792-8069-b5ed3abf1ad2'
#		heurekaOverenoKey: '330caecc83203912fc9018d0ae342b60' #ostry kod

		insertEmptyTranslate: true                                  #kdyz neni zalozeny prekad v DB -> vytovri se novy

		lang:
			cs:
				domain: https://calibra-stage.vs3.superkoderi.cz
			en:
				domain: https://calibra-stage-en.vs3.superkoderi.cz
			ae:
				domain: https://calibra-stage-ae.vs3.superkoderi.cz
			sk:
				domain: https://calibra-stage-sk.vs3.superkoderi.cz
			hr:
				domain: https://calibra-stage-hr.vs3.superkoderi.cz
			sl:
				domain: https://calibra-stage-sl.vs3.superkoderi.cz
			hu:
				domain: https://calibra-stage-hu.vs3.superkoderi.cz
			it:
				domain: https://calibra-stage-it.vs3.superkoderi.cz
			es:
				domain: https://calibra-stage-es.vs3.superkoderi.cz
			pl:
				domain: https://calibra-stage-pl.vs3.superkoderi.cz
			uk:
				domain: https://calibra-stage-uk.vs3.superkoderi.cz
			ie:
				domain: https://calibra-stage-ie.vs3.superkoderi.cz
			in:
				domain: https://calibra-stage-in.vs3.superkoderi.cz
			lt:
				domain: https://calibra-stage-lt.vs3.superkoderi.cz
			cl:
				domain: https://calibra-stage-cl.vs3.superkoderi.cz
			at:
				domain: https://calibra-stage-at.vs3.superkoderi.cz
			ro:
				domain: https://calibra-stage-ro.vs3.superkoderi.cz
			lv:
				domain: https://calibra-stage-lv.vs3.superkoderi.cz
			nl:
				domain: https://calibra-stage-nl.vs3.superkoderi.cz
			be-nl:
				domain: https://calibra-stage-be-nl.vs3.superkoderi.cz
			be-fr:
				domain: https://calibra-stage-be-fr.vs3.superkoderi.cz
			ch-de:
				domain: https://calibra-stage-ch-de.vs3.superkoderi.cz
			ch-fr:
				domain: https://calibra-stage-ch-fr.vs3.superkoderi.cz
			gr:
				domain: https://calibra-stage-gr.vs3.superkoderi.cz
			sr:
				domain: https://calibra-stage-rs.vs3.superkoderi.cz
			us:
				domain: https://calibra-stage-us.vs3.superkoderi.cz

		social:
			facebook:
				params:
					appId: '1906427749758516'
					appSecret: '579ef94ecedd81d63cadba0a4ccb78e3'
			google:
				params:
					clientId: '899678131476-7ntd3tvvq8ankuvikmd51nekp6gj375i.apps.googleusercontent.com'
					clientSecret: '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'

		gopay: # STAGE
			cs:
				goid: '8710217153'
				clientId: '1790281933'
				clientSecret: 'cKuKTkfd'
				isProduction: FALSE
				language: GoPay\Definition\Language::CZECH
			sk:
				goid: '8197918707'
				clientId: '1435452832'
				clientSecret: 'krthM2BB'
				isProduction: FALSE
				language: GoPay\Definition\Language::SLOVAK
			pl:
				goid: '8064752872'
				clientId: '1163844231'
				clientSecret: 'fahkSZGf'
				isProduction: FALSE
				language: GoPay\Definition\Language::POLISH
			ro:
				goid: '8710217153'
				clientId: '1790281933'
				clientSecret: 'cKuKTkfd'
				isProduction: FALSE
				language: GoPay\Definition\Language::ROMANIAN

extensions:
	sentry: Contributte\Logging\DI\SentryLoggingExtension

sentry:
	url: https://<EMAIL>/5971269
	options:
		environment: stage
		ignore_exceptions:
			- Nette\Application\AbortException

includes:
    - header.php

messenger:
    transport:
        elasticFront:
        	dsn: "sync://"
        clonerFront:
        	dsn: "sync://"
        elasticPriorityFront:
        	dsn: "sync://"
        failure:
        	dsn: "sync://"

elastica:
	config:
		host: localhost
		port: 9200

services:
	cacheStorage:
		factory: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

	mysql.panel: Dibi\Bridges\Tracy\Panel

	tracy.bar:
		setup:
			- @mysql.panel::register(@database)

tracy:
	bar:
		- Nette\Bridges\HttpTracy\SessionPanel
#			- Nette\Bridges\DITracy\ContainerPanel
#			- SuperKoderi\IdentityMapPanel

#	application:
#		catchExceptions: true
