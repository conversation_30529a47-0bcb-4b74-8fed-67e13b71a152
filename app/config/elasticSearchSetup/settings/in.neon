index:
	number_of_replicas: 0
analysis:
	filter:
		fileSynonym:
			type: "synonym"

		stopWords:
			type: "stop"
			stopwords: ["_english_"]

		customCommonGrams:
			type: "common_grams"
			common_words: ["pcs"]

		mynGram:
			type: "ngram"
			min_gram: 2
			max_gram: 3
			token_chars: ["letter", "digit"]

		customEdgeNgram:
			type: "edge_ngram"
			min_gram": 2
			max_gram: 6

		customWordDelimiter:
			type: "word_delimiter"
			catenate_all: "true"

	analyzer:
		dictionary:
			filter: ["lowercase","stopWords","unique","asciifolding"]
			type: custom
			tokenizer: standard

		synonym:
			filter: ["lowercase","stopWords", "fileSynonym", "unique", "asciifolding"]
			type: "custom"
			tokenizer: "standard"

		customWordDelimiter:
			filter: ["stop","customWordDelimiter","asciifolding","lowercase","unique"]
			type: "custom"
			tokenizer: "standard"

		customCommonGrams:
			filter: ["asciifolding","lowercase","stop","customCommonGrams","unique"]
			type: "custom"
			tokenizer: "standard"

		customEdgeNgram:
			filter: ["asciifolding","lowercase","stop","customEdgeNgram","unique",]
			type: "custom"
			tokenizer: "standard"
