
all:
	properties:
		kind:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		type:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		nameSort:
			type: "text"
			fielddata: true
		name:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		filter:
			properties:
				publishDate:
					type: date

common:
	properties:
		name:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		annotation:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'

		content:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"

product:
	properties:
		nameSort:
			type: "keyword"
		name:
			type: "text"
			fielddata: true
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		content:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		annotation:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"

		variants:
			type: "nested"


