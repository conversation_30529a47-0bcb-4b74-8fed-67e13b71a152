extensions:
	cf: App\Model\CustomField\CustomFieldsExtension

cf:
	definitions:
		text:
			type: text
			label: "Text"
		photos:
			type: image
			label: "Galerie"
			multiple: true
		content:
			type: tinymce
			label: "Content"
		caption:
			type: text
			label: "Caption"
		description:
			type: text
			label: "Description"
		image:
			type: image
			label: "Image"
		file:
			type: file
			label: "Soubor"
		dateTime:
			type: dateTime
			label: "Datum a čas"
		page:
			label: "Page"
			type: suggest
			subType: "tree"
			url: @cf.suggestUrls.searchPage
		seoLink:
			label: "Seolink"
			type: suggest
			subType: "seolink"
			url: @cf.suggestUrls.searchSeoLink
		productVariant:
			label: "Product variant"
			type: suggest
			subType: "variant"
			url: @cf.suggestUrls.searchVariant
		color:
			label: "Color"
			type: select
			options: [
				{ label: "Hypoallergenic", value: "#00B2A9" },
				{ label: "Mobility Support", value: "#007398" }
				{ label: "Urinary Care", value: "#AA0061" },
				{ label: "Gastrointestinal", value: "#B36924" }
				{ label: "Vitality Support", value: "#F6BE00" },
				{ label: "Weight Management", value: "#E4002B" }
			]

################### fields ################################################
	fields:
		menuSub:
			type: group
			label: "Submenu settings"
			items:
				visibility:
					label: "Hide submenu"
					type: checkbox
					defaultValue: false
					order: 1
				extraPage:
					extends: @cf.definitions.page
					label: "Extra item in side of submenu"
					order: 2

		gtm:
			type: group
			label: "GTM"
			items:
				pageType:
					label: "Page type"
					type: text
					order: 1
				subpagesPageType:
					label: "Page type for sub-pages"
					type: text
					order: 2

		carousel:
			type: list
			label: "Carousel"
			items:
				img:
					extends: @cf.definitions.image
					order: 1
				video:
					label: "Youtube link"
					type: text
					order: 2
				title:
					label: "Caption"
					type: text
					order: 3
				annot:
					label: "Additional text"
					type: text
					order: 4
				src:
					extends: @cf.definitions.page
					label: "Link to Page"
					order: 5
				srcSeoFilter:
					extends: @cf.definitions.seoLink
					label: "or link to Seofilter"
					order: 6
				srcMyUrl:
					label: "or any other link"
					type: text
					order: 7
				btnText:
					label: "Button text"
					type: text
					order: 8
				align:
					label: "Text alignment"
					type: select
					defaultValue: left
					options: [
						{ label: "Left", value: "left" },
						{ label: "Right", value: "right" }
					]
					order: 9
				gtmName:
					label: "GTM name"
					type: text
					order: 10
				hide:
					label: "Nezobrazovat"
					type: checkbox
				publicFrom:
					label: "Datum publikace od"
					type: dateTime
				publicTo:
					label: "Datum publikace do"
					type: dateTime

		benefits:
			type: list
			label: Main benefits
			items:
				item:
					type: group
					items:
						text:
							type: text
							label: Text
							order: 1
						page:
							extends: @cf.definitions.page
							order: 2
						seolink:
							extends: @cf.definitions.seoLink
							order: 3
						otherLink:
							type: text
							label: "Other link"
							order: 3
						gtm:
							type: text
							label: GTM text
							order: 4

		mainMenu:
			type: list
			label: "Main menu"
			items:
				tree:
					extends: @cf.definitions.page
					order: 1

		footerMenu:
			type: list
			label: "Footer menu"
			items:
				tree:
				 	extends: @cf.definitions.page
				 	order: 1
				showOnlyOnEshop:
					label: "Only on eshop"
					type: checkbox
					defaultValue: true
					order: 2

		seoLinkDog:
			type: group
			label: "Product submenu - dog (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkCat:
			type: group
			label: "Product submenu - cat (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkRodent:
			type: group
			label: "Product submenu - rodent (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkDiet:
			type: group
			label: "Product submenu - diet (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkPromo:
			type: group
			label: "Product submenu - promo (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkDogs:
			type: list
			label: "Product submenu - dogs items (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkCats:
			type: list
			label: "Product submenu - cats items (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkRodents:
			type: list
			label: "Product submenu - rodents items (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		seoLinkDiets:
			type: list
			label: "Product submenu - diets items (links to SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					order: 1
				name:
					label: Link name
					type: text
					defaultValue: ""
					order: 2

		contact:
			type: group
			label: "Contact data"
			items:
				company:
					label: "Company"
					type: tinymce
					order: 1
				address:
					label: "Address"
					type: tinymce
					order: 2
				phone:
					label: "Phone (without code)"
					type: text
					placeholder: '+*********** 333'
					order: 3
				openingHours:
					label: "Otevírací doba"
					type: text
					defaultValue: ''
					order: 4
				email:
					label: "E-mail"
					type: text
					defaultValue: ''
					order: 5
				productManager:
					label: "Product manager"
					type: text
					defaultValue: ''
					order: 6
				productManagerEmail:
					label: "E-mail (product manager)"
					type: text
					defaultValue: ''
					order: 7
				facebook:
					label: "Facebook"
					type: text
					defaultValue: ''
					order: 8
				instagram:
					label: "Instagram"
					type: text
					defaultValue: ''
					order: 9
				youtube:
					label: "Youtube"
					type: text
					defaultValue: ''
					order: 10

		coordinates:
			type: group
			label: "Souřadnice prodejny"
			items:
				lat:
					label: "Zeměpisná šířka"
					type: text
					defaultValue: ''
					order: 1
				lng:
					label: "Zeměpisná délka"
					type: text
					defaultValue: ''
					order: 2

		history:
			type: list
			label: "History"
			items:
				img:
					extends: @cf.definitions.image
					order: 1
				year:
					label: "Year"
					type: text
					order: 2
				text:
					label: "Description"
					type: text
					order: 3

		foodSelected:
			type: list
			label: "Vybrané suroviny"
			items:
				img:
					extends: @cf.definitions.image
					order: 1
				name:
				 	extends: @cf.definitions.caption
				 	order: 2
				text:
					extends: @cf.definitions.description
					order: 3

		foodAdditives:
			type: list
			label: "Vybraná aditiva"
			items:
				img:
					extends: @cf.definitions.image
					order: 1
				name:
				 	extends: @cf.definitions.caption
				 	order: 2
				text:
					extends: @cf.definitions.description
					order: 3

		foodAdditivesTextTitle:
			type: group
			label: "Popis vybraných aditiv - nadpis sekce"
			items:
				title:
					label: "Headline"
					type: text
					order: 1

		foodAdditivesText:
			type: list
			label: "Popis vybraných aditiv"
			items:
				name:
					extends: @cf.definitions.caption
					order: 1
				text:
				 	extends: @cf.definitions.description
				 	order: 2

		contentLeft:
			type: group
			label: "Left content"
			items:
				content:
					extends: @cf.definitions.content
					order: 1

		contentRight:
			type: group
			label: "Right content"
			items:
				content:
					extends: @cf.definitions.content
					order: 1

		feeds:
			type: group
			label: "Feed category settings"
			items:
				zbozi:
					label: "Zboží.cz"
					type: text
					defaultValue: ''
					order: 1
				heureka:
					label: "Heureka"
					type: text
					defaultValue: ''
					order: 2
				google:
					label: "Google"
					type: text
					defaultValue: ''
					order: 3

		cookies:
			type: group
			label: "Cookies"
			items:
				hide:
					label: "Skrýt cookie popup"
					type: checkbox
					defaultValue: false
					order: 1
				title:
					label: "Nadpis"
					type: text
					defaultValue: ""
					order: 2
				step1:
					label: "Text krok 1"
					type: tinymce
					defaultValue: ""
					order: 3
				step2:
					label: "Text krok 2"
					type: tinymce
					defaultValue: ""
					order: 4
				btnSetPreferences:
					label: "Tlačítko - nastavit preference"
					type: text
					defaultValue: ""
					order: 5
				btnConsentAndContinuation:
					label: "Tlačítko - souhlas a pokračování"
					type: text
					defaultValue: ""
					order: 6
				subTitle:
					label: "Nadpis"
					type: text
					defaultValue: ""
					order: 7
				technicalLink:
					label: "Technické - link"
					type: text
					defaultValue: ""
					order: 8
				technicalText:
					label: "Technické - text"
					type: tinymce
					defaultValue: ""
					order: 9
				statisticalLink:
					label: "Statistické - link"
					type: text
					defaultValue: ""
					order: 10
				statisticalText:
					label: "Statistické - text"
					type: tinymce
					defaultValue: ""
					order: 11
				preferentialLink:
					label: "Preferenční - link"
					type: text
					defaultValue: ""
					order: 12
				preferentialText:
					label: "Preferenční - text"
					type: tinymce
					defaultValue: ""
					order: 13
				marketingLink:
					label: "Marketingové - link"
					type: text
					defaultValue: ""
					order: 14
				marketingText:
					label: "Marketingové - text"
					type: tinymce
					defaultValue: ""
					order: 15
				btnConfirmSelected:
					label: "Tlačítko - potvrdit vybrané"
					type: text
					defaultValue: ""
					order: 15
				btnAcceptEverything:
					label: "Tlačítko - přijmout vše"
					type: text
					defaultValue: ""
					order: 16

		lines:
			type: group
			label: "Special text"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					label: Seo filter link
					order: 1
				tree:
					extends: @cf.definitions.page
					label: "Page link"
					order: 2

		articleBtns:
			type: group
			label: "Navigation above the article"
			items:
				btn1_text:
					label: "Left button title"
					type: text
					value: ""
					order: 1
				btn1:
					label: "Left button link"
					type: text
					value: ""
					order: 2
				btn2_text:
					label: "Right button title"
					type: text
					value: ""
					order: 3
				btn2:
					label: "Right button link"
					type: text
					value: ""
					order: 4

		seoArticles:
			type: list
			label: "Articles"
			items:
				tree:
					extends: @cf.definitions.page
					label: Title
					order: 1

		seoImage:
			type: group
			label: "Image"
			items:
				img:
					label: ""
					type: image
					order: 1

		seoLinks:
			type: list
			label: "Subcategories (other SEO filters)"
			items:
				seolink:
					extends: @cf.definitions.seoLink
					label: Title
					order: 1

		tabs:
			type: list
			label: "Your tabs"
			items: # in case of key renaming, the csv import/export must be adjusted accordingly!
				name:
					label: "Name"
					type: text
					order: 1
				content:
					label: "Content"
					type: tinymce
					order: 2
		vendorLinks:
			type: list
			label: "Vendor links"
			items: # in case of key renaming, the csv import/export must be adjusted accordingly!
				name:
					label: "Button name"
					type: text
					order: 1
				link:
					label: "Link"
					type: text
					order: 2
				img:
					extends: @cf.definitions.image
					order: 3

		giftImage:
			type: group
			label: 'Gift Image'
			items:
				image:
					label: 'Transparent png image'
					type: image
					order: 1

		prePreBasketButtons:
			type: group
			label: Buttons
			items:
				storno:
					label: 'Storno button'
					type: text
					order: 1
				continue:
					label: 'Continue button'
					type: text
					order: 2


		parameterForFilter:
			type: group
			label: "Nastavení filtru"
			items:
				visibleParameters:
					type: list
					label: Parametry
					items:
						indexable:
							type: checkbox
							label: "Indexovatelné"
						visibleCount:
							type: text
							label: "Počet viditelných hodnot"
						parameter:
							type: suggest
							subType: parameter
							placeholder: Jméno parametru
							url: @cf.suggestUrls.searchParameterForFilter


						numberAsRange:
							type: checkbox
							label: "Rozsah pro číselné hodnoty"

################### suggestUrls ################################################
	suggestUrls:
		searchPage:
			searchParameterName: search
			link: "/superadmin/search/cf-page"
			params: []

		searchSeoLink:
			searchParameterName: search
			link: "/superadmin/search/cf-seolink/"
			params: []

		searchVariant:
			searchParameterName: search
			link: "/superadmin/search/cf-variant/"
			params: []

		searchParameterForFilter:
			searchParameterName: search
			link: "/superadmin/search/parameter"
			params: [ 'types': ['select', 'multiselect', 'number'], 'onlyForFilter': 1]


################### templates ################################################
	templates:
		# examples - todo to WIKI:
		# WIKI - https://www.notion.so/superkoders/Vlastn-pole-Custom-Fields-verze-1-0-2c3322c358224c769c0bdb1a9593b6d2
		#1) Page:default: [customfields] # CF pro stránku s šablonou Page:default
		#2) Product:detail: [customfields] # CF pro produkt
		#3) product-AAA: [customfields] # CF pro produkt jehož nejvyšším rodičem (hlavní katgorie) je UID produktové kategorie AAA
		#4) uid-XXX: [customfields] # CF pro stránku s uid = XXX
		#5) parameter-YYY: [customfields] #CF pro parametr s uid = YYY
		#6) parameters: [customfields] #CF pro všechny parametry obecně, parameter-YYY přebíjí
		#7) banner-ZZZ: [customfields] #CF pro banner s pozici = ZZZ
		#8) Page:* CF pro všechny stránky ve stromě
		#9) user-ROLE || users

		# class specified
		productVariant: [@cf.vendorLinks] #used for vendor links

		Page:contact: [@cf.contact, @cf.coordinates]
		# Page:landing: [@cf.banner, @cf.footerCTA]
		Page:history: [@cf.history]
		Page:food: [@cf.foodSelected, @cf.foodAdditives, @cf.foodAdditivesTextTitle, @cf.foodAdditivesText]
		Page:breedingProgramme: [@cf.contentLeft, @cf.contentRight]
		Page:lines: [@cf.lines]

		uid-eshop: [@cf.seoLinkDog, @cf.seoLinkDogs, @cf.seoLinkCat, @cf.seoLinkCats, @cf.seoLinkRodent, @cf.seoLinkRodents, @cf.seoLinkDiet, @cf.seoLinkDiets, @cf.parameterForFilter]
		uid-personalData: [@cf.cookies]
		uid-title: [@cf.carousel, @cf.mainMenu, @cf.footerMenu, @cf.seoLinkDog, @cf.seoLinkCat, @cf.seoLinkRodent, @cf.seoLinkDiet, @cf.seoLinkPromo, @cf.benefits]
		uid-preprebasket: [@cf.prePreBasketButtons]

		Article:detail: [@cf.articleBtns]

		seolink: [@cf.seoImage, @cf.seoArticles, @cf.seoLinks]
		Product:detail: [@cf.tabs, @cf.giftImage, @cf.feeds]

		#for all pages
		Page:*: [@cf.menuSub, @cf.gtm]
		#Page:*:hide: [Product:detail, "uid-title"]
