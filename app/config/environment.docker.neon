includes:
	- dev.neon

parameters:
	stageName: local

	admin:
		allowedIpRanges:
			- "**********/16" # default docker bridge range
			- "***********/16" # default orbstack range

	config:
		domainUrl: https://calibra.superkoders.test/
		lang:
			cs:
				domain: calibra.superkoders.test
			en:
				domain: calibra-en.superkoders.test

		elasticSearch:
			enabled: true
			synonymFile: null

	database:
		host: calibra_db
		database: calibra
		user: calibra
		password: calibra

	redis:
		host: calibra_redis

messenger:
    transport:
        elasticFront:
        	dsn: "sync://"
        clonerFront:
        	dsn: "sync://"
        elasticPriorityFront:
        	dsn: "sync://"
        failure:
        	dsn: "sync://"

elastica:
	config:
		host: calibra_es
		port: 9200

http:
	proxy:
		- *********/8

mail:
	host: calibra_mailcatcher
	port: 1025
