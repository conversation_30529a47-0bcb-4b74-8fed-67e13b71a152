parameters:
	config:
		translate:
			en:
				test: "test en"

				edited: "Edited"
				created: "created"

				all: "All"

				lang_cs: "Czech"
				lang_en: "English"
				lang_sk: "Slovenština"
				lang_ru: "<PERSON><PERSON><PERSON><PERSON>"

				role_Registrovaný: "Signed"
				role_Admin: "Admin"
				role_Developer: "Developer"
				role_Accountant: "Accountant"

				role_accountant: "Accountant"
				role_developer: "Developer"
				role_admin: "Admin"
				role_partner: "Partner"
				role_user: "User"

				lost_password_link: "Lost password"
				lost_password_login_link: "Continue to log-in"
				lost_password_button: "Send me new password"

				# menu
				Modules: "Modules"
				Settings: "Settings"
				Library: "Library"
				Pages: "Pages"
				Products: "Products"
				Orders: "Orders"
				Users: "Users"
				Parameters: "Parameters"
				Vouchers: "Discount vouchers"
				Seolink: "SEO filters"
				E-mails: "E-mails"
				E-mails template: "E-mail templates"
				System strings: "Texts, translates"
				Redirects: "Redirects"
				Help: "Help"
				About: "About"
				Mutation: "Languages"

				# tabs
				tab_content: "Content"
				tab_images: "Images"
				new_redirect: "New redirect"
				tab_files: "Files"
				tab_help: "Help"
				tab_params: "Parameters"
				tab_links: "Links"
				tab_videos: "Videos"
				tab_seo: "SEO"
				tab_settings: "Other settings" #MoreDon't· settings
				tab_sorting: "Řazení produktů v kategorii"
				tab_product: "Product"
				tab_comments: "Comments"
				tab_sets: "Sets"
				tab_pages: "Pages"
				tab_products: "Products"
				tab_products_review: "Product reviews"
				tab_product_better: "Similar (sold out)"
				tab_product_similar: "Alternatives"
				tab_acc: "Recommended" #Příslušenství
				tab_price: "Variants"
				tab_reviews: "Rating"
				tab_faqs: "FAQ"
				tab_clientReviews: "User's review"
				tab_contents: "Content"
				tab_customfields: "Custom Fields"
				tab_modular: "Custom Content"
				tab_article: "Help"

				# forms
				keyName: "Key"
				keyIsEshop: "For eshop"
				keyValue: "Text"
				password: "Password"
				password_verify: "Password again"
				password2: "Password again"
				role: "Role"
				user_name: "Name"
				user_firstname: "Firstname"
				user_lastname: "Lastname"
				user_is_active: Active
				name: "Name"
				nameShort: "Short name"
				email: "E-mail"
				zip: "Zip"
				street: "Street"
				city: "City"
				vat_number: "VAT ID"
				company_id: "Comapny ID"
				company: "Company"
				phone: "Phone"
				info: "Info"
				file: "File"
				remember: "Keep me signed in" #Keep me signed in
				username: "Username"
				alias: "Alias"
				public: "Public"
				nonPublic: "Non public"
				nonActive: "Non active"
				anchor_text: "Text in link"
				title: "Title"
				description: "Description"
				keywords: "Keywords"
				aliases_history: "Alias history"
				annotation: "Annotation"
				annotation_detail: "Annotation for product detail"
				content: "Content"
				public_from: "Valid from"
				public_to: "Valid to (end day included)"
				message_public_from: "Valid from"
				message_public_to: "Valid to"
				template: "Template"
				profession: "Profession"
				position: "Position"
				infotext: "About author"
				mutation: "Language"

				strings_key: "Key"
				strings_isEshop: "Eshop"

				image_name: "Image name"
				hide_crossroad: "Hide crossroad"
				hide_first_image: "Hide first image in detail page" #Hide first image in detail page
				hide_big_image: "Hide big image in the detail"

				product_code: "Product code"
				product_id: "ID"
				product_public: "Public"
				product_set: "Set"
				product_name: "Poduct name"
				product_mutation: "Language"
				product_isSale: "Purchasable"
				product_isSaleShort: "Purchasable"
				product_not_isSale: "Not purchasable"
				product_not_isSaleShort: "Not purchasable"
				price: "Price"
				total_price: "Total price"
				price_vat: "Price with VAT"
				total_price_vat: "Total price with VAT"
				vat: "VAT"
				vat_rate: "VAT rate"
				code: "Code"
				is_old: "Is Old"
				is_in_prepare  : "In prepare"
				is_new: "News"
				is_set: "Allow set"
				is_isset: "Sets"
				is_action: "Action"
				is_bargain: "Sale"
				is_soldOut: "Sold Out"
				is_bestseller: "Bestsellers"
				show_on_homepage: "Show on homepage"
				top_product: "Top product"
				availability: "Availability"
				product_soldout: "Sold Out"
				product_old: "Sales ended"
				product_year: "Year"
				product_row: "Line"
				product_rank: "Rank"

				On stock: "On Stock"
				Sold Out: "Sold Out"
				One week: "Do týdne"
				Contact us: "On Ask"

				onStock: "On Stock"
				soldOut: "Sold Out"

				from_name: "From name"
				categories: "Category"

				filter_from: "From"
				filter_to: "To"

				filter_price_from: "Price From"
				filter_price_to: "Price To"

				product_hasDiscount: "Discount"
				product_hasDiscountDefault: "Discount - default"
				product_hasDiscountRegistered: "Discount - registered"
				product_hasDiscountRegisteredBreeding: "Discount - registered breeders"

				product_hasGift: "Gift"
				product_hasGiftDefault: "Gift - default"
				product_hasGiftRegistered: "Gift - registered"
				product_hasGiftRegisteredBreeding: "Gift - registered breeders"

				show_only: "Order status"

				Undefined: "Undefined"
				Yes: "yes"
				No: "no"

				type: "Type"
				Number: "Number"
				Text: "Text"
				Bool: "True/false"
				One option: "Select"
				More options: "Multiselect"
				Wysiwyg: "Wysiwyg"

				Unit: "Unit"
				use_param_search: "Use in parametric search" #
				use_param_price: "Použít k tvorbě variant"
				use_extra: "Parametr do nadpisu u filtrování"
				param_is_textarea: "Textarea"
				value: "Value"
				from: from

				params_translations: "Translations"


				help: "Help"
				preview_link: "Preview"
				attach_images: "Attach image"
				attached_pages: "Attached pages"
				attach_pages: "Attach page"
				attach_products: "Attach product"
				attached_products: "Attached products"
				attach_acticle: "Attach article"

				link_url: "Link address"
				link_text: "Link text"
				link_new_window: "New window"
				video_url: "Video URL"
				msg_video: "Copy the video url from the address bar"
				picture: "image"
				pictures: "images"
				pictures_more: "images"
				connected: "Attched:"
				Create new item: "Create New Product"
				Create new user: "Add new user"
				Create new voucher: "Add voucher"
				Create new newsletter: "Add newsletter"
				Create new email template: "Create new email template"
				New user: "New user"
				New voucher: "New voucher"
				New newsletter: "New newsletter"
				More settings: "Other settings"
				SEO: "SEO"
				exportUsers: "Export all users"

				downloadAllImages: "Download all images from this directory"

				#Firewall
				enableBlacklistItem: Block
				disableBlacklistItem: Unblock
				StatusOfBlocking: "Status of blocking"
				add_blacklist_item_btn: "Blacklist"

				#produkty
				product_clone_mutation: "Create language"
				product_clone_mutation_select: '-- Choose language --'
				product_create_mutation_button: Create language
				product_created: Product created
				product_clone_failed: An error occurred during the operation
				product_clone_mutation_already_exists: The product already has this language
				product_clone_mutation_itself: It cannot be cloned into the same language
				product_compensation: Variant compensation
				product_compensation_delete: Remove compensation
				product_isnt_public: Product is not public

				#objednávky
				label_order_email: "Order e-mail"
				id: "ID"
				search: "Order number"
				search_user: "User e-mail or name"
				Order Id: "Order number"
				User Name: "Customer name"
				Order: Order
				no.: number
				Order date: "Order date"
				stateOrder : "Status"
				Back: "Back"
				Exports: "Exports"
				StatisticsByUser: "Order statistics"
				invoices: "Invoices"
				creditNotes: "Credit notes"
				change_order_status: "Change order status"
				change_status_button: "Change status"
				Billing address: "Billing information"
				Corporate information: "Company information"
				Delivery address: "Delivery address"
				Ordered goods: "Ordered products"
				address: "Address"
				Note: "Note"
				product: "Product"
				amount: "Amount"
				price_sum: "Total price"
				order_number: "Order Number"
				parcel_number: "Parcel number"
				emails_recap: "vloží blok s rekapitulací objednávky (zadané údaje, tabulka zboží)"
				emails_sum: "total order price"
				emails_bankInfo: "informace pro platbu na účet (zobrazí se jen poud je vybrána platba na účet)"
				emails_tracking: "přidá odkaz na sledování objednávky"
				emails_link_for_online_payment: "url to page where customer can pay online"
				emails_blockIfUnpaid: "pair tags - shows if order is online payment and not paid or cancelled"
				barCode: "Tracking code"
				allOrdersValue: "Total value orders"
				paymentType: "Payment type"
				orderPaid: "Paid"
				order_statistics_show: "Show statistics"
				order_statistics_hide: "Hide statistics"
				filter_by: "Filter by"
				filter_created: "Order created date"
				filter_invoice_date: "Invoice created date"
				filter_created_credit_note: "Credit note created date"
				filter_order_type: "Order type"

				order_noviko_section: Adam
				order_noviko_status: Status
				order_noviko_status_unknown: Unknown
				order_noviko_last_update: Last updated

				# order statuses
				order_status_new: "New"
				order_status_waiting: "Awaiting payment"
				order_status_returned: "Returned by carrier"
				order_status_progress: "In progress"
				order_status_shipped: "Shipped"
				order_status_done: "Done"
				order_status_cancel: "Cancel"

				order_status_partially: "Partially processed"
				order_status_ready: "Ready for collection"
				order_status_cancel_reason: "Reason for the cancelling"

				orde_msg_storno_ok: "Order has been canceled"

				payment_name_online: "Online - card"
				payment_name_onlineBankAccount: "Online - bank account"
				payment_name_onlineApplePay: "Online - Apple Pay"
				payment_name_onlineGooglePay: "Online - Google Pay"

				order_noviko_msg_storno_ok: "Adam: Order has been canceled"
				order_noviko_msg_storno_already: "Adam: Order has already been canceled"
				order_noviko_msg_storno_rejected: "Adam: order can no longer be canceled"
				order_noviko_msg_storno_rejected_request_warehouse: "Adam: order can no longer be canceled by the system (email request has been sent to the warehouse)"
				order_noviko_msg_storno_failed: "Adam: storno operation failed"
				order_noviko_msg_save_header_rejected: "Adam: order can no longer be changed"
				order_noviko_msg_unknown_order: "Unknown order"
				order_noviko_msg_update_status_changed: "Order status has been changed"
				order_noviko_msg_update_status_not_changed: "Order status has not been changed"
				order_noviko_msg_update_status_no_changes: "No changes"

				order_done_csv_import: 'Import GEIS CSV'

				order_type_subscription: "Subscription"
				order_type_product: "Product"

				# dobropisy
				credit_note: 'Credit note'
				credit_note_amount: 'Credit note (amount)'
				credit_note_btn_create: 'Create credit note'
				credit_note_cancel_reason: 'Reason for the credit note'
				credit_note_select_amount_skip_item: '--'
				credit_note_msg_error_no_item: 'Blank credit note - please select at least one item'
				credit_note_msg_ok_created: 'Credit note has been created'
				credit_note_msg_ok_refunded: 'Online payment: the amount has been refunded'
				credit_note_msg_error_refunded: 'Online payment: the amount has not been refunded'

				# button
				save_button: "Save"
				delete_button: "Delete"
				login_button: "Log in"
				upload_images_button: "Upload images"
				upload_files_button: "Upload files"
				add_button: "Add"
				new_product_button: "New product"
				new_flag_button: "Labels"
				filter_button: "Filter"
				filter_cancel_button: "Cancel filter"
				add_value_button: "Add value"
				delete_images_button: "Delete all images in directory"
				product_back_button: "back to product list"

				product_flags: 'Labels'

				# message
				msg_info_firm: "Fill if you are company"
				msg_info_breeding: "Fill if you are breeder"
				msg_info_delivery: "Complete if different from personal data"
				msg_info_logout: "You have been logged out" #You have been signed out.

				msg_error_username: "Please enter your username" #Please enter your username.
				msg_error_password: "Please enter password"
				msg_error_valid_username: "The username is incorrect"	#The password is incorrect.
				msg_error_valid_password: "The password is incorrect"	#The username is incorrect.

				msg_product_deleted: "Product has been deleted"

				msg_signet_out_inactive: "User signed out, too long inactive" #User signed out, too long inactive
				msg_access_denied: "Access denied" #Access denied
				msg_module_access_denied: "Access to module denied" #Access denied
				msg_password_not_same: "Password not same" #Password not same
				error: "Error"

				msg_new_password_send: "New password has been sent to yout e-mail"


				msg_string_delete: "Text has been deleted"
				msg_string_duplicate: "This key is already used"

				# vouchers

				msg_error_voucher_count: "Enter how many codes you want to generate"
				msg_error_voucher_count_max: "The maximum number of codes to generate is 1000"
				msg_error_enter_voucher_code: "Enter valid custom code"
				msg_error_voucher_already_exist: "This custom code already exist"
				msg_voucher_generated: "Discount codes have been generated"

				label_publicTo: "Valid to"
				label_publicFrom: "Valid from"
				label_voucherType: "Discount type"
				label_voucherApplication: "Restrictions on the use of the voucher for"
				label_minPriceOrder: "Min. order value"
				label_maxPriceOrder: "Max. order value"
				label_discountAmount: "Discount value"
				label_discountPercent: "Discount in %"
				label_reuse: "I can use the code more than once"
				label_use_once_by_user: "Each registered user can use the code once"
				label_use_code_only_once: "The code can be used only once"
				label_voucher_count: "Enter the number of codes"
				label_custom_voucher: "Enter custom code"
				label_voucher_import: "Import codes or enter custom code"
				label_voucher_import_csv: "Import codes from CSV"
				label_gift_variant_id: "Product variant used as a gift"
				label_voucher_used: "Used"

				voucher_amount: "Value"
				voucher_percent: "Percent"
				generateCode: "Generate codes"
				generateCustomCode: "Generate custom code"
				exportVouchers: "Download active codes to csv"
				importVouchers: "Import codes from CSV (file or text)"
				vouchers_list_of_codes: "List of discount codes"

				voucher_app_product: "products"
				voucher_app_service: "transports"
				voucher_app_all: "all"

				label_voucherKind: "Type of Voucher"
				voucher_kind_normal: "ordinary"
				voucher_kind_product: "voucher as a product"
				voucher_kind_gift: "gift"

				onlyForDevelopers: "Only for developers"

				# newsletter

				label_emailContent: "E-mail content"
				label_internalName: "Internal name"
				label_language: "Language"
				label_products_count: "Products count"
				label_key: "Key"
				label_subject: "Subject"
				label_content: "Content"
				label_footer_text: "Footer text"
				label_name_newsletter: "Název/předmět"
				label_email_test: "Enter email (multiple emails split with comma)"
				preview: "preview"
				label_footerText: "Text in the footer"

				newsletter_butt_test_send: "Send test email"
				newsletter_butt_send: "Send newsletter"

				newsletter_concept: "Rozepsané"
				newsletter_sending: "Odesílané"
				newsletter_finished: "Odesláno"

				showEmailTemplate: "Show template"

				sent_recipients_count: "Počet odeslaných"
				all_recipients_count: "Počet všech příjemců"
				unsubscribed_count: "Počet odhlášených"

				msg_newsletter_sent: "Newsletter byl připraven k odeslání. Během pár minut se začne odesílat. "
				msg_test_newsletter_sent: "Testovací newsletter byl odeslán."

				Newsletter Emails: "E-mails"
				New email: "Add new e-mail"
				Create new email: "add e-mail"
				Edit email: "edit e-mail"
				exportEmails: "E-mails export"

				close_library_button: "Close library"

				date_created: "Created"
				date_edited: "Last edit"
				date_sending: "Start of sending"
				date_finished: "Finished"
				date_sent: "Sent"
				date_creatd: "Created"


				# mista

				Place: "Places"
				Places: "Places"
				place_name: "Name"
				place_city: "City"
				place_street: "Address"
				place_zip: "Zip"
				place_email: "E-mail"
				place_web: "Web"
				place_phone: "Phone"
				place_openTime: "Opening hours"
				place_type: "Type"
				place_gps: "Coordinates"
				place_lat: "Latitude"
				place_lon: "Longitude"
				place_code: "Seller code"
				place_mutation: "Language"
				new_place_button: "Add new place"
				Create new place: "Create new place"
				place_gps_missing: "GPS missing"
				place_get_gps_coordinates: "Get GPS coordinates"
				place_show_on_map: "Show on map"


				paging: "Pagination"
				pagingPrev: "previous"
				pagingNext: "next"
				paging_prev: "previous"
				paging_next: "next"

				image: "Image"
				use_image: "Allow images upload to parametrs values"
				butt_attach_image: "Image"

				firstname: "Firstname"
				lastname: "Lastname"


				discount: "Special price"
				discountType: Discount type
				price_buy: "Purchase price without VAT (internal)"
				price_buy_vat: "Purchase price with VAT"
				with_vat: "with VAT"
				without_vat: "without VAT"

				comment_no: "number"
				comment_name: "author"
				comment_date: "date"
				comment_product: "product"
				comment_product_tree: "product/page"
				comment_stars: "stars"
				comment_text: "text"

				state: "State"


				nameEn: "Name EN"
				titleEn: "Title EN"
				anchor_textEn: "Text in link EN"
				annotationEn: "Annotation EN"
				contentEn: "Content EN"
				saveProductFirst: "You must save the product to add files"
				Banners: "Banners"
				Config: "Config"
				New banner: "New banner"
				banner_position: "Nanner position"
				newWindow: "Open in new window"
				link: "Link"
				not_sold_separately: "Separately unsalable"
				tab_present: "Gifts"

				emails_order_products: "adds a list of order products"
				product_search_fulltext: Search
				label_bannerType: Banner position

				voucher_code: "Generate discount vouchers"
				enabledInChilds: "Enable on sitelinks"

				configItemList: Values
				configNewItem: new record
				config_name: Parameter name
				config_uid: Klíč parametru
				config_subUid: Sekundární klíč parametru
				config_section: Section
				config_value: Value
				config_forDeveloper: Pouze pro vývojáře

				mail_exist: "This e-mail is already registered"
				email_required: "Please fill in the e-mail"
				set_password: "Please fill in your password"

				form_send_reset_password: "Information has been sent to your e-mail"
				form_reset_password: "New password was set"
				isPublic: "Public"
				questions: "Otázky"
				question_text: "Znění"
				question_type: "Typ"
				Survey: "Dotazník"
				Surveys: "Dotazníky"
				add_question: Přidat otázku
				add_answer: Přidat odpověď
				required_question: Povinná otázka
				export: Export
				New survey: Nový dotazník
				remove_question: Odebrat otázku
				"Create new Survey": Vytvořit nový dotazník
				"param_category_tree": Pages
				"param_category_product": Products
				"search_libs": Search
				"choose_position": choose position
				"choose_voucher": Coupon pair
				"priceDPH": Price with VAT
				"ean": EAN
				"choose_parameter": choose parameter
				"active": Active
				"activeVariant": Active variant
				"priceDPHDiscount": Sale price with VAT
				"percentDiscount": Percent discount
				"activeDiscount": Active discount
				"fromDiscount": Discount valid from
				"toDiscount": "Discount valid to (end day included)"
				"place": Place
				"capacity": Capacity
				"Date": Date
				"mail_info": "Information to mail"
				"choose": Choose
				"fromDateTime": Start
				"toDateTime": End
				"to": End
				"searchText": Find
				"Rent": Rent
				"tooltip": "Filter tooltip"

				"filterLink": Link to article for filter parameter
				"filterLinkName": Link text
				"openBox": "Unpacked piece"

				parameterValues: "Parameter possible values"
				forcedOpen: Open in filter
				forcedOpenCount: The number of displayed values ​​in the filter
				add_product_button: Add product
				new_product_group: New group
				"Order payment": Payment method
				"Order payment status": Payment status
				"Zasilkovna id": "ID of Zásilkovna outpost"
				"Transport info text": "Transport description"
				"card_CREATED": Payment based
				"card_PAYMENT_METHOD_CHOSEN": The payment method you choose
				"card_AUTHORIZED": Payment pre-authorized
				"card_CANCELED": Payment canceled
				"card_TIMEOUTED": Expired payment
				"card_REFUNDED": Refunded
				"card_PARTIALLY_REFUNDED": Refunded
				"card_PAID_NEXT_ATTEMPT": Refunded
				"card_error": Error

				subscriptionOrderItemType: "Order Item Type"
				subscription_deactivate: Deactivate subscription
				subscription_skip_next_order: Skip next order

				is_public: Public
				is_not_public: Not public

				created_time_order: "Time created"

				seo_title_filter: 'Headline pro filtraci'
				seo_annotation_filter: 'Annotation for filtering'
				seo_description_filter: 'Metadescription for filtering'
				seo_fieldset_parameters: Filter settings
				seo_label_paramsVisibility: Filter visibility
				seo_label_isDefault: Default filter
				seo_label_isActive: Active
				seo_msg_validation_url: This nice URL address is already used
				seo_msg_validation_identical: This filter settings is identical with active filter ID
				seo_name_new_filter: New filter
				category: 'Category'
				hide_in_search: "Don't show in search results"
				priority_article: "Priority article (display first)"
				labe_h1: 'Headline H1'
				labe_name: 'Internal name'
				labe_url: 'Nice URL address'
				labe_noIndex: 'Disable indexing'
				labe_metaTitle: 'Meta - TITLE'
				labe_metaDescription: 'Meta - DESCRIPTION'
				labe_metaKey: 'Meta - KEYWORDS'
				"Seo links": Seo filters
				"newUrl": New address
				"oldUrl": Old address
				redirects_imports: "Import"
				hide_in_sitemap: "Don't show in sitemap"
				forceNoIndex: 'Enforcement "noindex,follow"'
				"Please enter a valid email address.": 'Enter your e-mail address'
				"This field is required.": 'Fill in all required fields'
				"form_password_not_same": 'The passwords are not identical'
				"rest_password_button": 'Change password'
				"reset_password_expired_link": 'link expired'
				"reset_password_ok": 'Password successfully changed'
				"reset_password_no_valid_link": 'Invalid link'
				form_enter_email: "Enter e-mail address"

				user_personal: "Personal data"
				user_company: "Company data"
				user_delivery: "Delivery data"
				user_breeding: "Breeding data"
				user_groups: "Groups"
				user_subcriptions: "Subscriptions"

				title_csv_import_text: CSV import of texts
				csv_import: CSV import
				csv_export: CSV export

				page_icon: Icon
				icon_globe: globe
				chat: chat
				icon_pets: pets
				icon_stethoscope: stethoscope
				icon_vet: vet
				icon_dog: dog
				icon_cat: cat
				icon_compare: compare
				icon_paw: paw
				icon_phone: phone
				icon_user: user
				icon_envelope: envelope
				icon_pet-small: small pet
				icon_search: search

				type_label: "Classic label"
				type_gift: "Is gift"
				type_transportFree: "Free transport"
				type_productLine: "Parameter: product line"

				mutation_back_button: "Back to languages"
				import_button: Import

				label_name: "Name"
				label_langCode: "Lang code"
				label_domain: "Domain"
				label_urlPrefix: "URL prefix"
				label_rootId: "Root ID"
				label_hidePageId: "Hide Page ID"
				label_orderEmail: "New order - e-mail receiver"
				label_contactEmail: "Contact form - e-mail receiver"
				label_breedingEmail: "Breding program registration - e-mail receiver"
				label_breedingEmailName: "Breding program registration - e-mail name"
				label_adminEmail: "Default admin e-mail address (for all other e-mail messages)"
				label_pointsSort: "Set points, to influence order"
				label_favouritePointsSort: "Favourite products"
				label_animalPointsSort: "Pets"
				label_actionPointsSort: "Action products"
				label_transportFreeFrom: "Free transport - minumim order value"
				label_countryDefault: "Default state - for delivery and billing address"
				label_countryList: "List of available states in delivery and billing address"

				label_isEshop: "E-shop"
				label_isFilterOff: "Hide filter on products list"
				label_subscriptionAllowed: "Allow subscription (only if eshop is enabled)"

				param_value_short: "Weight extra"
				deleted: "deleted"
				search_example: "Name, EAN, NovikoID"
				your_price: "your price"
				is_free_delivery: "Free delivery"
				product_variants_price_stock: "Variants, prices and store"
				product_price_stock: "Prices and store"
				product_variant_final_price: "Final price"
				product_variant_base_price: "Base price"
				product_variant_custom_fields: "Variant custom fields (vendor links, ...)"
				varints: "Variants"
				product_last_edit: "Last edit time"
				mutation_list: "Language list"
				annot_link: 'URL address format: "name":http://www.link.com'
				product_variant_isQuantityDiscount: "Quantity discount"
				product_variant_qdAmount: "Amount"
				product_variant_useQuantityUserGroupDiscount: Use user groups discounts

				string_param_names: "Parameter names"
				string_param_values: "Parameter values"
				string_others: "Translates"
				string_is_empty: "Only empty values"

				parameter_msg_error_delete_uid: "This parameter has been identified as important for site functionality. To delete it, please contact technical support."

				# synonyms
				title_synonyms: "Synonyms"
				msg_ok_synonyms: "Synonyms saved"
				msg_info_synonyms_duplicity: "Some synonyms were identical and were not used"

				# messages
				title_messages: "Messages"
				title_message: "Message"
				btn_new_message: "New message"

				form_message_error_edited_mismatch: "This item has been changed by the user %s (%s), the page needs to be refreshed."

				# settings
				title_settings: "Settings"

				contentSection: "Content section settings"
				homeNewProducts: "New products on homepage"
				homeArticles: "Articles on homepage"
				guideNewsletter: "Newsletter checkbox in guide compoment"
				footerNewsletter: "Newsletter in footer"
				renderContactForm: "Render contact form"

				systemEmail: "System emails settings"
				adminEmail: "Default admin e-mail address (for all other e-mail messages)"
				adminEmailName: "Default admin e-mail name"
				contactEmail: "Contact form - e-mail"
				contactEmailName: "Contact e-mail name"
				orderEmail: "New order - e-mail"
				orderEmailName: "Order e-mail name"
				breedingEmail: "Breding program registration - e-mail"
				breedingEmailName: "Breding program registration - e-mail name"
				warehouseEmail: "Warehouse e-mail"
				warehouseEmailName: "Warehouse e-mail name"
				newsletterEmail: "Newsletter e-mail"
				newsletterEmailName: "Newsletter e-mail name"
				undeliveredPackagesEmail: "Undelivered packages report e-mail"
				undeliveredPackagesEmailName: "Undelivered packages report e-mail name"


				saleNotificationSection: "Sale duration notification"
				durationDays: "Days of sale duration to notification"
				notificationEmail: "Email recipient of notification"


				order_detail_ready: "Order is ready"
				order_detail_sent: "Order is sent"
				order_detail_storno: "Order is canceled"
				order_detail_inprogress: "Order is in progress"

				order_detail_email_sent: "E-mail sent"
				resend_order_detail_email: Resend order detail email
				order_detail_email_not_sent_yet: Order detail email was not sent
				order_detail_send_email: "Send info e-mail to customer"
				order_detail_change_status: "and  change order status to  "
				order_detail_info_email_tooltip: 'Sends an informational e-mail that the order has been processed and is ready to be picked up / shipped. At the same time, it sets the status to "Ready to pick-up'
				order_detail_info_email_tooltip2: 'Sends an information e-mail that the order is being processed.'
				order_detail_info_email_tooltip3: 'Sends an informational e-mail that the order has been sent. Also sets the status to "Done"'
				order_detail_info_email_tooltip4: "don't forget to fill in the tracking code and save the order"
				order_detail_info_email_tooltip5: 'Sends an informational e-mail that the order has been canceled. Also sets the status to "Cancel"'

				order_email_sent_confirm: Confirmed
				order_email_sent_shipped: Shipped
				order_email_sent_storno: Canceled
				order_email_sent_storno_warehouse: Sent request to warehouse

				title_breeds: "Breeds"
				new_breed_button: "Add new breed"
				breed_type: "Breed type"
				breed_name: "Name"
				breed_name_alt: "Alternate names"

				# user breeding
				breedingName: Name
				breedingAmount: Number of animals
				breedingCompany: Company
				breedingNumber: Number
				user_is_breeding_authorized: Authorized

				user_partner_mutation_access: "Access to mutations (only for partner role)"
				user_partner_mutation_select : '-- Choose language --'
				user_form_message_error_role_partner: "Please specify at least one mutation for the partner"

				product_heurekaLink: "Link to Heureka"
				forceBestSellerOrder: "Default order (higher number = product is higher)"
				productSoldCount: "Number of products sold"
				forceHpBestSellerOrder: "Homepage Bestseller (higher number = product is higher)"
				basketTipsOrder: "Basket tips order (higher number = better position)"
				synonyms_info: "Made changes will take effect on the website within 5 minutes"

				userIsBreeding: Is breeder
				userIsVeterinary: Is veterinary
				animal_animalType: Animal type
				animal_dogSize: Dog size
				animal_ageDog: Dog age
				animal_ageCat: Cat age
				animalWeightFrom: Weight from
				animalWeightTo: Weight to
				userNumberOfAnimals: Number of animals
				orderAmountFrom: Order amount from
				orderAmountTo: Order amount to
				user_orderBoughtOrNot: Bought in the period
				user_filter_breed_msg_mutation: You can filter by breeds after selecting the language.
				user_filter_no_result: No result found
				breedDog: Dog breed
				breedCat: Cat breed

				userGroupAction: Action
				userGroupActionAdd: Add
				userGroupActionRemove: remove
				execute_button: Execute
				user_group_msg_mutation: "You can add or remove user groups after selecting the language."

				# user groups
				new_user_group_button: Add new user group
				ug_discount_type: Type
				ug_value: 'Discount value (%)'
				ug_discount_product_line: Product line
				ug_discount_combines_with_gifts: Applies to products with a gift
				msg_user_group_deleted: User group has been deleted
				ug_select_label : '-- Choose label --'

				# mutation agreement
				title_agreements: "Agreements"
				new_agreement_button: "Add new Agreement"
				agreement_type: "Type"
				agreement_place: "Place"
				agreement_required: "Required"

				# agreement types
				RegistrationForm: "Registration Form"
				OrderStep2Form: "Order Form"

				msg_operation_failed: "Operation failed"
				msg_template_key_exist: "Template key already exists"
				msg_redirect_invalid_char: "URL cannot contain the # character"

				discounts_heading: "Discounts and presents for"
				custom_fields_heading: "Custom fields for"
				discount_flag: "Label"
				discount_flag_round: "Round label"
				discount_flag_search: "Search label"
				discount_group: "User group"
				product_variant_set_discounts: "Set discounts and presents"
				product_variant_price_history: "Price history"
				selling_price: "Selling price"
				original_price: "Original price"
				product_price: "Product price"
				price_history_for: "Price history for"

				search_term: "Term"
				search_count: "Count"
				search_last_search: "Last search"
				search_last_search_result_count: "Last search (result count)"
				search_mutation: "Mutation"

				search_time: "Search time"
				search_suggest: "Is suggest"
				search_results_count: "Results count"
				search_results_products_count: "Prouduct results"
				search_results_page_count: "Pages results"
				search_results_seo_count: "Seolink results"
				library: "Image library"

				choose_template_for_page: 'Choose template for page'
				tree_insert_page: 'Insert'
				product_mentioned_in_article: "Products mentioned in the article"
				product_insert_to_article: "insertion into the article"

				#invoice export
				filtered_export: 'Export filtered'
				invoice_count: 'Invoice count'
				credit_note_count: 'Credit note count'

				invoice_export_button: 'Invoices in PDF'
				invoice_only_export_button: 'Invoices only in PDF'
				credit_note_export_button: 'Credit notes in PDF'
				invoice_export_xml_button: 'Invoices in XML'
				credit_note_export_xml_button: 'Credit notes in XML'
				invoice_export_link: 'The last invoice export'
				invoice_export_not_found: 'The requested invoice export (ID %id) does not exist.'
				invoice_export_processing: 'The invoice export is being generated...'
				invoice_export_error: 'An error occurred during the invoice export.'
				invoice_export_requested: 'The request for the invoice export was created and will be processed as soon as possible.'
				invoice_only_export_requested: 'The request for the invoice export (invoices only) was created and will be processed as soon as possible.'
				credit_note_export_requested: 'The request for the credit note export was created and will be processed as soon as possible.'
				invoice_export_limit_exceeded: 'Filtered orders count is  %filteredCount. Only first %maxCount orders is going to be exported.'

				title_template_cache: Template Cache
				msg_ok_cache_deleted: Cache has beed deleted

				export_csv_table: 'Export in CSV'
				export_all: "Export all"
				export_filtered: "Export filtered"

				#campaigns
				New newsletter campaign: "New newsletter campaign"
				newsletterCampaign: "Newsletter campaign"
				label_newsletter_campaign_name: "Campaign name"
				label_mutation_targeting: "Campaing target language"
				label_voucher: "Connected voucher"
				voucherCodePlaceholder: "* Here will be voucher code *"
				Edidation: Edit
				Preview: Preview
				newsletter: Newsletter
				Campaign reach out: "Campaign reach out (unique email count)"
				date_last_edited: "Last edited"
				date_last_edited_by: "Last edited by"
				checkbox_newsletter_campaign_filter_from: "Use filtering from"
				checkbox_newsletter_campaign_filter_to: "Use filtering to"
				label_newsletter_campaign_filter_did_last_order: "Users who made the last order in the filtered interval"
				label_newsletter_campaign_filter_has_active_subscription: "Users who have active subscription"
				label_newsletter_campaign_filter_had_subscription: "Users who had subscription in the past"
				label_newsletter_campaign_filter_productType: "Product type"
				label_newsletter_campaign_filter_productLine: "Product line"
				label_newsletter_campaign_filter_animalType: "Animal type"
				label_newsletter_campaign_filter_dogSize: "Dog size"
				label_newsletter_campaign_filter_ageDog: "Dog age"
				label_newsletter_campaign_filter_name: "Filter name"
				label_newsletter_campaign_filter_from: "From"
				label_newsletter_campaign_filter_to: "To"

				newsletter_campaign_status_new: "Concept (editable and ready to start)"
				newsletter_campaign_status_start: "Started (waiting in queue to be send)"
				newsletter_campaign_status_sending: "Sending"
				newsletter_campaign_status_finished: "Finished"

				newsletter_campaign_are_you_sure: "Are you sure you want to send newsletter? This action is permanent and cannot be stopped."

				newsletterCampaignFilterNotProductionWillBeUsed: "This is not production environment. You will see real data of recipients but only filtered emails will be used."
				newsletterCampaignFilterNotProductionUsed: "This is not production environment. Predefined system emails were used."
				campaignDoesNotExist: "Campaign does not exist."

				#subscription
				product_isSubscription: "Subscription product"
				is_in_subscription: "Is in subscription"
				is_oversize: "Is oversize"
				tab_subscription: "Subscription"
				tab_logs: "Logs"
				tab_next_order: "Next order"
				tab_orders: "Orders"
				subscription_id: "Subscription ID"
				subscription_name: "Subscription name"
				subscription_user: "User"
				subscription_interval: "Interval"
				subscription_order_sequence: "Order sequence"
				subscription_notification_at: "Notification at"
				subscription_order_creation_at: "Order creation at"
				subscription_order_id: "Subscription Order ID"
				subscription_order_expedition_at: "Order expedition at"
				subscription_is_cancelled: "Is cancelled"
				subscription_count_orders: "Count of orders"
				subscription_edit: "Edit subscription"
				emails_subscription_name: "Insert subscription name"
				emails_subscription_recap: "Insert subscription items recapitulation (only recurring items)"
				emails_subscription_id: "Insert subscription ID"
				emails_subscription_nextorderdate: "Insert date of creation next order (user can edit order before this date)"
				emails_subscription_nextordereditlink: "URL for open order where user can edit address or products"
				emails_subscription_orderrecap: "Insert table with subscription items recap"
				emails_subscription_user_edit_link: "URL for user to edit subscription"
				emails_subscription_admin_link: "URL for edit subscription to administation"
				subscription_order_create_immediately: "Create order immediately"
				confirm_subscription_create_immediately: "Are you sure that you want to create order immediately?"
				confirm_subscription_order_payment_now: "Are you sure that you want to process order payment immediately?"
				subscription_as_onetime: "One time (only next order)"
				subscription_as_subscription: "Subscription (each order)"
				subscription_next_order_date: "Subscription next order date (date for payment, cash on delivery)"
				discountAmount: "Discount amount (with VAT) - will be deducted from order total"
				discountText: "Discount text (for invoice, etc.)"

				msg_ok_saved: Saved
				msg_ok_deleted: Deleted

				#datagrid
				ublaboo_datagrid.per_page_submit: "Change"
				ublaboo_datagrid.action: ""
				ublaboo_datagrid.reset_filter: "Cancel filter"
				ublaboo_datagrid.no_item_found_reset: "No items found"
				ublaboo_datagrid.no_item_found: "No items found"
				ublaboo_datagrid.here: "Cancel filter?"
				ublaboo_datagrid.items: "Items"
				ublaboo_datagrid.from: "from"
				ublaboo_datagrid.previous: "Previous"
				ublaboo_datagrid.next: "Next"
				ublaboo_datagrid.hide_column: "Hide column"
				ublaboo_datagrid.show_all_columns: "Show all columns"
				ublaboo_datagrid.group_actions: "Group actions"
				ublaboo_datagrid.cancel: "Cancel"
				ublaboo_datagrid.save: "Save"
				ublaboo_datagrid.add: "Add"
				ublaboo_datagrid.all: "View all"
				ublaboo_datagrid.choose: "Choose"
				ublaboo_datagrid.execute: "Execute"
				ublaboo_datagrid.multiselect_choose: "Choose"
				filter_user_all: "All"
				ublaboo_isSet: "Is set"
				ublaboo_isInStock: "Is in stock"
				ublaboo_isNew: "Is new"
				ublaboo_manufacturer: "Manufacturer"
				ublaboo_publish: "Publish"
				ublaboo_isOld: "Stav Is old"
				ublaboo_isAction: "Is action"

				filter_noviko_id: "Adam ID"
				filter_noviko_status: "Adam status"
				subscription_mutation: "Subscription mutation"

				reason_cancel: "Reason for cancellation"
				deactive_reason_export: "Export deactive reasons"
				export_deactive_reason_error: "Export error deactive reason"
