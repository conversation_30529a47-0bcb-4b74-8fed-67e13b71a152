<?php

namespace SuperKoderi\Search;

/**
 * Parses search query into tokens
 *
 * TODO: operators +, -, NOT, AND, OR
 * TODO: work with Term[]
 */
class QueryParser
{
	protected $minLengthKeyword;


	public function __construct($minLengthKeyword)
	{
		$this->minLengthKeyword = $minLengthKeyword;
	}


	/**
	 * Get tokens with weights
	 * @param string $query
	 * @return array string => float
	 */
	public function parse($query)
	{
		$query = strip_tags(trim($query));
		if (mb_strlen($query) < $this->minLengthKeyword) return array();

		$tokens = array(); // token => weight
		$tokens[$query] = 3;

		// individual words
		if (preg_match_all('/[\w\-\/]{2,}/u', $query, $match))
		{
			foreach ($match[0] as $token)
				if (mb_strlen($token) >= $this->minLengthKeyword)
					$tokens[$token] = 1;
		}

		return $tokens;
	}

	/**
	 * Get search tokens
	 * @param string $query
	 * @return string[]
	 */
	public function getTokens($query)
	{
		return array_keys($this->parse($query));
	}

}
