{form form, novalidate => 'novalidate'}

	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>

{if isset($configs) && $configs}
	{*test*}
	{*{php $isDeveloper = 0}*}
	{var $prevSection = ""}
	{foreach $configs as $config}
		{if $prevSection != $config->section}
			<h2>{translate}config_{$config->section}{/translate}</h2>
			{php $prevSection = $config->section}
		{/if}
		<div class="grid-row">
			{if $config->forDeveloper && !$user->isInRole('developer')}
			{else}
				{formContainer $config->id}
					<div class="grid-1-3">
						{if $isDeveloper}
							{label name /}
							<span class="inp-fix">
							<input n:name="name" class="inp-text{if $formContainer['name']->hasErrors()} error{/if}">
						</span>
						{else}
							{$config->name}
						{/if}
					</div>
				{if $isDeveloper}
					<div class="grid-1-3">
						{label uid /}
						<span class="inp-fix">
							<input n:name="uid" class="inp-text{if $formContainer['uid']->hasErrors()} error{/if}">
						</span>
					</div>
					{*<div class="grid-1-3">*}
						{*{label subUid /}*}
						{*<span class="inp-fix">*}
							{*<input n:name="subUid" class="inp-text{if $formContainer['subUid']->hasErrors()} error{/if}">*}
						{*</span>*}
					{*</div>*}
					<div class="grid-1-3">
						<label n:name="forDeveloper">{_config_forDeveloper}</label>
						<span class="inp-fix">
							<input n:name="forDeveloper" class="inp-text{if $formContainer['forDeveloper']->hasErrors()} error{/if}">
						</span>
					</div>
					<div class="grid-1-3">
						{label section /}
						<span class="inp-fix">
							<input n:name="section" class="inp-text{if $formContainer['section']->hasErrors()} error{/if}">
						</span>
					</div>
				{/if}
					<div class="grid-1-3">
						{label value /}
						<span class="inp-fix">
						<input n:name="value" class="inp-text{if $formContainer['value']->hasErrors()} error{/if}">
					</span>
					</div>
				{/formContainer}
			{/if}
		</div>
	{sep}<hr>{/sep}
	{/foreach}
{/if}


{*{if $isDeveloper}*}
	{*<h2>{_configNewItem}</h2>*}
	{*<div class="grid-row">*}
		{*{formContainer new}*}
			{*<div class="grid-1-3">*}
				{*{label name /}*}

				{*<span class="inp-fix">*}
					{*<input n:name="name" class="inp-text{if $formContainer['name']->hasErrors()} error{/if}">*}
				{*</span>*}

			{*</div>*}
			{*<div class="grid-1-3">*}
				{*{label value /}*}
				{*<span class="inp-fix">*}
					{*<input n:name="value" class="inp-text{if $formContainer['value']->hasErrors()} error{/if}">*}
				{*</span>*}
			{*</div>*}
			{*<div class="grid-1-3">*}
				{*{label uid /}*}
				{*<span class="inp-fix">*}
					{*<input n:name="uid" class="inp-text{if $formContainer['uid']->hasErrors()} error{/if}">*}
				{*</span>*}
			{*</div>*}
			{*<div class="grid-1-3">*}
				{*{label subUid /}*}
				{*<span class="inp-fix">*}
					{*<input n:name="subUid" class="inp-text{if $formContainer['subUid']->hasErrors()} error{/if}">*}
				{*</span>*}
			{*</div>*}
			{*<div class="grid-1-3">*}
				{*{label forDeveloper /}*}
				{*<label n:name="forDeveloper">{_forDeveloper}</label>*}
				{*<span class="inp-fix">*}
						{*{input forDeveloper class => 'inp-text'}*}
					{*<input n:name="forDeveloper" class="inp-text{if $formContainer['forDeveloper']->hasErrors()} error{/if}">*}
				{*</span>*}

			{*</div>*}
			{*<div class="grid-1-3">*}
				{*{label section /}*}
				{*<span class="inp-fix">*}
					{*<input n:name="section" class="inp-text{if $formContainer['section']->hasErrors()} error{/if}">*}
				{*</span>*}
			{*</div>*}
		{*{/formContainer}*}
	{*</div>*}
{*{/if}*}



	<div class="fixed-bar">
		<button class="btn btn-green btn-icon-before">
			<span><span class="icon icon-checkmark"></span> {_save_button}</span>
		</button>
	</div>


{/form}
