<?php

namespace SuperKoderi\Components;

use App\Model\Flag;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\ParameterValueModel;
use App\Model\Services;
use App\Model\User;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\Template;
use Nette\Forms\Form;
use Nextras\Orm\Collection\EmptyCollection;
use SuperKoderi\Translator;
use SuperKoderi\ConfigService;
use SuperKoderi\hasErrorOnInputTrait;
use function json_decode;

/**
 * @property-read Template $template
 */
class FlagsForm extends UI\Control
{
	public function __construct(
		private readonly User $user,
		private readonly Mutation $mutation,
		private readonly Translator $translator,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
		private readonly ParameterValueModel $parameterValueModel,
	)
	{
	}

	public function render():  void
	{
		$this->template->setTranslator($this->translator);
//		$this->template->configs = $this->configService->fetchAll();
		$this->template->items = $this->orm->flag->findBy(['mutation' => $this->mutation])->orderBy('sort');

		$this->template->colors = $this->configService->getParam('flagColors');
		$this->template->types = ['label', 'transportFree', 'gift', 'productLine'];
		$this->template->virtualFlags = $this->orm->parameter->getBy(['uid' => 'virtualFlag'])?->options ?? new EmptyCollection();
		$this->template->productLines = $this->orm->parameter->getBy(['uid' => 'productLine'])?->options ?? new EmptyCollection();


		$this->template->isDeveloper = $this->presenter->getUser()->isInRole('developer');
		$this->template->render(__DIR__ . '/flagsForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addSubmit('send');
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formSucceeded(UI\Form $form, $values): void
	{
		bd("SAVE....");
		$data = $_POST;
//		if (!isset($data['contentId'])) {
//			$data['contentId'] = [];
//		}
//
		$items = $this->orm->flag->findBy(['mutation' => $this->mutation])->orderBy(['sort' => 'ASC']);

		foreach ($items as $item) {
			if (!isset($data['name'][$item->id])) {
				$this->orm->flag->remove($item);
			}
		}

		$maxSort = 0;
		$s = 0;
		foreach ($data['name'] as $key => $name) {

			if ($maxSort < $data['valueSort'][$key]) {
				$maxSort = $data['valueSort'][$key];
			}

			if (isset($data['itemIds']) && in_array($key, $data['itemIds'])) {
				// existuje
				$productContent = $this->orm->flag->getByIdChecked($key);
//				$productContent->sort = $data['valueSort'][$key];
				$productContent->sort = $s;

				if ($productContent->name != $data['name'][$key] ||
					$productContent->color != $data['color'][$key] ||
					$productContent->type != $data['type'][$key] ||
					$productContent->paramValue != $data['paramValue'][$key]
				) {

					$productContent->editedTime = new \DateTime();
					$productContent->edited = $this->user->id;
				}

			} else {
				$productContent = new Flag();
				$productContent->createdTime = new \DateTime();
				$productContent->created = $this->user->id;
				$productContent->mutation = $this->mutation;
				$maxSort++;
				$productContent->sort = $maxSort;
			}

			$productContent->name = $data['name'][$key];
			$productContent->type = $data['type'][$key];

			$productContent->virtualFlag = $data['virtualFlag'][$key] > 0 ? $data['virtualFlag'][$key] : null;

			if ($data['type'][$key] == 'productLine') {
				$productContent->paramValue = $data['paramValue'][$key] > 0 ? $data['paramValue'][$key] : NULL;
			} else {
				$productContent->paramValue = NULL;
			}

			$productContent->colorCode = $data['color'][$key];
			$productContent->color = $this->configService->getParam('flagColors')[$data['color'][$key]];

			$this->orm->persistAndFlush($productContent);
			$s++;
		}

		$this->updateProductsWithVirtualFlags($this->mutation);

		$this->presenter->redirect('this');
	}

	private function updateProductsWithVirtualFlags(Mutation $mutation): void
	{
		$virtualFlagParameter = $this->orm->parameter->getBy(['uid' => 'virtualFlag']);
		if (!isset($virtualFlagParameter)) {
			$this->flashMessage('Parameter with uid = \'virtualFlag\' not found. Add it first and re-save.');

			return;
		}

		$limit = 100;
		$lastId = 0;
		do {
			$relatedFlags = $this->orm->flag->findBy([
				'mutation' => $mutation,
			])->fetchPairs('id', 'virtualFlag');

			$products = $this->orm->product->findBy([
				'id>' => $lastId,
				'mutation' => $mutation,
			])->limitBy($limit);

			foreach ($products as $product) {
				foreach ($product->activeVariants as $variant) {
					$flagIds = json_decode($variant->flagsJson ?? '[]');

					if ($flagIds === []) {
						continue;
					}

					$parameterValuesConnectedToFlags = [];
					foreach ($flagIds as $flagId) {
						$parameterValuesConnectedToFlags[] = $relatedFlags[$flagId];
					}

					// Unique virtualFlags parameter value
					if ($variant->isFreeTransport == 1) {
						$transportFreeFlagId = $this->orm->flag->getBy([
							'type' => Flag::TYPE_TRANSPORT_FREE,
							'mutation' => $product->mutation,
						])?->id;

						if (isset($transportFreeFlagId)) {
							$parameterValuesConnectedToFlags[] = $relatedFlags[$transportFreeFlagId];
						}
					}

					$this->parameterValueModel->handleParameterValuesMultiSelectAttachment($product, $virtualFlagParameter, $parameterValuesConnectedToFlags);
				}

				$lastId = $product->id;
			}

			$continue = $this->orm->product->findBy([
				'id>' => $lastId,
				'mutation' => $mutation,
			])->fetch() !== null;
		} while ($continue);
	}

}


interface IFlagsFormFactory
{

	function create(User $user, Mutation $mutation): FlagsForm;

}
