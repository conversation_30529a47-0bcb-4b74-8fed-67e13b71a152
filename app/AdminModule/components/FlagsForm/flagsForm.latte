{form form, novalidate => 'novalidate'}

{*{if isset($configs) && $configs}*}
	{*{foreach $configs as $config}*}
		{*<div class="grid-row">*}
			{*{formContainer $config->id}*}

			{*{/formContainer}*}
		{*</div>*}
	{*{/foreach}*}
{*{/if}*}


	<div class="box-param-type" data-type='["select", "multiselect"]'>
		{*<h2>Přehled št<PERSON>tků</h2>*}
		<div class="crossroad-attached">
			{* vyjimka: razeni hodnot oceneni podle abecedy *}
			<div class="holder">
				<div class="hd">
					<div class="grid-row">
						<p class="grid-1-6">
							<strong>{_Label}</strong>
						</p>
						<p class="grid-1-6">
							<strong>{_VirtualParameter}</strong>
						</p>
						<p class="grid-1-6">
							<strong>{_Color}</strong>
						</p>
						<p class="grid-1-6">
							<strong>{_Color}</strong>
						</p>
						<p class="grid-1-6">
							<strong>{_Type}</strong>
						</p>
						<p class="grid-1-6">
						</p>
					</div>
				</div>
				<div class="bd">
					<ul class="sortable reset" data-copy="values" data-pattern='{include "part/form/values.latte"}'>
						{if $items}
							{foreach $items as $k=>$item}
								{include 'part/form/values.latte', 'k' => $item->id, 'data' => $item, 'sort' => $item->sort}
							{/foreach}
						{/if}
					</ul>
				</div>
			</div>
			<div class="ft">
				<p>
					<a href="#" class="btn btn-icon-before" data-copy="values">
						<span><span class="icon icon-plus"></span> {_add_value_button}</span>
					</a>
				</p>
			</div>
		</div>
	</div>


	<div class="fixed-bar">
		<button class="btn btn-green btn-icon-before">
			<span><span class="icon icon-checkmark"></span> {_save_button}</span>
		</button>
	</div>


{/form}
