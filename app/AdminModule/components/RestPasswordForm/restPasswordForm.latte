{form form class => 'form-login', novalidate => 'novalidate'}
	{control messageForForm:admin, $flashes, $form}

	<fieldset class="inner">
		<div class="row {if $form['password']->hasErrors()}error{/if}">
			{label password /}<br/>
			<span class="inp-fix">
				{input password class => 'inp-text w-full'}
			</span>
		</div>

		<div class="row {if $form['passwordVerify']->hasErrors()}error{/if}">
			{label passwordVerify /}<br/>
			<span class="inp-fix has-error">
				{input passwordVerify class => 'inp-text w-full'}
			</span>

		</div>
<br>
		<div class="row">

			<button type="submit" class="btn">
				<span>{_rest_password_button}</span>
			</button>
		</div>

	</fieldset>
	<br>
	<a href="{plink 'Sign:default'}">{_lost_password_login_link}</a>

{/form}

