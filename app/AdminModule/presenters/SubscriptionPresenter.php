<?php declare(strict_types = 1);

namespace AdminModule;

use App\Model\Subscription;
use App\Model\SubscriptionModel;
use App\Model\SubscriptionOrder;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\Strings;
use SuperKoderi\Components\IAdminSubscriptionFormFactory;
use SuperKoderi\Components\IAdminSubscriptionOrderFormFactory;
use SuperKoderi\Components\SubscriptionCancelForm;
use SuperKoderi\TranslatorDB;
use Throwable;

/**
 * @property Session $session
 * @property Subscription|null $object
 * @property SubscriptionOrder|null $subscriptionOrder
 */
class SubscriptionPresenter extends BasePresenter
{

	public SessionSection $filterSection;

	/** @inject */
	public TranslatorDB $translatorDB;

	/** @inject */
	public IAdminSubscriptionFormFactory $subscriptionFormFactory;

	/** @inject */
	public IAdminSubscriptionOrderFormFactory $subscriptionOrderFormFactory;

	/** @inject */
	public SubscriptionModel $subscriptionModel;

	private SubscriptionOrder|null $subscriptionOrder = null;

	public function startup(): void
	{
		parent::startup();

		$this->filterSection = $this->session->getSection($this->getFilterSessionName('subscriptionsFilter'));
		if (!isset($this->filterSection->data)) {
			$this->filterSection->data = null;
		}
	}

	public function actionDefault(?string $sortColumn = null, ?string $sortDirection = null): void
	{
		$defaultOrder = 'id desc';
		if ($sortColumn !== null && $sortDirection !== null) {
			$defaultOrder = $sortColumn . ' ' . $sortDirection;
		}
		//$sortColumn = $sortColumn ?: 'id';
		//$sortDirection = strtolower($sortDirection) === 'desc' ? 'desc' : 'asc';

		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->getParam('adminPaging');

		$subscriptionsQuery = $this->orm->subscription->findByFilter($this->filterSection, $this->user, $defaultOrder);
		$subscriptions = $subscriptionsQuery->limitBy($paginator->itemsPerPage, $paginator->offset);

		$paginator->itemCount = $subscriptionsQuery->countStored();


		$this->template->sortColumn = $sortColumn;
		$this->template->sortDirection = $sortDirection;
		$this->template->filterSection = $this->filterSection;
		$this->template->subscriptions = $subscriptions;
		$this->template->isPartner = $this->user->isPartner();
		$this->template->productVariantsReport = $this->orm->subscriptionOrder->getProductVariantReportForNextDays(14, $this->filterSection->data);
	}

	public function actionEdit(int $id): void
	{
		$subscription = $this->orm->subscription->getById($id);
		$this->object = $subscription;
		$this->subscriptionOrder = $subscription->nextSubscriptionOrder;
		$this->template->subscriptionOrder = $this->subscriptionOrder;
		$this->template->cancelReasonTranslate = function ($reason): string {
			return $this->translatorDB->translate(SubscriptionCancelForm::CANCEL_REASON[$reason]);
		};

		if (!isset($this->object)) {
			$this->redirect('default');
		}
	}

	public function actionEditNextOrder(string $hash): void
	{
		$subscriptionOrder = $this->orm->subscriptionOrder->getBy(['hash' => $hash]);
		$this->object = $subscriptionOrder;
		$this->subscriptionOrder = $subscriptionOrder;
		$this->template->subscriptionOrder = $this->subscriptionOrder;

		if (!isset($this->object) || !$this->subscriptionOrder instanceof SubscriptionOrder || !$this->subscriptionOrder->canBeEdited) {
			$this->redirect('default');
		}
	}

	public function actionDelete(int $id): never
	{
		$subscription = $this->orm->subscription->getById($id);

		if (isset($subscription)) {
			$this->orm->subscription->remove($subscription);
			$this->flashMessage('Subscription deleted successfully.');
		} else {
			$this->flashMessage('No such a subscription to be deleted.');
		}

		$this->redirect('default');
	}

	public function actionClearFilter()
	{
		$this->filterSection->data = null;
		$this->template->filterSection = null;

		$this->redirect('default');
	}

	protected function createComponentSubscriptionForm()
	{
		return $this->subscriptionFormFactory->create($this->object);
	}

	protected function createComponentSubscriptionNextOrderForm()
	{
		return $this->subscriptionOrderFormFactory->create($this->subscriptionOrder);
	}

	/**
	 * Edit form factory.
	 * @return UI\Form
	 */
	protected function createComponentFilterForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addMultiSelect('status', 'subscription status', Subscription::STATUSES);
		$form->addSelect('paymentType', 'subscription payment type', [null => ' -- ' ] + Subscription::PAYMENTS);
		$form->addSelect('subPaymentType', 'subscription sub payment type', [null => ' -- ' ] + Subscription::SUB_PAYMENTS);
		$form->addText('fulltext', 'search');
		$form->addText('name', 'name');
		$form->addText('firstname', 'firstname');
		$form->addText('lastname', 'lastname');
		$form->addText('email', 'email');
		$form->addText('hash', 'hash');
		$form->addInteger('productVariantId', 'productVariantId');

		$subscriptionMutation = $this->orm->mutation->findBy(['subscriptionAllowed' => true])->fetchPairs('id', 'name');
		$form->addSelect('subscriptionMutation', 'subscription_mutation', [' -- '] + $subscriptionMutation);


		if ($this->filterSection->data) {
			$data = $this->filterSection->data;

			if (isset($data->paymentType) && !in_array($data->paymentType, array_keys([null => ' -- '] + Subscription::PAYMENTS), true)) {
				$data->paymentType = null;
			}

			if (isset($data->subPaymentType) && !in_array($data->subPaymentType, array_keys([null => ' -- '] + Subscription::SUB_PAYMENTS), true)) {
				$data->subPaymentType = null;
			}

			$form->setDefaults($data);
		}

		$form->addSubmit('save', 'save_button');

		$form->onSuccess[] = [$this, 'filterFormSucceeded'];
		return $form;
	}


	public function filterFormSucceeded($form)
	{
		$values = $form->getValues();

		$this->filterSection->data = $values;

		$this->redirect('this');
	}

	public function handleTryToCreateAgain(string $subscriptionOrderHash): void
	{
		try {
			$subscriptionOrder = $this->orm->subscriptionOrder->getByHash($subscriptionOrderHash);
			if (
				$subscriptionOrder->subscription instanceof Subscription &&
				$subscriptionOrder->subscription->status == Subscription::STATUS_ACTIVE &&
				$subscriptionOrder instanceof SubscriptionOrder &&
				$subscriptionOrder->status == SubscriptionOrder::STATUS_LOW_STOCK) {
				$this->subscriptionModel->processSubscriptionOrder($subscriptionOrder, true);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('this');
	}

	public function handleCreateImmediately(string $subscriptionOrderHash): void
	{
		try {
			$subscriptionOrder = $this->orm->subscriptionOrder->getByHash($subscriptionOrderHash);
			if (
				$subscriptionOrder->subscription instanceof Subscription &&
				$subscriptionOrder->subscription->status == Subscription::STATUS_ACTIVE &&
				$subscriptionOrder instanceof SubscriptionOrder &&
				$subscriptionOrder->canBeEdited) {
				$this->subscriptionModel->forceCreateImmediately($subscriptionOrder);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('this');
	}

	public function handleProcessPaymentNow(string $subscriptionOrderHash): void
	{
		try {
			$subscriptionOrder = $this->orm->subscriptionOrder->getByHash($subscriptionOrderHash);
			if (
				$subscriptionOrder->subscription instanceof Subscription &&
				$subscriptionOrder->subscription->status == Subscription::STATUS_ACTIVE &&
				$subscriptionOrder instanceof SubscriptionOrder &&
				$subscriptionOrder->status == SubscriptionOrder::STATUS_RESERVED) {
				$this->subscriptionModel->processPaymentNow($subscriptionOrder);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('this');
	}

	public function handleDeactivateSubscription(string $subscriptionHash): void
	{
		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			if ($subscription instanceof Subscription) {
				$this->subscriptionModel->deactivateSubscription($subscription, true);
			}
			$this->flashMessage('Subscription deactivated successfully.');
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('this');
	}

	public function handleSkipNextOrder(string $subscriptionHash): void
	{
		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			if (
				$subscription instanceof Subscription) {
				$this->subscriptionModel->skipNextOrder($subscription, true);
			}
			$this->flashMessage('Subscription order skipped.');
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('this');
	}

	public function handleExportDeactiveReason(): void
	{
		$file = implode(';', ['id', 'lang', 'name', 'subscription name', 'email', 'date created','date cancelled', 'reason', 'other reason']) . "\n";

		try {
		$result = $this->orm->subscription->findBy([
			'status' => Subscription::STATUS_DEACTIVATED,
		]);

		/** @var Subscription $subscription */
			foreach($result->fetchAll() as $subscription) {
 			$row = [
				'id' => $subscription->id,
				'mutation' => $subscription->mutation->langCode,
				'userName' => "{$subscription->firstname} {$subscription->lastname}",
				'name' => $subscription->name,
				'email' => $subscription->email,
				'created' => $subscription->created ? $subscription->created->format('j.n.Y H:i:s') : '',
				'cancelled' => $subscription->cancelled ? $subscription->cancelled->format('j.n.Y H:i:s') : '',
				'reason' => $subscription->reason ? $this->translatorDB->translate(SubscriptionCancelForm::CANCEL_REASON[$subscription->reason]) : '',
				'otherReason' => $subscription->otherReason ? $subscription->otherReason : '',
			];

			$file .= implode(";", $row) . "\n";
		}

			$file = Strings::fixEncoding($file);

			$name = "export-deactive-reason.csv";

			header("Pragma: no-cache");
			header("Expires: 0");
			header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
			header("Cache-Control: private", false);
			header("Content-Type: text/csv");
			header('Content-Disposition: attachment; filename="' . $name . '"');
			header("Content-Transfer-Encoding: binary");
			if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) OR empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
				// don't use length if server using compression
				header('Content-Length: ' . strlen($file));
			}

			echo $file;

			$this->terminate();
		} catch (AbortException $e) {
			throw $e;
		} catch (\Throwable $e) {
			$this->flashMessage('export_deactive_reason_error', 'error');
			$this->redirect('this');
		}
	}



}
