<?php

namespace AdminModule;

use App\Model\CreditNoteModel;
use App\Model\Mutation;
use App\Model\Order;
use App\Model\OrderItem;
use App\Model\OrderMapper;
use App\Model\OrderModel;
use App\Model\SubscriptionModel;
use App\Model\SubscriptionOrder;
use App\Model\Task;
use App\Model\VoucherCodeModel;
use DateTime;
use DirectoryIterator;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use Nette\Utils\FileSystem;
use Nette\Utils\Strings;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi;
use SuperKoderi\CreditNoteExporter;
use SuperKoderi\InvoiceExporter;
use SuperKoderi\InvoiceService;
use SuperKoderi\LogicException;
use Throwable;
use Tracy\Debugger;

/**
 * Tree presenter.
 */
class OrderPresenter extends BasePresenter
{
	/** @var array */
	const CSV_IMPORT_DONE_COLUMNS = [
		'<PERSON><PERSON><PERSON>',
		'Datum odeslání',
		'<PERSON>ákaznick<PERSON> reference',
		'Variabilní symbol',
		'Stát',
		'<PERSON><PERSON>',
		'Hmotnost',
		'Hodnota dobírky',
		'Datum Doručení',
		'Čas doručení',
		'Příjemce',
	];

	/** @var OrderModel @inject */
	public $orderModel;

	public $filterSession;

	public SessionSection $filterStatisticsSession;

	/** @var Order */
	public $object;

	/** @var VoucherCodeModel @inject */
	public $voucherCodeModel;

	/** @var SuperKoderi\Email\ISendVoucherFactory @inject */
	public $sendVoucherFactory;

	/** @var SuperKoderi\Model\Pdf\IGeneratePdfFactory @inject */
	public $generatePdfFactory;

	/** @var SuperKoderi\OrderMailService @inject */
	public $orderMailService;

	/** @var SuperKoderi\Noviko\Order\OrderService @inject */
	public $novikoOrderService;

	/** @var InvoiceService @inject */
	public $invoiceService;

	/** @var CreditNoteModel @inject */
	public $creditNoteModel;

	/** @var InvoiceExporter @inject */
	public $invoiceExporter;

	/** @var CreditNoteExporter @inject */
	public $creditNoteExporter;

	private $orderStatuses;

	/** @var array */
	private $creditNotesPossibleItemsAmount;

	/** @var SubscriptionModel @inject */
	public $subscriptionModel;

	public function startup()
	{
		parent::startup();
		$this->filterSession = $this->session->getSection($this->getFilterSessionName('orderFilter'));
		if (!isset($this->filterSession->data)) {
			$this->filterSession->data = null;
		}

		$this->filterStatisticsSession = $this->session->getSection($this->getFilterSessionName('orderStatisticsFilter'));
		if (!isset($this->filterStatisticsSession->data)) {
			$this->filterStatisticsSession->data = null;
		}

		$this->orderStatuses = Order::getConstsByPrefix('STATUS_', 'order_status_');
	}


	public function actionDefault(string $email = null): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->getParam('adminPaging');

		$this->filterSession->defaults = new ArrayHash;
		$this->filterSession->defaults->withoutDeleted = !$this->isDeveloper();
		if (isset($email)) {
			$this->actionFindByEmail($email);
		} else {
			unset($this->filterSession->data->email);
		}

		$ordersQuery = $this->orm->order->findByFilter($this->filterSession, $this->user, 'id desc');
		$orders = $ordersQuery->limitBy($paginator->itemsPerPage, $paginator->offset);

		$paginator->itemCount = $ordersQuery->countStored();

		//$this->template->sum = $this->orm->orderItem->sumPrice($ordersQuery);
		//$this->template->sumDPH = $this->orm->orderItem->sumPriceDPH($ordersQuery);;
		$this->template->filterSession = $this->filterSession;

		$this->template->orders = $orders;
		$this->template->orderStatuses = $this->orderStatuses;

		$this->template->invoiceExportStatus = null;
		$this->template->invoiceExportUrl = null;

		$this->template->allowExport = isset($this->filterSession->data->mutation) && $this->filterSession->data->mutation !== null;

		/** @var Task|null $latestTask */
		$latestTask = $this->orm->task->findBy(['type' => Task::TYPE_INVOICE_EXPORT])->orderBy('id', 'DESC')->fetch();
		if ($latestTask !== null) {
			if (in_array($latestTask->status, [Task::STATUS_IN_QUEUE, Task::STATUS_PROCESSING])) {
				$this->template->invoiceExportStatus = $this->translator->translate('invoice_export_processing');
			} elseif ($latestTask->status == Task::STATUS_ERROR) {
				$this->template->invoiceExportStatus = $this->translator->translate('invoice_export_error');
			} elseif ($latestTask->status = Task::STATUS_DONE) {
				if (is_file($this->invoiceExporter->getZipFilePath($latestTask))) {
					$this->template->invoiceExportUrl = $this->link(":Admin:File:InvoiceExport", ['id' => $latestTask->id]);
				}
			}
		}

	}


	public function actionDetail($id): void
	{
		if (!$id) {
			$this->redirect('default');
		}
		$this->object = $this->orm->order->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}

		$this->checkPermissionMutation($this->object->mutation);
		$this->creditNotesPossibleItemsAmount = $this->object->creditNotesPossibleItemsAmount;
	}


	public function actionLog($id): void
	{
		$this->object = $this->orm->order->getById($id);

		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->getParam('adminPaging');
		$orderLog = $this->orm->orderLog->findBy(['orderId' => $id])->orderBy('id', ICollection::DESC)->limitBy(
			$paginator->itemsPerPage,
			$paginator->offset
		);
		$paginator->itemCount = $orderLog->countStored();

		$this->template->orderLog = $orderLog;
		$this->template->orderStatuses = $this->orderStatuses;
	}


	public function renderDetail(): void
	{
		$this->template->canCreateCreditNote = $this->object->canCreateCreditNote;
	}


	public function actionExport(): void
	{
		$sourceDir = DATA_DIR.'/xml';
		if (!file_exists($sourceDir)) {
			FileSystem::createDir($sourceDir);
		}

		$fileData = $this->fillArrayWithFileNodes( new DirectoryIterator( $sourceDir ) );

		$this->template->dataTree = $fileData;
	}


	public function actionStatisticsByUser(string $orderBy = 'count', string $orderScope = 'desc'): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pagerStatistics');
		$paginator = $this['pagerStatistics']->getPaginator();
		$paginator->itemsPerPage = $this->configService->getParam('adminPaging');

		$this->filterStatisticsSession->defaults = new ArrayHash;
		$this->filterStatisticsSession->defaults->withoutDeleted = !$this->isDeveloper();

		$orderBy = $orderBy === 'count' ? 'orderCount' : $orderBy;
		$orders = $this->orm->order->findStatistics($this->filterStatisticsSession, $orderBy . ' ' . $orderScope, $paginator->itemsPerPage, $paginator->offset);
		$paginator->itemCount = $this->orm->order->findStatisticsCount($this->filterStatisticsSession);

		$this->template->filterStatisticsSession = $this->filterStatisticsSession;
		$this->template->orders = $orders;
		$this->template->orderStatuses = $this->orderStatuses;
		$this->template->orderBy = $orderBy;
		$this->template->orderScope = $orderScope;
		$this->template->ordersByEmail = $this->getOrdersByEmail($orders);
	}

	private function getOrdersByEmail(array $orders): array
	{
		$emails = [];
		foreach ($orders as $order) {
			$emails[] = $order->email;
		}

		$detailedOrdersQuery = $this->orm->order->findByFilter($this->filterStatisticsSession, $this->user, 'email asc');
		$detailedOrders = $detailedOrdersQuery->findBy([
			'status!=' => Order::STATUS_CANCEL,
			'email' => $emails
		]);

		$ordersByEmail = [];
		foreach ($detailedOrders as $detailedOrder) {
			$ordersByEmail[$detailedOrder->email][] = $detailedOrder;
		}

		return $ordersByEmail;
	}


	private function  fillArrayWithFileNodes( DirectoryIterator $dir , int $limit = 30): array
		{
			$data = array();
			foreach ( $dir as $node )
			{
				if ( $node->isDir() && !$node->isDot() )
				{
					$data[$node->getFilename()] = $this->fillArrayWithFileNodes( new DirectoryIterator( $node->getPathname() ), $limit );
				}
				else if ( $node->isFile() )
				{
					$fullPath = $node->getPath();
					$fullPath = explode('/', $fullPath);
					$path = array_pop($fullPath);
					$path = array_pop($fullPath).'/'.$path;
					$data[] = [
						'file' => $node->getFilename(),
						'path' => $path.'/'.$node->getFilename(),
					];
				}

			}

		return $data;
	}


	public function handleGetExportFile(string $filePath): void
	{
		$sourceDir = DATA_DIR.'/xml';

		try {
			$file = $sourceDir.'/'.$filePath;
			$filename = explode('/', $filePath);
			$filename = array_pop($filename);

			header('Content-Description: File Transfer');
			header('Content-Type: text/xml');
			header('Content-Disposition: attachment; filename="'.$filename.'"');
			header('Expires: 0');
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Content-Length: ' . filesize($file));
			readfile($file);
			die;
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('Při generování XML došlo k chybě', 'error');
		}
	}


	/**
	 * @return UI\Form
	 */
	protected function createComponentEditForm()
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$states = ['' => ''] + $this->object->mutation->getStates();

		//$form->addSelect('status', 'change_order_status', $this->orderStatuses);
		//$form->addText('name', 'user_name');
		$form->addText('firstname', 'user_firstname');
		$form->addText('lastname', 'user_lastname');
		$form->addText('email', 'email');
		$form->addText('phone', 'phone');
		$form->addText('street', 'address');
		$form->addText('city', 'city');
		$form->addText('zip', 'zip');
		$form->addSelect('state', 'state', $states);
		$form->addTextArea('infotext', 'Note', 50, 5);

		$form->addText('ic', 'company_id');
		$form->addText('dic', 'vat_number');
		$form->addText('company', 'company');

		$form->addText('dName', 'user_name');
		$form->addText('dFirstname', 'user_firstname');
		$form->addText('dLastname', 'user_lastname');
		$form->addText('dCompany', 'company');
		$form->addText('dStreet', 'street');
		$form->addText('dCity', 'city');
		$form->addText('dPhone', 'phone');
		$form->addText('dZip', 'zip');
		$form->addSelect('dState', 'state', $states);
		$form->addText('dInfo', 'info');

		//$form->addText('barCode', 'barCode');

		if ($this->object) {
			$form->setDefaults($this->object->getFormData($form));
		}

		$form->addSubmit('save', 'Save');

		$form->onSuccess[] = [$this, 'editFormSucceeded'];
		return $form;
	}


	public function editFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		if ($this->orderModel->saveHead($this->object, $values)) {
			$result = $this->novikoOrderService->saveHeader($this->object);

			$this->flashMessage('OK', 'ok');

			if (is_array($result)) {
				list($msg, $type) = $result;
				$this->flashMessage($msg, $type);
			}

			if (!$this->isAjax()) {
				$this->redirect('this', $this->object->id);
			} else {
				$this->redrawControl();
			}
		} else {
			$this->flashMessage('Error', 'error');
		}
	}

	/**
	 * @return UI\Form
	 */
	protected function createComponentStornoForm()
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addSelect('cancelReason', 'order_status_cancel_reason', Order::getConstsByPrefix('CANCEL_REASON'));
		$form->addSubmit('storno', 'Storno');
		$form->onSuccess[] = [$this, 'stornoFormSucceeded'];
		return $form;

	}


	/**
	 * @param UI\Form $form
	 * @param ArrayHash $values
	 * @throws AbortException
	 */
	public function stornoFormSucceeded(UI\Form $form, ArrayHash $values)
	{
		try {
			$novikoResult = true;

			if ($this->object->novikoId && $this->object->novikoStatus < SuperKoderi\Noviko\Order\Order::ID_STATUS_TO_DELIVERY) { // storno v Noviku lze pouze do stavu 20 (K predani dopravci); stav 11 resime az dal
				list($msg, $type) = $this->novikoOrderService->storno($this->object, $values->cancelReason);

				if ($type == 'error') {
					$novikoResult = false;
				}
				$this->flashMessage($msg, $type);
			}

			if ($novikoResult) {
				$data = $this->creditNoteModel->getDataForLastCreditNote($this->object, $values->cancelReason);
				$this->orderModel->storno($this->object, $values->cancelReason);
				$creditNote = $this->creditNoteModel->create($this->object, $data);

				if ($this->object->subscriptionOrder instanceof SubscriptionOrder) {
					try {
						$this->subscriptionModel->cancelSubscriptionOrder($this->object->subscriptionOrder, SubscriptionOrder::CANCELLING_REASON_CANCEL_FORM);
					} catch (Throwable $e) {
						Debugger::log($e);
					}
				}
				/*if ($creditNote) { // gopay refundace
					$this->creditNoteModel->doPaymentRefund($this->object, $creditNote);
				}*/

				$this->orderMailService->orderStorno($this->object, $creditNote);
				$this->flashMessage('orde_msg_storno_ok', 'ok');
			}

			$this->redirect('this', $this->object->id);

		} catch (AbortException $e) {
			throw $e;
		} catch (LogicException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('msg_operation_failed', 'error');
		}
	}


	/**
	 * @param $id
	 * @throws AbortException
	 */
	public function handleNovikoUpdate($id)
	{
		list($msg, $type) = $this->novikoOrderService->updateStatus($this->object);
		$this->flashMessage($msg, $type);
		$this->redirect('this', $this->object->id);
	}


	/**
	 * Situace: stornovana obj s platbou online ktera jeste neni v Noviku
	 *
	 * @throws AbortException
	 */
	public function handleRevokeStorno()
	{
		try {

			if (!$this->object->isCanRevokeStorno) {
				throw new LogicException('Storno cannot be revoked');
			}

			$this->object->status = Order::STATUS_NEW;
			$this->object->paymentStatus = Order::ONLINE_PAYMENT_PAID_NEXT_ATTEMPT;
			$this->object->cancelReason = null;
			$this->object->canceled = null;

			if (empty($this->object->invoiceNumber)) {
				$this->object = $this->orderModel->doInvoice($this->object, false);
			}

			$this->orm->order->persistAndFlush($this->object);
			$this->flashMessage('orde_msg_storno_ok', 'ok');
			$this->redirect('this', $this->object->id);

		} catch (AbortException $e) {
			throw $e;
		} catch (LogicException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('msg_operation_failed', 'error');
		}
	}


	/**
	 * Edit form factory.
	 * @return UI\Form
	 */
	protected function createComponentFilterForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		//$objectId = isset($this->object->id) ? $this->object->id : '';

		$form->addSelect('mutation', 'product_mutation', $this->getAllowedMutationList())
			->setPrompt('all');

		$form->addMultiSelect('status', 'show_only', $this->orderStatuses);
		$form->addText('fulltext', 'search');
		$form->addText('parcelNumber', 'parcel_number');
		$form->addText('firstname', 'firstname');
		$form->addText('lastname', 'lastname');
		$form->addText('orderEmail', 'label_order_email');

		$form->addText('from', 'filter_from')->setDefaultValue('01. 01. 2020');
		$form->addText('to', 'filter_to')->setDefaultValue(date('d. m. Y'));
		$form->addSelect('filterBy', 'filter_by', [
			'created' => 'filter_created',
			'invoiceDate' => 'filter_invoice_date',
			'createdCreditNote' => 'filter_created_credit_note',
		])->setDefaultValue('created');
		$form->addText('novikoId', 'filter_noviko_id');

		$novikoStatusTitles = \SuperKoderi\Noviko\Order\Order::$statuses;
		foreach ($novikoStatusTitles as $value => $title) {
			$novikoStatusTitles[$value] = $title . (is_numeric($value) ?  ' (' . $value . ')'  : '');
		}
		$novikoStatusTitles = [OrderMapper::FILTER_ALL => '---'] + $novikoStatusTitles;

		$form->addSelect('novikoStatus', 'filter_noviko_status', $novikoStatusTitles)
			->setDefaultValue('all');

        $form->addSelect('orderItemType', 'filter_order_type', [
            null => '---',
            OrderItem::TYPE_PRODUCT => 'Product',
            OrderItem::TYPE_SUBSCRIPTION => 'Subscription',
        ]);

		if ($this->filterSession->data) $form->setDefaults($this->filterSession->data);

		$form->addSubmit('save', 'save_button');

		$form->onSuccess[] = [$this, 'filterFormSucceeded'];
		return $form;
	}


	public function filterFormSucceeded($form)
	{
		$values = $form->getValues();

		$values->fromDate = DateTime::createFromFormat('d. m. Y', $values['from']);
		$values->toDate = DateTime::createFromFormat('d. m. Y', $values['to']);
		$this->filterSession->data = $values;

		$this->redirect('this');
	}


	public function actionClearFilter()
	{
		$this->filterSession->data = null;

		$this->redirect('default');
	}


	/**
	 * Edit form factory.
	 * @return UI\Form
	 */
	protected function createComponentFilterStatisticsForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		//$objectId = isset($this->object->id) ? $this->object->id : '';

		$form->addSelect('mutation', 'product_mutation', $this->getAllowedMutationList())
			->setPrompt('all');

		$form->addMultiSelect('status', 'show_only', $this->orderStatuses);
		$form->addText('fulltext', 'search');

		$defaultFromDate = new DateTime();
		$defaultFromDate->setDate($defaultFromDate->format('Y'), 1, 1);
		$form->addText('from', 'filter_from')->setDefaultValue($defaultFromDate->format('d. m. Y'));
		$form->addText('to', 'filter_to')->setDefaultValue(date('d. m. Y'));

		if ($this->filterStatisticsSession->data) $form->setDefaults($this->filterStatisticsSession->data);

		$form->addSubmit('save', 'save_button');

		$form->onSuccess[] = [$this, 'filterStatisticsFormSucceeded'];
		return $form;
	}


	public function filterStatisticsFormSucceeded(UI\Form $form): void
	{
		$values = $form->getValues();

		$values->fromDate = DateTime::createFromFormat('d. m. Y', $values['from']);
		$values->toDate = DateTime::createFromFormat('d. m. Y', $values['to']);
		$this->filterStatisticsSession->data = $values;

		$this->redirect('this');
	}


	public function actionClearFilterStatistics()
	{
		$this->filterStatisticsSession->data = null;

		$this->redirect('statisticsByUser');
	}


	/**
	 * @throws AbortException
	 */
	public function actionFindByEmail(string $email, bool $forceChangeFilter = false): void
	{
		$filter = $this->filterSession->data;

		if (!isset($filter)) {
			$filter = new ArrayHash();
		}

		if (!isset($filter->status)) {
			$filter->status = [];
		}

		$this->template->email = $email;

		$email = htmlspecialchars($email);
		$filter->email = $email;

		if ($forceChangeFilter) {
			$allPossibleStatuses = $this->orderStatuses;
			unset($allPossibleStatuses['cancel']);
			$filter->status = array_keys($allPossibleStatuses);

			$this->filterSession->data = $filter;

			if (isset($this->filterStatisticsSession->data->mutation)) {
				$this->filterSession->data->mutation = $this->filterStatisticsSession->data->mutation;
			} else {
				if (isset($this->filterSession->data->mutation)) {
					unset($this->filterSession->data->mutation);
				}
			}

			$from = $this->filterStatisticsSession->data->from ??  '01. 01. 2020';
			$this->filterSession->data->from = $from;
			$this->filterSession->data->fromDate = DateTime::createFromFormat('d. m. Y', $from);

			$to = $this->filterStatisticsSession->data->to ??  date('d. m. Y');
			$this->filterSession->data->to = $to;
			$this->filterSession->data->toDate = DateTime::createFromFormat('d. m. Y', $to);

			$this->redirect('default', $email);
		}
		$this->filterSession->data = $filter;
	}


	public function handleSendVoucherToEmail($orderItemId)
	{
		$orderItem = $this->orm->orderItem->getById($orderItemId);

		$emailTo = $orderItem->order->email;
		$from = [
			$this->configService->get('orderEmail', 'mailFrom'),
			$this->configService->get('orderEmail', 'mailFromName')
		];

		$this->generatePdfFactory = $this->generatePdfFactory->create("cs", "");

		// odeslani na email
		$codes = explode(",", $orderItem->variantName ?? '');
		foreach ($codes as $code) {
			$voucherInfo = $this->voucherCodeModel->getVoucherInfo($code, $orderItem, TRUE);

			$pdfString = $this->generatePdfFactory->generateVoucherPDF('voucher', 'darkovy-poukaz', $voucherInfo, 'S');
			$voucherInfo->attach = $pdfString;
			// odeslat
			$this->sendVoucherFactory
				->create()
				->send($from, $emailTo, 'voucher_mail_subject', 'voucher', $voucherInfo)
				->markAsSent($orderItem); // ulozeni informace o odeslani
		}

		$this->redirect('Order:detail', ['id' => $orderItem->order->id]);
	}


	public function handleMakePdf($orderItemId)
	{
		$orderItem = $this->orm->orderItem->getById($orderItemId);

		$this->generatePdfFactory = $this->generatePdfFactory->create("cs", "");

		$codes = explode(",", $orderItem->variantName ?? '');
		$voucherInfo = [];
		foreach ($codes as $code) {
			$voucherInfo[] = $this->voucherCodeModel->getVoucherInfo($code, $orderItem, TRUE);
		}

		$this->generatePdfFactory->generateVoucherPDF('voucher', 'darkovy-poukaz', $voucherInfo, 'D');
	}


	/**
	 * @throws AbortException
	 */
	/*public function handleDeleteOrder()
	{
		if ($this->object->status != Order::STATUS_CANCEL) {
			$this->flashMessage('Objednávku nelze smazat.');
			$this->redirect('this');
		}

		$this->orderModel->saveHead($this->object, [
			'isDeleted' => 1,
		]);

		$this->flashMessage('Objednávka smazána.', 'ok');
		$this->redirect('Order:default');
	}*/


	public function handleInvoicePdf(): void
	{
		try {
			$mpdf = $this->invoiceService->create($this->object);
			$mpdf->Output($this->object->invoicePdfFileName, 'D');
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('Při generování PDF došlo k chybě', 'error');
		}
	}


	public function handleInvoiceXML(): void
	{
		try {
			$DOMDocument = $this->invoiceService->createXML([$this->object], $this->object->mutation);
			$tempDirectory = TEMP_DIR.'/xml';
			$file = $tempDirectory.'/'.$this->object->invoiceXMLFileName;
			if (!file_exists($tempDirectory)) {
				mkdir($tempDirectory, 0777, true);
			}

			file_put_contents($file, $DOMDocument->saveXML());

			header('Content-Description: File Transfer');
			header('Content-Type: text/xml');
			header('Content-Disposition: attachment; filename="'.$this->object->invoiceXMLFileName.'"');
			header('Expires: 0');
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Content-Length: ' . filesize($file));
			readfile($file);
			die;
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('Při generování XML došlo k chybě', 'error');
		}
	}


	public function handleInvoiceXMLs(): void
	{
		ini_set('memory_limit', '500M');

		$orders = $this->orm->order->findByFilter($this->filterSession, $this->user, 'id desc')->fetchAll();
		$ordersByMutation = [];
		/** @var Order $order */
		foreach ($orders as $order) {
			if (!isset($ordersByMutation[$order->mutation->langMenu])) {
				$ordersByMutation[$order->mutation->langMenu] = [];
				$ordersByMutation[$order->mutation->langMenu]['mutation'] = $order->mutation;
			}

			if ($order->invoiceNumber !== null) {
				$ordersByMutation[$order->mutation->langMenu][] = $order;
			}
		}

		foreach ($ordersByMutation as $mutationKey => $orderGroup) {
			try {

				/** @var Mutation $mutation */
				$mutation = $orderGroup['mutation'];
				unset($orderGroup['mutation']);
				$DOMDocument = $this->invoiceService->createXML($orderGroup, $mutation);
				$tempDirectory = TEMP_DIR.'/xml';

				$filterData = $this->filterSession->data;

				$dateName = "";
				if (isset($filterData)) {
					if (isset($filterData->from) && isset($filterData->to)) {
						$dateName .= "_".$filterData->from."-".$filterData->to;
					}

					if ((bool)$filterData->fulltext) {
						$dateName .= "_search-".$filterData->fulltext;
					}
				} else {
					$dateName = "-ALL";
				}
				$filename = "OrderInvoiceExportXML-".$mutationKey.$dateName.".xml";
				$file = $tempDirectory."/".$filename;//OrderExportXML-".$mutationKey.$dateName.".xml";

				if (!file_exists($tempDirectory)) {
					mkdir($tempDirectory, 0777, true);
				}

				file_put_contents($file, $DOMDocument->saveXML());

				header('Content-Description: File Transfer');
				header('Content-Type: text/xml');
				header('Content-Disposition: attachment; filename="'.$filename.'"');
				header('Expires: 0');
				header('Cache-Control: must-revalidate');
				header('Pragma: public');
				header('Content-Length: ' . filesize($file));
				readfile($file);
				die;
			} catch (Throwable $e) {
				Debugger::log($e);
				$this->flashMessage('Při generování XML došlo k chybě', 'error');
			}
		}
	}


	public function handleCreditNoteXMLs(): void
	{
		$orders = $this->orm->order->findByFilter($this->filterSession, $this->user, 'id desc')->fetchAll();
		$ordersByMutation = [];
		/** @var Order $order */
		foreach ($orders as $order) {
			if (!isset($ordersByMutation[$order->mutation->langMenu])) {
				$ordersByMutation[$order->mutation->langMenu] = [];
				$ordersByMutation[$order->mutation->langMenu]['mutation'] = $order->mutation;
			}

			if ($order->invoiceNumber !== null) {
				$ordersByMutation[$order->mutation->langMenu][] = $order;
			}
		}

		foreach ($ordersByMutation as $mutationKey => $orderGroup) {
			try {

				if ($mutationKey != 'CZ') {
					continue;
				}

				/** @var Mutation $mutation */
				$mutation = $orderGroup['mutation'];
				unset($orderGroup['mutation']);

				$creditNotes = [];
				/** @var Order $singleOrder */
				foreach ($orderGroup as $singleOrder) {
					if ($singleOrder->hasCreditNote) {
						foreach ($singleOrder->creditNotes as $creditNote) {
							$creditNotes[] = $creditNote;
						}
					}
				}


				$DOMDocument = $this->invoiceService->createCreditNoteXML($creditNotes, $mutation);
				$tempDirectory = TEMP_DIR.'/xml';

				$filterData = $this->filterSession->data;

				$dateName = "";
				if (isset($filterData)) {
					if (isset($filterData->from) && isset($filterData->to)) {
						$dateName .= "_".$filterData->from."-".$filterData->to;
					}

					if ((bool)$filterData->fulltext) {
						$dateName .= "_search-".$filterData->fulltext;
					}
				} else {
					$dateName = "-ALL";
				}
				$filename = "OrderCreditNoteExportXML-".$mutationKey.$dateName.".xml";
				$file = $tempDirectory."/".$filename;

				if (!file_exists($tempDirectory)) {
					mkdir($tempDirectory, 0777, true);
				}

				file_put_contents($file, $DOMDocument->saveXML());

				header('Content-Description: File Transfer');
				header('Content-Type: text/xml');
				header('Content-Disposition: attachment; filename="'.$filename.'"');
				header('Expires: 0');
				header('Cache-Control: must-revalidate');
				header('Pragma: public');
				header('Content-Length: ' . filesize($file));
				readfile($file);
				die;
			} catch (Throwable $e) {
				Debugger::log($e);
				$this->flashMessage('Při generování XML došlo k chybě', 'error');
			}
		}
	}


	public function handleStatisticsCSV(string $orderBy = 'count', string $orderScope = 'desc'): void
	{
		header("Content-Type: text/csv; charset=utf-8");
		header("Content-disposition: attachment; filename=\"statistics.csv\"");

		$ordersQuery = $this->orm->order->findByFilter($this->filterStatisticsSession, $this->user, 'id desc');

		$ordersByEmail = [];
		$noCanceledOrders = $ordersQuery->findBy([
			'status!=' => Order::STATUS_CANCEL
		]);

		foreach ($noCanceledOrders as $order) {
			$ordersByEmail[$order->email][] = $order;
		}

		uasort($ordersByEmail, function ($a, $b) use ($orderBy, $orderScope) {
			if ($orderBy == 'count') {
				if ($orderScope == 'desc') {
					return count($b) <=> count($a);
				} else {
					return count($a) <=> count($b);
				}

			} else {
				if ($orderScope == 'desc') {
					return Strings::lower($b[0]->$orderBy) <=> Strings::lower($a[0]->$orderBy);
				} else {
					return Strings::lower($a[0]->$orderBy) <=> Strings::lower($b[0]->$orderBy);
				}
			}
		});

		$groupedOrders = array_values($ordersByEmail);

		$csvData = [
			array(
				$this->translator->translate("lastname"),
				$this->translator->translate("firstname"),
				$this->translator->translate("email"),
				$this->translator->translate("OrderCount"),
				$this->translator->translate("TotalPrice"),
				$this->translator->translate("total_price_vat"),
			)
		];

		foreach ($groupedOrders as $orderGroup) {
			$lastOrder = $orderGroup[count($orderGroup) - 1];

			$usedMutations = [];

			$ordersByMutation = [];

			$ordersByMutationData = [
				'totalPrice' => 0,
				'totalPriceDPH' => 0,
				'count' => 0
			];
			foreach ($orderGroup as $order) {
				$mutation = $order->mutation;
				$langCode = $mutation->langCode;

				$usedMutations[$langCode] = $mutation;

				if (!isset($ordersByMutation[$langCode])) {
					$ordersByMutation[$langCode] = $ordersByMutationData;
				}

				$ordersByMutation[$langCode]['count'] = $ordersByMutation[$langCode]['count'] + 1;
				$ordersByMutation[$langCode]['totalPrice'] = $ordersByMutation[$langCode]['totalPrice'] + $order->totalPrice;
				$ordersByMutation[$langCode]['totalPriceDPH'] = $ordersByMutation[$langCode]['totalPriceDPH'] + $order->totalPriceDPH;

			}

			$counts = "";
			$totalPrices = "";
			$totalPricesDPH = "";
			$first = true;
			foreach ($ordersByMutation as $langCode => $data) {
				SuperKoderi\Templating\Helpers::$mutation = $usedMutations[$langCode];
				$counts .= ($first ? '' : "\n") . $data['count'];
				$totalPrices .= ($first ? '' : "\n") . SuperKoderi\Templating\Helpers::priceFormat($data['totalPrice']);
				$totalPricesDPH .= ($first ? '' : "\n") . SuperKoderi\Templating\Helpers::priceFormat($data['totalPriceDPH']);

				$first = false;
			}

			$csvData[] = array(
				$lastOrder->lastname,
				$lastOrder->firstname,
				$lastOrder->email,
				$counts,
				$totalPrices,
				$totalPricesDPH
			);
		}

		$fp = fopen('php://output', 'wb');

		if ($fp !== false) {
			fputs($fp, $bom = (chr(0xEF) . chr(0xBB) . chr(0xBF)));

			$delimiter = ';';
			foreach ($csvData as $csvLine) {
				fputcsv($fp, $csvLine, $delimiter);
			}
		}

		die;
	}


	public function handleCreditNotePdf($creditNoteId)
	{
		$creditNote = $this->orm->creditNote->getById($creditNoteId);

		if (!$creditNote) {
			$this->redirect('this');
		}

		$this->checkPermissionMutation($creditNote->order->mutation);

		try {
			$mpdf = $this->invoiceService->createCreditNote($this->object, $creditNote);
			$mpdf->Output($creditNote->pdfFileName, 'D');
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('Při generování PDF došlo k chybě', 'error');
		}
	}


	public function handleCreditNoteXML($creditNoteId): void
	{
		$creditNote = $this->orm->creditNote->getById($creditNoteId);

		if (!$creditNote) {
			$this->redirect('this');
		}

		$this->checkPermissionMutation($creditNote->order->mutation);

		try {
			$DOMDocument = $this->invoiceService->createCreditNoteXML([$creditNote], $creditNote->order->mutation);
			$tempDirectory = TEMP_DIR.'/xml';
			$file = $tempDirectory.'/'.$creditNote->XMLFileName;
			if (!file_exists($tempDirectory)) {
				mkdir($tempDirectory, 0777, true);
			}

			file_put_contents($file, $DOMDocument->saveXML());

			header('Content-Description: File Transfer');
			header('Content-Type: text/xml');
			header('Content-Disposition: attachment; filename="'.$creditNote->XMLFileName.'"');
			header('Expires: 0');
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Content-Length: ' . filesize($file));
			readfile($file);
			die;
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('Při generování XML došlo k chybě', 'error');
		}
	}


	/**
	 * @return UI\Form
	 */
	protected function createComponentCreditNoteForm()
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		foreach ($this->object->itemsForCreditNote as $item) {
			if (!empty($this->creditNotesPossibleItemsAmount[$item->id])) {
				$container = $form->addContainer($item->id);
				$container->addHidden('id', $item->id);

				$amounts = range(0, $this->creditNotesPossibleItemsAmount[$item->id]);
				$amounts[0] = 'credit_note_select_amount_skip_item';
				$container->addSelect('amount', 'order_status_cancel_reason', $amounts);
			}
		}
		$reasonsForCreditNotes = [Order::CANCEL_REASON_WITHDRAW_CONTRACT, Order::CANCEL_REASON_COMPLAINT_GOODS];
		$form->addSelect('cancelReason', 'credit_note_cancel_reason', array_combine($reasonsForCreditNotes, $reasonsForCreditNotes));// jen pro D, all = Order::getConstsByPrefix('CANCEL_REASON')
		$form->addSubmit('save', 'credit_note_btn_create');

		$form->onValidate[] = [$this, 'creditNoteFormValidate'];
		$form->onSuccess[] = [$this, 'creditNoteFormSucceeded'];

		$form->action .= '#orderItem';
		return $form;
	}


	public function creditNoteFormValidate(UI\Form $form, ArrayHash $values)
	{
		$amount = 0;

		foreach ($this->object->itemsForCreditNote as $item) {
			if (isset($values[$item->id])) {
				$amount += $values[$item->id]->amount;
			}
		}

		if (!$amount) {
			$form->addError('credit_note_msg_error_no_item');
		}
	}


	public function creditNoteFormSucceeded(UI\Form $form, ArrayHash $values)
	{
		try {
			$creditNote = $this->creditNoteModel->create($this->object, $values);
			$this->flashMessage('credit_note_msg_ok_created', 'ok');

			/*
			 * // gopay refundace
			$paymentRefundResult = $this->creditNoteModel->doPaymentPartiallyRefund($this->object, $creditNote);

			if ($paymentRefundResult === true) {
				$this->flashMessage('credit_note_msg_ok_refunded', 'ok');
			} elseif ($paymentRefundResult === false) {
				$this->flashMessage('credit_note_msg_ok_refunded', 'ok');
			}
			*/

			if ($this->object->creditNotesPossibleItemsAmountSum) {
				$this->orderMailService->creditNoteStorno($this->object, $creditNote);

			} else { // byly pokryty všechny položky = nastavit stav plna refundace + storno celé obj + storno email s D

				/* // gopay refundace
				if ($this->object->hasInvoice && $this->object->paymentType == Order::PAYMENT_ONLINE && $this->object->paymentId && $paymentRefundResult && $this->object->paymentStatus == Order::ONLINE_PAYMENT_PARTIALLY_REFUNDED) { //
					$this->object->paymentStatus = Order::ONLINE_PAYMENT_REFUNDED;
					$this->orm->persistAndFlush($this->object);
				}*/

				$this->orderModel->storno($this->object, $values->cancelReason);
				$this->orderMailService->orderStorno($this->object, $creditNote);

				if ($this->object->subscriptionOrder instanceof SubscriptionOrder) {
					$this->subscriptionModel->cancelSubscriptionOrder($this->object->subscriptionOrder, SubscriptionOrder::CANCELLING_REASON_CREDIT_NOTE_FORM);
				}
				$this->flashMessage('orde_msg_storno_ok', 'ok');
			}

			$this->redirect('this#'); // # = potlaceni #orderItem, jinak to skoci dolu a ok msg neni videt

		} catch (AbortException $e) {
			throw $e;
		} catch (LogicException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('msg_operation_failed', 'error');
		}
	}


	public function handleExport(): void
	{
		$data = $this->invoiceExporter->storeFilter2TaskData($this->filterSession);

		$this->orm->task->cancelTasks(Task::TYPE_INVOICE_EXPORT);
		$task = $this->orm->task->createTask(Task::TYPE_INVOICE_EXPORT, $data);

		$this->orm->flush();
		$this->flashMessage('invoice_export_requested', 'ok');

		$filteredCount = $this->invoiceExporter->getOrderCount($task);
		$maxCount = (int)$this->configService->getParam('invoiceExport', 'limit');
		if ($maxCount < $filteredCount) {
			$this->flashMessage($this->translator->translate('invoice_export_limit_exceeded', ['filteredCount' => $filteredCount, 'maxCount' => $maxCount]), 'error', false);
		}

		$this->redirect('this');
	}

	public function handleExportCreditNotes(): void
	{
		$data = $this->creditNoteExporter->storeFilter2TaskData($this->filterSession);

		$this->orm->task->cancelTasks(Task::TYPE_CREDIT_NOTE_EXPORT);
		$task = $this->orm->task->createTask(Task::TYPE_CREDIT_NOTE_EXPORT, $data);

		$this->orm->flush();
		$this->flashMessage('credit_note_export_requested', 'ok');

		$filteredCount = $this->creditNoteExporter->getOrderCount($task);
		$maxCount = (int)$this->configService->getParam('invoiceExport', 'limit');
		if ($maxCount < $filteredCount) {
			$this->flashMessage($this->translator->translate('invoice_export_limit_exceeded', ['filteredCount' => $filteredCount, 'maxCount' => $maxCount]), 'error', false);
		}

		$this->redirect('this');
	}


	public function handleStatistics(int $actionCode = 1): void
	{
		$this->template->statistics = $this->orderModel->getFinancialStatisticsByMutation($this->filterSession, $this->user);
	}


	public function handleReSendOrderMail(string $id) {

		if ($this->user->isDeveloper()) {
			$order = $this->orm->order->getBy([
				'id' => (int)$id
			]);

			if ($order !== null) {;
				$this->orm->setMutation($order->mutation);
				$this->mutationHolder->setMutation($order->mutation);
				SuperKoderi\Templating\Helpers::$mutation = $order->mutation; // kvůli měnám, datumům

				$this->orderMailService->normalConfirm($order);
				$this->flashMessage("ORDER MAIL SENT");
			} else {
				$this->flashMessage("ORDER NOT FOUND");
			}

		} else {
			$this->flashMessage("ACCESS RESTRICTED");
		}

		$this->redrawControl();
	}
}
