<?php declare(strict_types=1);

namespace AdminModule;

use App\Model\Task;
use Nette\Application\Responses\FileResponse;
use SuperKoderi\InvoiceExporter;
use SuperKoderi\CreditNoteExporter;

class FilePresenter extends BasePresenter
{

	/** @var InvoiceExporter @inject */
	public $orderExporter;

	/** @var CreditNoteExporter @inject */
	public $creditNoteExporter;


	public function startup(): void
	{
		parent::startup();
	}


	public function actionInvoiceExport(int $id): void
	{
		/** @var Task|null $task */
		$task = $this->orm->task->getById($id);

		if ($task === null) {
			$this->error($this->translator->translate('invoice_export_not_found', ['id' => $id]));
			die();
		}

		$filePath = $this->orderExporter->getZipFilePath($task);

		if (!is_file($filePath)) {
			$this->error($this->translator->translate('invoice_export_not_found', ['id' => $id]));
			die();
		}

		$response = new FileResponse($filePath, basename($filePath), 'application/zip');
		$this->sendResponse($response);
	}


	public function actionCreditNoteExport(int $id): void
	{
		/** @var Task|null $task */
		$task = $this->orm->task->getById($id);

		if ($task === null) {
			$this->error($this->translator->translate('invoice_export_not_found', ['id' => $id]));
			die();
		}

		$filePath = $this->creditNoteExporter->getZipFilePath($task);

		if (!is_file($filePath)) {
			$this->error($this->translator->translate('invoice_export_not_found', ['id' => $id]));
			die();
		}

		$response = new FileResponse($filePath, basename($filePath), 'application/zip');
		$this->sendResponse($response);
	}

	public function actionInvoiceXMLExport(int $id): void
	{
		/** @var Task|null $task */
		$task = $this->orm->task->getById($id);

		if ($task === null) {
			$this->error($this->translator->translate('invoice_export_not_found', ['id' => $id]));
			die();
		}

		$filePath = $this->orderExporter->getZipFilePath($task);

		if (!is_file($filePath)) {
			$this->error($this->translator->translate('invoice_export_not_found', ['id' => $id]));
			die();
		}

		$response = new FileResponse($filePath, basename($filePath), 'application/zip');
		$this->sendResponse($response);
	}

}
