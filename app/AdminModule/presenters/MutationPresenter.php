<?php

namespace AdminModule;

use App\AdminModule\Components\SettingsForm\ISettingsFormFactory;
use App\AdminModule\Components\SettingsForm\SettingsForm;
use App\Model\MutationModel;
use Nette\Application\UI;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi;

class MutationPresenter extends BasePresenter
{
	/** @inject */
	public MutationModel $mutationModel;

	/** @inject */
	public SuperKoderi\TranslatorDB $translatorDB;

	/** @inject */
	public SuperKoderi\Components\IFlagsFormFactory $flagsFormFactory;

	/** @inject */
	public SuperKoderi\Components\ISynonymsFormFactory $synonymsFormFactory;

	/** @inject */
	public ISettingsFormFactory $settingsFormFactory;

	public function startup(): void
	{
		parent::startup();
	}

	public function actionDefault(): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = 50; // TODO - FIX PAGING - $this->configService->getParam('adminPaging');

		$cfg = [];

		if ((bool)$this->user->getPartnerAllowedMutationIds()) {
			$cfg[] = ['id=' => $this->user->getPartnerAllowedMutationIds()];
		}

		if (count($cfg) == 1) {
			$cfg = $cfg[0];
		} elseif (count($cfg) > 1) {
			array_unshift($cfg, ICollection::OR);
		}

		$mutations = $this->orm->mutation->findBy($cfg)->limitBy($paginator->itemsPerPage, $paginator->offset);

		$paginator->itemCount = $mutations->count();

		if ($this->isAjax()) {
			$this->redrawControl('editForm');
		}

		$this->template->mutations = $mutations;
	}


	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->mutation->getById($id);
		if (!isset($this->object)) {
			$this->redirect('default');
		}
		$this->checkPermissionMutation($this->object);
	}


	public function actionFlags($id)
	{
		$this->object = $this->orm->mutation->getById($id);
		if (!$this->object) {
			$this->redirect('default');
		}
		$this->checkPermissionMutation($this->object);
	}

	public function actionSynonyms($id)
	{
		$this->object = $this->orm->mutation->getById($id);
		if (!$this->object) {
			$this->redirect('default');
		}
		$this->checkPermissionMutation($this->object);
	}


	public function actionSettings(int $id): void
	{
		$this->object = $this->orm->mutation->getById($id);
		if (!isset($this->object)) {
			$this->redirect('default');
		}

		$this->checkPermissionMutation($this->object);
	}


	public function renderEdit()
	{
		$this->template->object = $this->object;
		$this->template->adminAllInTabs = $this->configService->getParam('adminAllInTabs');
	}


	/**
	 * Edit form factory.
	 * @return \Nette\Application\UI\Form
	 */
	protected function createComponentEditForm()
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$objectId = isset($this->object->id) ? $this->object->id : '';

		$form->addHidden('id', $objectId);

		if ($this->isDeveloper()) {
			$form->addText('name', 'label_name')->setRequired();
			$form->addText('langCode', 'label_langCode')->setRequired();
			$form->addText('domain', 'label_domain');
			$form->addText('urlPrefix', 'label_urlPrefix');
			$form->addInteger('rootId', 'label_rootId');
			$form->addInteger('hidePageId', 'label_hidePageId');
			$form->addCheckbox('isEshop', 'label_isEshop');
			$form->addCheckbox('isFilterOff', 'label_isFilterOff');
			$form->addCheckbox('subscriptionAllowed', 'label_subscriptionAllowed');
		}
		$form->addText('adminEmail', 'label_adminEmail');
		$form->addText('adminEmailName', 'label_adminEmailName');
		$form->addText('contactEmail', 'label_contactEmail');
		$form->addText('contactEmailName', 'label_contactEmailName');
		$form->addText('breedingEmail', 'label_breedingEmail');
		$form->addText('breedingEmailName', 'label_breedingEmailName');
		$form->addText('orderEmail', 'label_orderEmail');
		$form->addText('orderEmailName', 'label_orderEmailName');
		$form->addText('favouritePointsSort', 'label_favouritePointsSort');
		$form->addText('animalPointsSort', 'label_animalPointsSort');
		$form->addText('actionPointsSort', 'label_actionPointsSort');

		$sql = "SELECT nicename, iso3
				FROM country
				WHERE iso3 IS NOT NULL
				ORDER BY iso3 = 'CZE' DESC, iso3 = 'SVK' DESC, iso3 = 'DEU' DESC, iso3 = 'AUT' DESC";
		$_states = $this->db->query($sql)->fetchAll();

		$states = ['' => ''];
		foreach ($_states as $s) {
			$states[$s->iso3] = $s->nicename;
		}
		$form->addSelect('countryDefault', 'label_countryDefault', $states)->setPrompt("-");
		$form->addMultiSelect('countryList', 'label_countryList', $states);


		if ($objectId) {
			$this->object = $this->orm->mutation->getById($objectId);
			$form->setDefaults($this->object->toArray());
		}

		$form->addSubmit('save', 'Save');

		$form->onError[] = [$this, 'editFormError'];
		$form->onSuccess[] = [$this, 'editFormSucceeded'];

		return $form;
	}


	public function editFormError($form)
	{
		$this->redrawControl();
	}


	public function editFormSucceeded(UI\Form $form, $values)
	{
		$httpValues = $form->getHttpData();

		try {
			if ($id = (int)$values['id']) {
				if ($this->mutationModel->save($this->object, $values, $this->userEntity) !== false) {
					$this->flashMessage('OK', 'ok');

					if (isset($httpValues['saveBack'])) {
						$this->redirect('Mutation:default');
					}

					if (!$this->isAjax()) {
						$this->redirect('this');
					} else {
						$this->redrawControl();
					}
				} else {
					$this->flashMessage('Error', 'error');
				}
			} else {
				if ($id = $this->newsletterService->add($values, $httpValues)) {
					$this->flashMessage('OK', 'ok');
					$this->redirect('edit', $id);
				} else {
					$this->flashMessage('Error', 'error');
				}
			}
		} catch (\DibiDriverException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}
	}



	/**
	 * Edit form factory.
	 * @return \Nette\Application\UI\Form
	 */
	protected function createComponentEditEmailForm()
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$objectId = isset($this->object->id) ? $this->object->id : '';

		$form->addHidden('id', $objectId);
		$form->addText('email', 'email')
			->addRule(UI\Form::EMAIL)
			->setType('email')
			->setRequired();

//		$form->addText("firstname", "firstname");
//		$form->addText("lastname", "lastname");
//		$form->addSelect("interest", "interest", $this->configService->params['interest']);

		if ($objectId) {
			$this->object = $this->newsletterService->fetchEmail($objectId);
			$form->setDefaults($this->object);
		}

		$form->addSubmit('save', 'Save');

		$form->onError[] = [$this, 'editEmailFormError'];
		$form->onSuccess[] = [$this, 'editEmailFormSucceeded'];

		return $form;
	}


	public function editEmailFormError($form)
	{
		$this->redrawControl();
	}


	public function editEmailFormSucceeded($form)
	{
		$values = $form->getValues();

		try {
			if ($id = (int)$values['id']) {
				if ($this->newsletterService->saveEmail($values) !== false) {
					$this->flashMessage('OK', 'ok');

					if (!$this->isAjax()) {
//						$this->redirect('this', $id);
						$this->redirect('emails');
					} else {
						$this->redrawControl();
						$this->redirect('emails');
					}
				} else {
					$this->flashMessage('Error', 'error');
				}
			} else {
				try {
					if ($id = $this->newsletterService->addEmail($values)) {
						$this->flashMessage('OK', 'ok');
//					    $this->redirect('editEmail', $id);
						$this->redirect('emails');
					} else {
						$this->flashMessage('Error', 'error');
					}
				}
				catch (\SuperKoderi\UserException $e) {
					$this->flashMessage('E-mail already exists', 'error');
				}
			}
		} catch (\DibiDriverException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}
	}

	protected function createComponentFlagsForm()
	{
		return $this->flagsFormFactory->create($this->userEntity, $this->object);
	}

	protected function createComponentSynonymsForm()
	{
		return $this->synonymsFormFactory->create($this->userEntity, $this->object);
	}

	protected function createComponentSettingsForm(): SettingsForm
	{
		return $this->settingsFormFactory->create($this->userEntity, $this->object);
	}

}
