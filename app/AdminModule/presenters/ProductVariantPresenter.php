<?php declare(strict_types = 1);

namespace AdminModule;

use App\Model\CustomField\CustomFields;
use App\Model\ElasticSearch\Product\Convertor\PriceData;
use App\Model\ElasticSearch\Product\Facade;
use App\Model\FileModel;
use App\Model\Orm;
use App\Model\ProductDiscountModel;
use App\Model\ProductModel;
use App\Model\ProductVariant;
use App\Model\ProductVariantModel;
use App\Model\UserModel;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Application\UI\Form;
use Nette\Http\FileUpload;
use Nette\Utils\Json;
use Nextras\Orm\Exception\NoResultException;
use SuperKoderi\StringHelper;
use Throwable;
use Tracy;
use function abs;
use function floatval;
use function in_array;
use function min;

/**
 * @property ProductVariant $object
 */
class ProductVariantPresenter extends BasePresenter
{

	/** @inject */
	public Orm $orm;

	/** @inject */
	public ProductDiscountModel $productDiscountModel;

	/** @inject */
	public ProductVariantModel $productVariantModel;

	/** @inject */
	public ProductModel $productModel;

	/** @inject */
	public UserModel $userModel;

	/** @inject */
	public FileModel $fileModel;

	/** @inject */
	public CustomFields $customFields;

	/** @inject */
	public Facade $elasticProductFacade;

	public function startup(): void
	{
		parent::startup();
	}

	public function actionEdit(int $id): void
	{
		$this->object = $this->getProductVariant($id);

		$this->template->variant = $this->object;
		$this->template->flags = $this->orm->flag->findBy(['mutation' => $this->object->product->mutation, 'type' => 'label'])->orderBy('sort');
		$this->template->groups = $this->orm->userGroup->findBy(['mutation' => $this->object->product->mutation]);
		$this->template->presents = $this->object->presentsAll;
	}

	public function actionEditCF(int $id): void
	{
		$this->object = $this->getProductVariant($id);

		$this->template->fileUploadLink = $this->presenter->link('uploadCf!', ['id' => $this->object->id]);
		$this->template->variant = $this->object;
	}

	public function actionPriceHistory(int $id): void
	{
		$this->object = $this->getProductVariant($id);

		$this->template->variant = $this->object;
		$userGroups = $this->orm->userGroup->findBy(['mutation' => $this->object->product->mutation]);

		$prices = [];
		foreach ($userGroups as $ug) {
			$prices[$ug->id] = $this->orm->productVariantPriceLog
				->findBy(['productVariant' => $this->object, 'groupKey' => $ug->id])
				->orderBy('id');
		}

		$this->template->userGroups = $userGroups;
		$this->template->prices = $prices;
	}

	protected function getProductVariant(int $id): ProductVariant
	{
		try {
			return $this->orm->productVariant->getByIdChecked($id);
		} catch (Throwable) {
			$this->redirect('default');
		}
	}


	/**
	 * Edit form factory.
	 */
	protected function createComponentEditForm(): Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addHidden('id', isset($this->object->id));

		$form->addSubmit('save', 'save_button');

		$form->onError[] = [$this, 'editFormError'];
		$form->onSuccess[] = [$this, 'editFormSucceeded'];

		return $form;
	}

	public function editFormError(UI\Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editFormSucceeded(UI\Form $form): void
	{
		$data = $form->getHttpData();

		try {
			if (isset($data['value'])) {
				foreach ($this->object->discounts as $discount) {
					if (!isset($data['value'][$discount->id])) {
						$this->productDiscountModel->delete($discount);
					}
				}

				foreach ($data['value'] as $key => $name) {
					$valuesAll = [
						'userGroup' => (int) $data['group'][$key],
						'flag' => StringHelper::intTonull($data['flagId'][$key]),
						'flagTop' => StringHelper::intTonull($data['flagTopId'][$key]),
						'percent' => min(abs(floatval($data['value'][$key])), 100),
						'active' => 1,
						'type' => 'general',
						'saveBack' => $data['saveBack'] ?? null,
					];

					$valuesAll['from'] = StringHelper::stringTonull($data['from'][$key]);
					$valuesAll['to'] = StringHelper::stringTonull($data['to'][$key]);

					$stringKey = (string) $key;
					$stringDataItemsIds = [];
					if (isset($data['itemIds'])) {
						foreach ($data['itemIds'] as $id) {
							$newId = (string) $id;
							$stringDataItemsIds[$newId] = $newId;
						}
					}

					if (in_array($stringKey, $stringDataItemsIds, true)) {
						$discount = $this->orm->discount->getById($key);
						$this->productDiscountModel->save($valuesAll, $discount);

					} else {
						$valuesAll['variant'] = $this->object;
						$this->productDiscountModel->save($valuesAll);
					}
				}

				if ($this->object->product->mutation->isEshop) {
					$gKeys = $this->userModel->getAllPossibleGroupAndCombination($this->object->product->mutation);
					$this->productVariantModel->setBestPricesPerGroups($this->object, $gKeys);
					$this->elasticProductFacade->update($this->object->product, $this->object->product->mutation, [PriceData::class]);
				}
			} else {
				$isDelete = false;
				foreach ($this->object->discounts as $discount) {
					$this->productDiscountModel->delete($discount);
					$isDelete = true;
				}

				if ($isDelete && isset($this->object) && $this->object->product->mutation->isEshop) {
					bd('isDelete');
					$gKeys = $this->userModel->getAllPossibleGroupAndCombination($this->object->product->mutation);
					$this->productVariantModel->setBestPricesPerGroups($this->object, $gKeys);
					$this->elasticProductFacade->update($this->object->product, $this->object->product->mutation, [PriceData::class]);
				}
			}

			// save presents
			$this->productVariantModel->savePresents($this->object, $data);

			$this->flashMessage('OK', 'ok');

			$this->productModel->updateProduct($this->object->product);

			if (isset($valuesAll['saveBack'])) {
				$this->redirect('Product:edit', $this->object->product->id);
			}

			if ($this->isAjax()) {
				$this->template->object = $this->object;
				$this->template->presents = $this->object->presentsAll;

				$this->redrawControl();
			} else {
				$this->redirect('ProductVariant:edit', $this->object->id);
			}
		} catch (AbortException $e) {
			throw $e;
		} catch (Throwable $e) {
			Tracy\Debugger::log($e, Tracy\ILogger::ERROR);
			$this->flashMessage('Error', 'error');
		}
	}

	/**
	 * Edit CF form factory.
	 */
	protected function createComponentEditCFForm(): Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addHidden('id', $this->object->id);
		$form->addSubmit('save', 'save_button');

		$form->onError[] = [$this, 'editCFFormError'];
		$form->onSuccess[] = [$this, 'editCFFormSucceeded'];

		return $form;
	}

	public function editCFFormError(UI\Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editCFFormSucceeded(UI\Form $form): void
	{
		$data = $form->getHttpData();

		try {
			if (isset($data['id'])) {
				$variant = $this->orm->productVariant->getByIdChecked($data['id']);

				if (isset($data['customFields'])) {
					$variant->cf = $this->customFields->prepareDataToSave($data['customFields']);
				}

				$this->orm->persistAndFlush($variant);

				$this->flashMessage('Saved successfully', 'ok');
			} else {
				throw new NoResultException();
			}
		} catch (NoResultException) {
			$this->flashMessage('This variant does not exits.', 'error');
		} catch (Throwable $e) {
			Tracy\Debugger::log($e, Tracy\ILogger::ERROR);
			$this->flashMessage('Error', 'error');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		} else {
			$this->redirect('ProductVariant:edit', $this->object->id);
		}
	}

	public function handleUploadCf(FileUpload|null $file = null): never
	{
		$file ??= new FileUpload($_FILES['file']);

		$fileEntity = $this->fileModel->add($file);

		echo Json::encode([
			'id' => $fileEntity->id,
		]);
		$this->terminate();
	}

}
