{block #content}

<div class="box-title">
	<p class="r">
		<a n:href="export" class="btn btn-icon-before">
			<span><span class="icon icon-list"></span> {_"Exports"}</span>
		</a>
		<a n:href="statisticsByUser" class="btn btn-icon-before">
			<span><span class="icon icon-meter2"></span> {_"StatisticsByUser"}</span>
		</a>
	</p>
	<h1><a n:tag-ifset="$email" n:href="default">{_"Orders"}</a>{ifset $email} ({$email}){/ifset}</h1>
</div>
<br/>

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}
{form filterForm class => "form-filter"}
	<div class="grid-row">
		<p class="grid-1-5" n:if="!$object">
			{label mutation /}
			<span class="inp-fix  inp-fix-select">
					{input mutation class => 'inp-text'}
				</span>
		</p>
		<p class="grid-2-3">
			{label status /}<br/>
			{*<label for="status-all">{_all}</label>*}
			{*<input type="checkbox" id="status-all" name="status[]" value="" />&nbsp;&nbsp;&nbsp;*}
			{foreach $form['status']->items as $statusKey => $statusName}
				<input type="checkbox" name="status[]"
					   {if in_array($statusKey,$form['status']->value)}checked=checked{/if}
					   id="status-{$statusKey}" value="{$statusKey}"/>
				<label class="{$statusKey}" for="status-{$statusKey}">{_$statusName}</label>
				&nbsp;
			{/foreach}
		</p>
	</div>

	<div class="grid-row">
		<p class="grid-1-5">
			{label fulltext /}<br/>
			<span class="inp-fix">
				{input fulltext class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label parcelNumber /}<br/>
			<span class="inp-fix">
				{input parcelNumber class => 'inp-text w-full'}
			</span>
		</p>
	</div>
	<div class="grid-row">
		<p class="grid-1-5">
			{label firstname /}<br/>
			<span class="inp-fix">
				{input firstname class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label lastname /}<br/>
			<span class="inp-fix">
				{input lastname class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label orderEmail /}<br/>
			<span class="inp-fix">
				{input orderEmail class => 'inp-text w-full'}
			</span>
		</p>
	</div>
	<div class="grid-row">
		<p class="grid-1-5">
			{label from /}<br/>
			<span class="inp-fix inp-icon-after">
				<span class="icon icon-calendar"></span>
				{input from class => 'inp-text w-full inp-datetime-entry2'}
			</span>
		</p>
		<p class="grid-1-5">
			{label to /}<br/>
			<span class="inp-fix inp-icon-after">
				<span class="icon icon-calendar"></span>
				{input to class => 'inp-text w-full inp-datetime-entry2'}
			</span>
		</p>

		<p class="grid-1-5">
			{label filterBy /}<br/>
			<span class="inp-fix inp-fix-select">
				{input filterBy class => 'inp-text'}
			</span>
		</p>

		<p class="grid-1-5">
			<br>
			<button class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_filter_button}</span>
			</button>

			{if $filterSession->data}
				<a n:href="clearFilter" class="btn btn-red btn-icon-before">
					<span><span class="icon icon-close"></span> {_filter_cancel_button}</span>
				</a>
			{/if}
		</p>
	</div>
	<div class="grid-row">
		<p class="grid-1-5">
			{label novikoId /}<br/>
			<span class="inp-fix">
				{input novikoId class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label novikoStatus /}<br/>
			<span class="inp-fix inp-fix-select">
				{input novikoStatus class => 'inp-text'}
			</span>
		</p>
		<p class="grid-1-5">
			{label  orderItemType /}<br/>
			<span class="inp-fix inp-fix-select">
				{input orderItemType class => 'inp-text'}
			</span>
		</p>
	</div>

	<p class="bold">
		{_filtered_export}:
	</p>
	<div class="grid-row">
		<p class="grid-1-1">
			{if $allowExport}
				<a {if count($orders) > 0}href="{plink export!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($orders) === 0 ? btn-disabled">
					<span><span class="icon icon-rocket"></span> {_invoice_export_button}</span>
				</a>
				<a {if count($orders) > 0}href="{plink exportCreditNotes!}"{/if} n:class="btn, btn-orange, btn-icon-before, count($orders) === 0 ? btn-disabled">
					<span><span class="icon icon-rocket"></span> {_credit_note_export_button}</span>
				</a>
			{/if}
			<a n:ifset="$invoiceExportStatus" n:href="this" n:class="btn, btn-blue, btn-icon-before"><span><span class="icon icon-download"></span>{$invoiceExportStatus}</span></a>
			<a n:ifset="$invoiceExportUrl" n:class="btn, btn-blue, btn-icon-before, count($orders) === 0 ? btn-disabled" href="{$invoiceExportUrl}"><span><span class="icon icon-download"></span>{_invoice_export_link}</span></a>
				<a {if count($orders) > 0}href="{plink invoiceXMLs!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($orders) === 0 ? btn-disabled">
					<span><span class="icon icon-rocket"></span> {_invoice_export_xml_button}</span>
				</a>
				<a {if count($orders) > 0}href="{plink creditNoteXMLs!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($orders) === 0 ? btn-disabled">
					<span><span class="icon icon-rocket"></span> {_credit_note_export_xml_button}</span>
				</a>
		</p>
		<p class="grid-1-1">
			{if !isset($statistics)}
				<a {if count($orders) > 0}href="{plink statistics!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($orders) === 0 ? btn-disabled">
					<span><span class="icon icon-eye"></span> {_order_statistics_show}</span>
				</a>
			{/if}
		</p>
	</div>
	<div class="">
		{include './statistics.latte'}
	</div>
{/form}

<table>
	<thead>
	<tr>
		<th class="status"></th>
		<th>{_"Order Id"}</th>
		<th>{_"product_mutation"}</th>
		<th>{_"Date"}</th>
		<th>{_"paymentType"}</th>
		<th>{_"User Name"}</th>
		<th>{_"filter_noviko_id"}</th>
		<th>{_"filter_noviko_status"}</th>
		<th>{_"filter_order_type"}</th>
		<th>{_"price_vat"}</th>
		<th></th>
	</tr>
	</thead>
	<tbody>
	{foreach $orders as $p}
		{do SuperKoderi\Templating\Helpers::$mutation = $p->mutation}
		<tr class="clickable"{if $p->isDeleted} style="background-color: #d6d6d6"{/if}>
			<td class="status {$p->status}"></td>
			<td><a n:href="detail $p->id">{$p->number}</a>
				<span n:if="$p->emailConfirmationFailed" style="color: red" class="icon Icon icon-mail" title="Email confirmation failed">
				</span>
			</td>
			<td>{$p->mutation->name}</td>
			<td>{$p->created|date:"d. m. Y H:i"}</td>
			{var $paymentTypeText = $p->paymentName}
			{if App\Model\Order::isOnlinePayment($p->paymentType) && $p->subPaymentType}
				{var $paymentTypeText = 'payment_name_' .$p->subPaymentType}
			{/if}
			<td>{_$paymentTypeText}</td>
			<td>{$p->name}</td>
			<td>{$p->novikoId}</td>
			<td>{if $p->novikoStatus !== null}{_$p->novikoStatusName} ({$p->novikoStatus}){/if}</td>
			<td>
				{if $p->isSubscriptionOrder}{_order_type_subscription}{else}{_order_type_product}{/if}
			</td>
			<td>
				{$p->totalPriceDPH|priceFormat}
			</td>
			<td class="right"><a n:href="detail $p->id" class="icon icon-pencil"><span
							class="vhide">{_"edit"}</span></a></td>
		</tr>
	{/foreach}
	</tbody>
</table>

<div class="paging">
	<p class="l">
		{control pager:admin, showPages => "true"}
	</p>
	<p class="r">
		{control pager:admin, class => "r"}
	</p>
</div>
