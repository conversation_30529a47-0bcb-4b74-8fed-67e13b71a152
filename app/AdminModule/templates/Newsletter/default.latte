{block #content}

<div class="box-title">
	<p class="r">
		<a n:href="Newsletter:emails" class="btn">
			<span><span class="icon"></span>{_"Newsletter Emails"}</span>
		</a>
		<a n:href="Newsletter:add" class="btn btn-icon-before">
			<span><span class="icon icon-plus"></span> {_"New newsletter"}</span>
		</a>
	</p>
	<h1>{_"Newsletter"}</h1>
</div>

{if $newslConcept->count}
<h2>{_newsletter_concept}</h2>
<table>
	<thead>
		<tr>
			<th>{_"id"}</th>
			<th>{_"date_created"}</th>
			<th>{_"name"}</th>
			<th></th>
		</tr>
	</thead>
	<tbody>
	{foreach $newslConcept->data as $u}
		<tr class="clickable">
			<td>{$u->id}</td>
			<td>{$u->createdTime|niceDate:$adminLang}</td>
			<td>{$u->name}</td>
			<td><a n:href="edit $u->id" class="icon icon-pencil"><span class="vhide">{_"edit"}</span></a></td>
		</tr>
	{/foreach}
	</tbody>
</table>
{/if}

{if $newslSending->count}
<h2>{_newsletter_sending}</h2>
<table>
	<thead>
	<tr>
		<th>{_"id"}</th>
		<th>{_"date_sending"}</th>
		<th>{_"name"}</th>
		<th>{_"all_recipients_count"}</th>
		<th>{_"sent_recipients_count"}</th>
		<th></th>
	</tr>
	</thead>
	<tbody>
	{foreach $newslSending->data as $u}
		<tr class="clickable">
			<td>{$u->id}</td>
			<td>{$u->sendingTime|niceDate:$adminLang}</td>
			<td>{$u->name}</td>
			<td>{$u->countRecipients()}</td>
			<td>{$u->countSentRecipients()}</td>
			<td><a n:href="edit $u->id" class="icon icon-pencil"><span class="vhide">{_"edit"}</span></a></td>
		</tr>
	{/foreach}
	</tbody>
</table>
{/if}

{if $newslFinished->count}
<h2>{_newsletter_finished}</h2>
<table>
	<thead>
	<tr>
		<th>{_"id"}</th>
		<th>{_"date_sent"}</th>
		<th>{_"name"}</th>
		<th>{_"unsubscribed_count"}</th>
		<th>{_"sent_recipients_count"}</th>
		<th></th>
	</tr>
	</thead>
	<tbody>
	{foreach $newslFinished->data as $u}
		<tr class="clickable">
			<td>{$u->id}</td>
			<td>{$u->sentTime|niceDate:$adminLang}</td>
			<td>{$u->name}</td>
			<td>{$u->countUnsubscribed()}</td>
			<td>{$u->countRecipients()}</td>
			<td><a n:href="edit $u->id" class="icon icon-pencil"><span class="vhide">{_"edit"}</span></a></td>
		</tr>
	{/foreach}
	</tbody>
</table>
{/if}
