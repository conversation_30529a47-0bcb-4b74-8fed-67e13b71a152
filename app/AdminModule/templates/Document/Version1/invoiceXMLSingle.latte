{*
	! DO NOT EDIT THIS LATTE !
	-> IF client requests editation of then CREATE a NEW VERSION
	-> IF you are missing some variable then EDIT .../Document/[DocumentType]Structure/[DocumentType][Version].php file

	- this file is a document version
	- template to be filled wiith document data from db

	These variable types are not real
	- in fact simplified data structures are used
	- but for code completion & developement are useful
*}
{varType App\Model\Order $order}
{var $roundPositionsExtended = $order->mutation->roundPositionsExtended}
<doklad>
	<kniha>FV</kniha>
	<rada>{if $order->mutation->langCode == 'pl'}17{else}15{/if}</rada>
	<typ_dokladu>10</typ_dokladu>
	<k_fakture_vs></k_fakture_vs>
	<var_symbol>{$order->number}</var_symbol>
	<ext_cislo>{$order->invoiceNumber}</ext_cislo>
	<objednavka>{$order->number}</objednavka>
	<dat_vyst>{$order->invoiceDate|date:'%d.%m.%Y'}</dat_vyst>
	<dat_zd_pln>{$order->invoiceDate|date:'%d.%m.%Y'}</dat_zd_pln>
	<dat_spl>{$order->dueDate|date:'%d.%m.%Y'}</dat_spl>
	<dat_kurz>{$order->invoiceDate|date:'%d.%m.%Y'}</dat_kurz>
	<kurz>1</kurz>
	<mnozstvi>1</mnozstvi>
	<id_meny>{$order->mutation->currency ?? ""}</id_meny>
	<forma_uhrady>{if $order->paymentType == App\Model\Order::PAYMENT_ONDELIVERY}DO{else}PKO{/if}</forma_uhrady>
	<id_partnera></id_partnera>
	<nazev>{$order->firstname.' '.$order->lastname}</nazev>
	<ulice>{$order->street}</ulice>
	<psc>{$order->zip}</psc>
	<obec>{$order->city}</obec>
	<stat>{$order->mutation->langMenu ?? ""}</stat>
	<email>{$order->email}</email>
	<telefon>{$order->phone}</telefon>
	<ucet>311/100</ucet>
	<stredisko>19</stredisko>
	<typ_zakazky>VET</typ_zakazky>
	<zakazka></zakazka>
	<cinnost>CAL</cinnost>
	<dealer></dealer>
	<text></text>
	<polozky>
{*		GROUPING ITEMS BY CLIENT ASSIGNMENT	*}
		{var $prices = [
			'totalPrice' => 0,
			'totalPriceDPH' => 0,
			'totalDPH' => 0,
		]}
		{var $invoiceItems = [
			'transportAndPayment' => $prices,
		]}

		{foreach $order->itemsForInvoice as $item}
			{varType App\Model\OrderItem $item}
			{if $item->isTypeTransport || $item->isTypePayment}
				{if !isset($invoiceItems['transportAndPayment']['vat'])}
					{var $invoiceItems['transportAndPayment']['vat'] = $item->vat}
				{/if}
				{var $invoiceItems['transportAndPayment']['totalPrice'] = $invoiceItems['transportAndPayment']['totalPrice'] + round($item->totalPrice, $roundPositionsExtended)}
				{var $invoiceItems['transportAndPayment']['totalPriceDPH'] = $invoiceItems['transportAndPayment']['totalPriceDPH'] + round($item->totalPriceDPH, $roundPositionsExtended)}
				{var $invoiceItems['transportAndPayment']['totalDPH'] = $invoiceItems['transportAndPayment']['totalDPH'] + round($item->totalDPH, $roundPositionsExtended)}
			{else}
				{if !isset($invoiceItems[(string)$item->vat])}
					{var $invoiceItems[(string)$item->vat] = $prices}
				{/if}
				{var $invoiceItems[(string)$item->vat]['totalPrice'] = $invoiceItems[(string)$item->vat]['totalPrice'] +  round($item->totalPrice, $roundPositionsExtended)}
				{var $invoiceItems[(string)$item->vat]['totalPriceDPH'] = $invoiceItems[(string)$item->vat]['totalPriceDPH'] + round($item->totalPriceDPH, $roundPositionsExtended)}
				{var $invoiceItems[(string)$item->vat]['totalDPH'] = $invoiceItems[(string)$item->vat]['totalDPH'] + round($item->totalDPH, $roundPositionsExtended)}
			{/if}
		{/foreach}

		{var $ok = ksort($invoiceItems, SORT_STRING)}

		{var $rowCounter = 1}
		{foreach $invoiceItems as $itemGroupKey => $itemGroup}
			{continueIf $itemGroup['totalPriceDPH'] == 0}
			<polozka>
				<nomenklatura></nomenklatura>
				<nazev>Řádek {$rowCounter}</nazev>
				<sazba_dph>{if $itemGroupKey == 'transportAndPayment'}{sprintf('%02d', (int) $itemGroup['vat'])}{else}{sprintf('%02d', (int) $itemGroupKey)}{/if}</sazba_dph>
				<kod_dph>PTN{if $itemGroupKey == 'transportAndPayment'}{sprintf('%02d', (int) $itemGroup['vat'])}{else}{sprintf('%02d', (int) $itemGroupKey)}{/if}{if $order->mutation->langCode == 'pl'}PL{/if}</kod_dph>
				<mnozstvi_vyd>1</mnozstvi_vyd>
				<cena_cenik>{(round($itemGroup['totalPrice'], $roundPositionsExtended))}</cena_cenik>
				<cena_celkem>{(round($itemGroup['totalPrice'], $roundPositionsExtended))}</cena_celkem>
				<castka_dph>{round($itemGroup['totalDPH'], $roundPositionsExtended)}</castka_dph>
				<strana>1</strana>
				<ucet>{if $itemGroupKey == 'transportAndPayment'}602/120{else}604/120{/if}</ucet>
				<stredisko>1112</stredisko>
				<typ_zakazky>VET</typ_zakazky>
				<zakazka>{if $itemGroupKey == 'transportAndPayment'}15{elseif ((int)$itemGroupKey) == 15}12{elseif ((int)$itemGroupKey) == 21}13{/if}</zakazka>
				<cinnost>CAL</cinnost>
				<dealer></dealer>
				<text></text>
			</polozka>
			{var $rowCounter = $rowCounter + 1}
		{/foreach}
{*		{print_r($invoiceItems)}*}
	</polozky>
</doklad>


