{default $variant = false}
{default $rowKey = 'XXX'}
{var $baseKey =  'variants_rows'}
{if $variant && isset($variant->product->mutation)}
	{do SuperKoderi\Templating\Helpers::$mutation = $variant->product->mutation}
{/if}

<li>
	<div class="inner">
		<span class="drag-area js-handle"></span>
		<div class="grid-row">
			<p class="grid-2-5">
				{var $name = $baseKey . '[rowKey][]'}
				<input name="{$name}" type="hidden" value="{$rowKey}">

				{var $name = $baseKey . '[id]['.$rowKey.']'}
				{if isset($variant->id)}
					<input name="{$name}" type="hidden" value="{if $variant}{$variant->id}{/if}">
				{/if}

				{var $name = $baseKey . '[nameDefault]['.$rowKey.']'}
				<label for="{$name}">{_"Variant name"}</label>
				<span class="inp-fix">
					<input name="{$name}" class="inp-text w-full" type="text"
						   value="{if $variant}{$variant->nameDefault}{/if}"/>
				</span>
			</p>
			<p class="grid-1-5">
				{if $variant}
					{if $canEditWeight}
						{var $name = $baseKey . '[nameWeight]['.$rowKey.']'}
						<label for="{$name}">{_"Short name"}</label>
						<span class="inp-fix">
						<input name="{$name}" class="inp-text w-full" type="text"
								 value="{if $variant}{$variant->nameWeight}{/if}"/>
						</span>
					{else}
						<br>  {_"Short name"}: <strong>{$variant->nameWeight}</strong>
					{/if}
				{/if}
			</p>
			<p class="grid-1-5">

			</p>
			<p class="grid-1-5">
				<span class="inp-item inp-center">
					{var $name = $baseKey . '[active]['.$rowKey.']'}
					<input id="id_{$name}" name="{$name}" class="w-full" type="checkbox" value="1"
						   {if $variant && $variant->active}checked{/if}/>
					<label for="id_{$name}">{_activeVariant}</label>
				</span>
				<br>
				<span class="inp-item inp-center">
					{var $name = $baseKey . '[isFreeTransport]['.$rowKey.']'}
					<input id="id_{$name}" name="{$name}" class="w-full" type="checkbox" value="1"
						   {if $variant && $variant->isFreeTransport}checked{/if}/>
					<label for="id_{$name}">{_"is_free_delivery"}</label>
				</span>
				<span class="inp-item inp-center">
					{var $name = $baseKey . '[isSubscription]['.$rowKey.']'}
					<input id="id_{$name}" name="{$name}" class="w-full" type="checkbox" value="1"
						   {if $variant && $variant->isSubscription}checked{/if}/>
					<label for="id_{$name}">{_"is_in_subscription"}</label>
				</span>
				<span class="inp-item inp-center">
					{var $name = $baseKey . '[isOversize]['.$rowKey.']'}
					<input id="id_{$name}" name="{$name}" class="w-full" type="checkbox" value="1"
						   {if $variant && $variant->isOversize}checked{/if}/>
					<label for="id_{$name}">{_"is_oversize"}</label>
				</span>
			</p>
		</div>
		<br>
		<div class="grid-row">
			<p class="grid-1-5">
				{*<h3>Noviko</h3>*}
				{if $variant}
					Noviko ID: <strong>{$variant->novikoId}</strong><br>

				{var $name = $baseKey . '[calibraId]['.$rowKey.']'}
				<label for="{$name}">Calibra ID</label>
				<span class="inp-fix">
				<input name="{$name}" id="{$name}" class="inp-text w-full" type="text"
					   value="{if $variant}{$variant->calibraId}{/if}"/>
				</span>
				<br>

				{_ean}: <strong>{$variant->ean}</strong><br>
				{_Stock}: <strong>{$variant->stock}</strong><br>
					{if $variant->novikoEdited}{_product_last_edit}:
						<strong>{$variant->novikoEdited|date:"d. m. Y H:i"}</strong><br>{/if}
				{if $canEditWeight}
					{var $name = $baseKey . '[weight]['.$rowKey.']'}
					<label for="{$name}">{_Weight}  [g]</label>
					<span class="inp-fix">
					<input name="{$name}" id="{$name}" class="inp-text w-full" type="text"
									 value="{$variant->weight}"/>
					</span>
					<br/>
				{else}
					{_Weight} (g): <strong>{$variant->weight}</strong><br>
				{/if}

					{*Název: {$variant->name}<br>*}
					{*Kód dodavatele: {$variant->codeSuplier}<br>*}
					{*Váha: {$variant->nameWeight}<br>*}
				{/if}
			</p>
			<p class="grid-1-5">
				{var $name = $baseKey . '[priceDPH]['.$rowKey.']'}
				<label for="{$name}">{_priceDPH} ({_your_price})</label>
				<span class="inp-fix">
						<input name="{$name}" id="{$name}" class="inp-text w-full" type="text"
							   value="{if $variant}{$variant->priceDPH}{/if}"/>
					</span>
				<br>
				{if $variant}
					{_priceDPH} (Noviko): <strong>{$variant->retailPriceVat|priceFormat}</strong><br>

					{if in_array($variant->product->mutation->id, [App\Model\Mutation::ID_PL_ESHOP, App\Model\Mutation::ID_RO_ESHOP])}
						{var $name = $baseKey . '[vat]['.$rowKey.']'}
						<label for="{$name}">VAT %</label>
						<select name="{$name}" id="{$name}" class="inp-text w-full">
							<option>---</option>
							{foreach App\Model\Mutation::MUTATION_VAT_CHOICE[$variant->product->mutation->id] as $key => $value}
								<option value="{$value}"{if $variant->vat == $key} selected="selected"{/if}>{$value}</option>
							{/foreach}
						</select>
						<br>
						{else}
						VAT: <strong>{$variant->vat} %</strong><br>
					{/if}
				{/if}
			</p>
			<p class="grid-1-5">
				{if $variant}
					{_product_variant_final_price}:<br>
					{foreach $groups as $g}

						<strong>{$variant->getPriceFinalDPHForGroup($g->id)|priceFormat}</strong> ({$g->name})<br>
					{/foreach}
				{/if}
			</p>
			<div class="grid-1-5">
				{if $variant}
					discounts: {$variant->discounts->count()}, presents: {$variant->presentsAll->count()}
					<br><a n:href="ProductVariant:edit, id=>$variant->id">{_product_variant_set_discounts}</a>
					<br><a n:href="ProductVariant:priceHistory, id=>$variant->id">{_product_variant_price_history}</a>
				{/if}
				{if empty($variant) || $variant->isQuantityDiscount}
					{if empty($variant)}
						{var $name = $baseKey . '[qdNovikoID]['.$rowKey.']'}
						<label for="{$name}">{_product_variant_isQuantityDiscount} </label>
						<span class="inp-fix inp-fix-select">
							<select name="{$name}" id="{$name}" class="inp-text w-full">
									<option value="">{_choose}</option>
								{foreach $object->variants as $qaVar}
									{continueIf $qaVar->isQuantityDiscount}
									<option value="{$qaVar->novikoId}">
										{$qaVar->novikoId} ({$qaVar->nameWeight})
									</option>
								{/foreach}
							</select>
						</span>
					{elseif $variant->isQuantityDiscount}
						<br><br>
					{_product_variant_isQuantityDiscount}: yes<br>
					{/if}


					{var $name = $baseKey . '[qdAmount]['.$rowKey.']'}
					<label for="{$name}">{_product_variant_qdAmount}</label>
					<span class="inp-fix">
						<input name="{$name}" class="inp-text w-full" type="text"
							   value="{if $variant}{$variant->qdAmount}{/if}"/>
					</span>
					<span class="inp-item inp-center">
					{var $name = $baseKey . '[useQuantityUserGroupDiscount]['.$rowKey.']'}
					<input id="id_{$name}" name="{$name}" class="w-full" type="checkbox" value="1"
						   {if $variant && $variant->useQuantityUserGroupDiscount}checked{/if}/>
					<label for="id_{$name}">{_product_variant_useQuantityUserGroupDiscount}</label>
					</span>
				{/if}
			</div>
			<p class="grid-1-5">
				{if $variant && $flags && $flags->count()}
					{var $name = $baseKey . '[flags]['.$rowKey.'][]'}
					<label for="{$name}">{_product_flags} </label>
					<span class="inp-fix-select">
						<select multiple="multiple" name="{$name}" id="{$name}" class="inp-text w-full">
							{foreach $flags as $flag}
								<option value="{$flag->id}"{if $variant && $variant->isFlagSet($flag)} selected="selected"{/if}>
									{$flag->name}
								</option>
							{/foreach}
						</select>
					</span>
				{/if}
			</p>
		</div>

		{if $variant && $object && $object->mutation->id == App\Model\Mutation::ID_CS_DEFAULT}
			<div class="grid-row">
				<p class="grid-2-5">
					{var $name = $baseKey . '[compensationId]['.$rowKey.']'}
					<label for="{$name}">{_product_compensation}
						<a n:if="$variant->compensation" n:href="Product:edit id => $variant->compensation->product->id">detail</a></label>
					<span class="inp-fix inp-fix-suggest">
						<input type="text" name="q" class="inp-text inp-suggest"
							   data-suggest="/superadmin/search/variant?mutationId={$variant->product->mutation->id}"
							   data-parent="{$variant->id}"
							   value="{if $variant->compensation}{$variant->compensation->nameDefault}{if !$variant->compensation->active} ({_nonActive}) {elseif !$variant->compensation->product->public} ({_product_isnt_public}){/if}{/if}"/>
						<input type="hidden" name="{$name}]"
							   value="{if $variant->compensation}{$variant->compensation->id}{/if}"
							   class="suggest-value"/>
						</span>
				</p>
				<p class="grid-1-5">
					<br>
					<span n:if="$variant->compensation" class="inp-item inp-center">
					{var $name = $baseKey . '[compensationDelete]['.$rowKey.']'}
					<input id="id_{$name}" name="{$name}" class="w-full" type="checkbox" value="1"/>
					<label for="id_{$name}">{_product_compensation_delete}</label>
					</span>
				</p>
			</div>
		{elseif $variant && $variant->compensation}
			<div class="grid-row">
				<p class="grid-2-5">
					{_product_compensation}<br>{$variant->compensation->nameDefault}
					<a n:if="$variant->compensation" n:href="Product:edit id => $variant->compensation->product->id">detail</a>
				</p>
			</div>
		{/if}

		<div class="grid-row">
			{if !$object->hasShellVariant || ($object->hasShellVariant && !$variant)}
				{if !$variant || $variant->isQuantityDiscount || $variant->product->mutation->id != App\Model\Mutation::ID_CS_DEFAULT}
					<br>
					<p
							class="grid-1-5">
						<a href="#" class="icon icon-close remove"></a>
					</p>
				{/if}
			{/if}
		</div>
		<hr>
		<div class="grid-row">
			{if $variant}
				<p class="grid-3-5" {*n:if="$object && $object->mutation->id == App\Model\Mutation::ID_CS_DEFAULT"*}>
					<a n:href="ProductVariant:editCF, id=>$variant->id">{translate}product_variant_custom_fields{/translate}</a>
				</p>
			{/if}
		</div>
		<hr>
		<div class="grid-row">
			<p class="grid-1" {*n:if="$object && $object->mutation->id == App\Model\Mutation::ID_CS_DEFAULT"*}>
				<label>{translate}newsletterCampaign{/translate} {translate}placeholder{/translate}: </label>
				{if isset($variant->product)} "[#product:{$variant->product->id}-{$variant->id}]" or for 2 products
					"[#products2:{$variant->product->id}-{$variant->id},{$variant->product->id}-{$variant->id}]"{/if}
			</p>
		</div>
		<hr>
	</div>
</li>
