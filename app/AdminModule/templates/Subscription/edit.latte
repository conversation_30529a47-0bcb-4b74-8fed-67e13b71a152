{block #content}

<div class="box-title">
	<p class="r">
		<a n:if="$object->isActive" class="btn btn-" n:href="skipNextOrder! $object->hash" onclick="return confirm('Are you sure you want to skip next order for this subscription?')">
			<span><span class="icon icon-arrow-right "></span> {_'subscription_skip_next_order'}</span>
		</a>
		<a n:if="$object->isActive" class="btn btn-red" n:href="deactivateSubscription! $object->hash" onclick="return confirm('Are you sure you want to deactivate this subscription?')">
			<span><span class="icon icon-cancel-circle "></span> {_'subscription_deactivate'}</span>
		</a>
		<a n:href="default" class="btn btn-icon-before">
			<span><span class="icon icon-arrow-left "></span> {_"Back"}</span>
		</a>
	</p>
	<h1>Subscription detail</h1>
</div>

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}

{php  $pTabs = $config['tabs']['subscriptions']}
<div class="menu-tabs">
	<ul class="reset">
		<li><a href="#tab-default">{_tab_subscription}</a></li>
		<li n:if="in_array('logs', $pTabs)"><a href="#tab-logs">{_tab_logs}</a></li>
		<li n:if="in_array('orders', $pTabs)"><a href="#tab-orders">{_tab_orders}</a></li>
		<li n:if="in_array('settings', $pTabs)"><a href="#tab-settings">{_tab_settings}</a></li>
	</ul>
</div>


<div id="tab-default" class="tab-fragment">
	<div class="grid-row">
		<div class="grid-1">
			{if $object->status == App\Model\Subscription::STATUS_DRAFT}
			SUBSCRIPTION IS DRAFT - DID NOT FINISH ORDER FLOW
			{/if}
			<h2>Subscription details</h2>
			<p>{_'subscription_id'}: <strong>{$object->id}</strong></p>
			<p>{_'subscription_name'}: <strong>{$object->name}</strong></p>
			<p>{_'subscription_user'}: <strong><a n:href="User:edit $object->user->id">{$object->user->email}</a></strong></p>
			<p>{_'subscription_interval'}: <strong>{$object->interval}</strong></p>
			{if $object->status === App\Model\Subscription::STATUS_DEACTIVATED}
				<p>{_'reason_cancel'}: {$cancelReasonTranslate($object->reason)}
					{if $object->otherReason}
						({$object->otherReason})
					{/if}
				</p>
			{elseif $object->nextSubscriptionOrder}
				<h2>Details for next order</h2>
				<p>{_'subscription_order_sequence'}: <strong>{$object->nextSubscriptionOrder->sequence}</strong></p>
				<p>{_'subscription_notification_at'}: <strong>{$object->nextSubscriptionOrder->sendNotificationAfter|date:"d.m.Y H:m"}</strong></p>
				<p>{_'subscription_order_creation_at'}: <strong>{$object->nextSubscriptionOrder->createOrderReservationAfter|date:"d.m.Y H:m"}</strong></p>
				<p>{_'subscription_order_expedition_at'}: <strong>{$object->nextSubscriptionOrder->payOrderAfter|date:"d.m.Y H:m"}</strong></p>
			{/if}
		</div>
	</div>
</div>

<div id="tab-logs" class="tab-fragment">
	<div class="grid-row">
		<div class="grid-1">
			<span n:if="$object->nextSubscriptionOrder && count($object->nextSubscriptionOrder->logs) > 0">
				<h3>Logs</h3>
					<table class="table">
					{foreach $object->nextSubscriptionOrder->logs as $log}
						<tr>
							<td>{$log->type}</td>
							<td>{$log->message}</td>
							<td>{$log->created|date:'d. m. Y H:i:s'}</td>
						</tr>
					{/foreach}
				</table>
				</span>

			<h2>Subscription logs</h2>
			<span n:if="count($object->logs) > 0">
					<table class="table">
					{foreach $object->logs as $log}
						<tr>
							<td>{$log->type}</td>
							<td>{$log->message}</td>
							<td>{$log->created|date:'d. m. Y H:i:s'}</td>
						</tr>
					{/foreach}
				</table>
				</span>

		</div>
	</div>
</div>

<div id="tab-orders" class="tab-fragment">
	<div class="grid-row">
		<div class="grid-1">
			<h2>Orders from subscription</h2>
			<span n:if="$object->subscriptionOrders">
		<table class="table">
			<tr>
				<th>SubscriptionOrder ID</th>
				<th>SubscriptionOrder Sequence</th>
				<th>SubscriptionOrder Status</th>
				<th>Notification sent after</th>
				<th>Notification sent at</th>
				<th>Order reservation after</th>
				<th>Order reservation at</th>
				<th>Order payment after</th>
				<th>Order payment at</th>
				<th>Order Status</th>
				<th>Order number</th>
				<th>Action</th>
			</tr>
						{foreach $object->subscriptionOrders as $objectOrder}
							<tr>
								<td>{$objectOrder->id}</td>
								<td>{$objectOrder->sequence}</td>
								<td>{$objectOrder->status}</td>
								<td>{$objectOrder->sendNotificationAfter|date:'d.m.Y H:i:s'}</td>
								<td>{$objectOrder->notificationSentAt|date:'d.m.Y H:i:s'}</td>
								<td>{$objectOrder->createOrderReservationAfter|date:'d.m.Y H:i:s'}</td>
								<td>{$objectOrder->createOrderReservationAt|date:'d.m.Y H:i:s'}</td>
								<td>{$objectOrder->payOrderAfter|date:'d.m.Y H:i:s'}</td>
								<td>{$objectOrder->orderPaidAt|date:'d.m.Y H:i:s'}</td>
								<td>{if $objectOrder->order}{$objectOrder->order->status}{/if}</td>
								<td>{if $objectOrder->order}<a n:href="Order:detail $objectOrder->order->id">{$objectOrder->order->number}</a>{/if}</td>
								<td n:if="$objectOrder->canBeEdited"><a n:href="editNextOrder hash => $objectOrder->hash">Edit</a></td>
								<td n:if="$objectOrder->status == App\Model\SubscriptionOrder::STATUS_LOW_STOCK"><a n:href="tryToCreateAgain! subscriptionOrderHash => $objectOrder->hash">Try to create again</a></td>
								<td n:if="$objectOrder->status == App\Model\SubscriptionOrder::STATUS_RESERVED"><a onclick="return confirm({_'confirm_subscription_order_payment_now'})" n:href="processPaymentNow! subscriptionOrderHash => $objectOrder->hash">Process payment immediately</a></td>
							</tr>
						{/foreach}
		</table>
				</span>
		</div>
	</div>
</div>

<div id="tab-settings" class="">
	<div class="grid-row">
		<div class="grid-1">
			{if $object->status == App\Model\Subscription::STATUS_DRAFT}
				SUBSCRIPTION IS DRAFT - DID NOT FINISH ORDER FLOW
			{else}
				{control subscriptionForm}
			{/if}
		</div>
	</div>
</div>
