{block #content}

<div class="box-title">
	<p class="r">
	{*	<a n:href="NewsletterCampaign:add" class="btn btn-icon-before">
			<span><span class="icon icon-plus"></span> {_"New newsletter campaign"}</span>
		</a>*}
	</p>
	<h1>{_"Subscription"}</h1>
</div>

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}

{form filterForm class => "form-filter"}
	<div class="grid-row">
		<p class="grid-2-3">
			{label status /}<br/>
			{foreach $form['status']->items as $statusKey => $statusName}
				<input type="checkbox" name="status[]"
					   {if in_array($statusKey,$form['status']->value)}checked=checked{/if}
					   id="status-{$statusKey}" value="{$statusKey}"/>
				<label class="{$statusKey}" for="status-{$statusKey}">{_$statusName}</label>
				&nbsp;
			{/foreach}
		</p>
	</div>

	<div class="grid-row">
		<p class="grid-1-5">
			{label fulltext /}<br/>
			<span class="inp-fix">
				{input fulltext class => 'inp-text w-full'}
			</span>
		</p>
	</div>
	<div class="grid-row">
		<p class="grid-1-5">
			{label paymentType /}<br/>
			<span class="inp-fix">
				{input paymentType class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label subPaymentType /}<br/>
			<span class="inp-fix">
				{input subPaymentType class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label name /}<br/>
			<span class="inp-fix">
				{input name class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label firstname /}<br/>
			<span class="inp-fix">
				{input firstname class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label lastname /}<br/>
			<span class="inp-fix">
				{input lastname class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label email /}<br/>
			<span class="inp-fix">
				{input email class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label hash /}<br/>
			<span class="inp-fix">
				{input hash class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label productVariantId /}<br/>
			<span class="inp-fix">
				{input productVariantId class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label subscriptionMutation /}<br/>
			<span class="inp-fix">
				{input subscriptionMutation class => 'inp-text w-full'}
			</span>
		</p>
	</div>
	<div class="grid-row">
		<p class="grid-1-5">
			<br>
			<button class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_filter_button}</span>
			</button>

			{if $filterSection->data}
				<a n:href="clearFilter" class="btn btn-red btn-icon-before">
					<span><span class="icon icon-close"></span> {_filter_cancel_button}</span>
				</a>
			{/if}
		</p>


	</div>
	<p class="bold">
		{_filtered_export}:
	</p>
	<div class="grid-row">
		<p class="grid-1-1">
			{if isset($filterSection->data->status) && in_array('deactivated', $filterSection->data->status)}
				<a href="{plink exportDeactiveReason!}" n:class="btn, btn-blue, btn-icon-before">
				<span><span class="icon icon-rocket"></span> {_deactive_reason_export}</span>
				</a>

			{/if}
			{*<a {if count($subscriptions) > 0}href="{plink export!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($subscriptions) === 0 ? btn-disabled">
				<span><span class="icon icon-rocket"></span> {_invoice_export_button}</span>
			</a>
			<a n:ifset="$invoiceExportStatus" n:href="this" n:class="btn, btn-blue, btn-icon-before"><span><span class="icon icon-download"></span>{$invoiceExportStatus}</span></a>
			<a n:ifset="$invoiceExportUrl" n:class="btn, btn-blue, btn-icon-before, count($subscriptions) === 0 ? btn-disabled" href="{$invoiceExportUrl}"><span><span class="icon icon-download"></span>{_invoice_export_link}</span></a>
			<a {if count($subscriptions) > 0}href="{plink invoiceXMLs!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($subscriptions) === 0 ? btn-disabled">
				<span><span class="icon icon-rocket"></span> {_invoice_export_xml_button}</span>
			</a>
			<a {if count($subscriptions) > 0}href="{plink creditNoteXMLs!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($subscriptions) === 0 ? btn-disabled">
				<span><span class="icon icon-rocket"></span> {_credit_note_export_xml_button}</span>
			</a>*}
		</p>
		<p class="grid-1-1">
			{*{if !isset($statistics)}
				<a {if count($subscriptions) > 0}href="{plink statistics!}"{/if} n:class="btn, btn-blue, btn-icon-before, count($subscriptions) === 0 ? btn-disabled">
					<span><span class="icon icon-eye"></span> {_order_statistics_show}</span>
				</a>
			{/if}*}
		</p>
	</div>
{/form}

<table>
	<thead>
	<tr>
		<th>{_"ID"}</th>
		<th>{_"Status"}</th>
		<th>{_"Name"}</th>
		<th>{_"Interval"}</th>
		<th>{_"Payment method"}</th>
		<th>{_"Last order value (total price)"}</th>
		<th><a style="color: white;" n:href="this sortColumn => 'so.payOrderAfter', sortDirection => ($sortColumn === 'so.payOrderAfter' && $sortDirection === 'asc' ? 'desc' : 'asc')">{_"Expected expedition date"} {if $sortColumn === 'so.payOrderAfter'}
					{if $sortDirection === 'asc'}&uarr;{else}&darr;{/if}
				{/if}</a></th>
		<th>{_"User email"}</th>
		<th>{_"created"}</th>
		<th></th>
	</tr>
	</thead>
	<tbody>
	{foreach $subscriptions as $p}
		{do SuperKoderi\Templating\Helpers::$mutation = $p->mutation}
		<tr class="clickable">
			<td>{$p->id}</td>
			<td>{$p->status}</td>
			<td><a n:href="edit $p->id">{$p->name}</a></td>
			<td>{$p->interval}</td>
			<td>{$p->paymentType} {if $p->subPaymentType !=='online'}/ {$p->subPaymentType}{/if}</td>
			<td>{$p->lastProcessedOrder??->totalPrice|priceFormat}</td>
			<td>{($p->reservedSubscriptionOrder ? $p->reservedSubscriptionOrder->payOrderAfter : $p->openSubscriptionOrder??->payOrderAfter)|date:"d. m. Y H:i"}</td>
			<td>{$p->user->email}</td>
			<td>{$p->created|date:"d. m. Y H:i"}</td>
			<td class="right"><a n:href="edit $p->id" class="icon icon-pencil"><span
							class="vhide">{_"edit"}</span></a></td>
		</tr>
	{/foreach}
	</tbody>
</table>

<div class="paging">
	<p class="l">
		{control pager:admin, showPages => "true"}
	</p>
	<p class="r">
		{control pager:admin, class => "r"}
	</p>
</div>


{if !$isPartner}
	<h1>Report for next 14 days</h1>
	<table>
		<thead>
			<tr>
				<th>{_"Noviko ID"}</th>
				<th>{_"EAN"}</th>
				<th>{_"Name"}</th>
				<th>{_"Amount"}</th>
				<th>{_"Stock"}</th>
				<th>{_"Subscription IDs"}</th>
			</tr>
		</thead>
		<tbody>
			{foreach $productVariantsReport as $item}
				<tr>
					<td>{$item->novikoId}</td>
					<td>{$item->ean}</td>
					<td>{$item->nameDefault}</td>
					<td>{$item->amount}</td>
					<td>{$item->stock}</td>
					<td>
						{php $ids = explode(',', $item->subscriptionIds)}
						{foreach $ids as $id}
							<a n:href="edit $id" target="_blank">{$id}</a>
						{/foreach}
					</td>
				</tr>
			{/foreach}
		</tbody>
	</table>
{/if}
