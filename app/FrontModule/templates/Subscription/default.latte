{block content}
	<div class="row-main">
		<p n:foreach="$flashes as $flash" class="message message--{$flash->type}">
			{_$flash->message}
		</p>

		<div class="row-main__helper">
			{include '../part/menu/breadcrumb.latte', class=>'u-mb-md'}
			{include '../part/box/annot.latte', class=>'u-mb-xl'}

			{include '../part/box/content.latte'}

			{if $isSubscriptionEnabled}
				{include '../part/crossroad/subscriptions.latte', showLink=>false, showDetail=>true, static=>true}
				<div class="b-alert">
					<div class="b-alert__inner">
						<h2>
							{_'subscription_forget_title'}
						</h2>
						<p>
							{_'subscription_forget_text'|noescape}
						</p>
						<a href="{plink 'UID|eshop'}" class="btn btn--arrow btn--sm">
							<span class="btn__text">
								<span class="item-icon item-icon--after">
									{_'subscription_forget_btn'}
									{('angle-right')|icon}
								</span>
							</span>
						</a>
					</div>
				</div>
				<div id="popup-add-subscription" class="b-subscription-popup u-hide">
					<div class="b-subscription-popup__inner">
						<h2>
							{_'subscription_add_title'}
						</h2>
						<p>
							{_'subscription_add_text'|noescape}
						</p>
						<a href="{plink 'UID|eshop'}" class="btn btn--arrow btn--sm">
							<span class="btn__text">
								<span class="item-icon item-icon--after">
									{_'subscription_add_btn'}
									{('angle-right')|icon}
								</span>
							</span>
						</a>
					</div>
				</div>
			{/if}
		</div>
	</div>
