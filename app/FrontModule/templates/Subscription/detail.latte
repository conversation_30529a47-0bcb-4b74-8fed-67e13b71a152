{default $isOk = true}
{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->nextSubscriptionOrder === null}
	{var $isOk = false}
{/if}

{default $lastOrderIsPaid = true}
{if $subscription->lastProcessedOrder && $subscription->lastProcessedOrder->status === App\Model\Order::STATUS_WAITING}
	{var $lastOrderIsPaid = false}
{/if}

{block content}
	<div class="row-main">
		<p n:foreach="$flashes as $flash" class="message message--{$flash->type}">
			{_$flash->message}
		</p>

		<div class="row-main__helper">
			{include '../part/menu/breadcrumb.latte', class=>'u-mb-md'}
			{include '../part/box/annot.latte', class=>'u-mb-xl'}

			{if $isSubscriptionEnabled && !$isOk}
			<div class="u-max-width--7-12 u-mx-auto u-text-center">
				<h3 class="u-mb-xxs u-color-green">
					{$subscription->name}
				</h3>
				<p class="u-mb-0 u-color-red">
					<b>{_'subscription_has_some_error_contact_admin'}</b>
				</p>
			</div>
			{/if}

			{if $isSubscriptionEnabled && $isOk}
				<div class="u-max-width--7-12 u-mx-auto u-text-center">
					<h3 class="u-mb-xxs u-color-green">
						{$subscription->name}
					</h3>
					{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE}
						<p class="u-mb-0 u-color-green">
							<b>{_'subscription_is_active'}</b>
						</p>
						{if $subscription->nextSubscriptionOrder}
						<p>
							{_'subscription_closest_order'}: {$subscription->nextSubscriptionOrder->payOrderAfter|date}
						</p>
						{/if}
					{elseif $subscription->status === App\Model\Subscription::STATUS_DEACTIVATED}
						<p class="u-mb-xxs">
							<b>{_'subscription_is_inactive'}</b>
						</p>
						<p>
							<a n:href="'UID|subscriptionActivation',  $subscription->hash" class="btn btn--sm">
								<span class="btn__text">
									{_'subscription_activate'}
								</span>
							</a>
						</p>
					{elseif $subscription->status === App\Model\Subscription::STATUS_PENDING}
						<p class="u-mb-xxs">
							<b>{_'subscription_is_inactive'}</b>
						</p>
						<p>
							{_'subscription_awaiting_payment'}
						</p>
					{else}
						<p class="u-mb-xxs">
							<b>{_'subscription_cancelled'}</b>
						</p>
					{/if}
					{if !$lastOrderIsPaid}
						<p class="u-mb-0 u-color-red">
						<p class="u-mb-xxs">
							<b>{_'subscription_last_order_not_paid'}</b>
						</p>
						<p>
							<a n:if="$subscription->lastProcessedOrder->showRetryPayment" href="{plink 'UID|retryOnlinePayment', $subscription->lastProcessedOrder->number, $subscription->lastProcessedOrder->hash, true}" class="btn btn--gray">
								<span class="btn__text">
									{_btn_order_retry_payment}
								</span>
							</a>
						</p>
					{/if}
				</div>

				<div class="b-subscription-tabs js-tabs">
					<div class="b-subscription-tabs__tabs">
						<ul class="b-subscription-tabs__list js-tabs__list">
							<li class="b-subscription-tabs__item js-tabs__item">
								<a href="#list" class="b-subscription-tabs__link js-tabs__link is-active">
									{('document')|icon}
									{_'subscription_product_list'}
								</a>
							</li>
							<li class="b-subscription-tabs__item js-tabs__item" n:if="$lastOrderIsPaid">
								<a href="#settings" class="b-subscription-tabs__link js-tabs__link">
									{('settings')|icon}
									{_'subscription_settings'}
								</a>
							</li>
							<li class="b-subscription-tabs__item js-tabs__item" n:if="$lastOrderIsPaid">
								<a href="#order" class="b-subscription-tabs__link js-tabs__link">
									{('package')|icon}
									{_'subscription_next_order'}
								</a>
							</li>
						</ul>
					</div>
					<div class="b-subscription-tabs__content js-tabs__content">
						<div id="list" class="b-subscription-tabs__fragment js-tabs__fragment is-active">
							{snippet subscriptionProducts}
								{var $subscriptionProducts = $subscription->subscriptionItems}
								{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->nextSubscriptionOrder}
									{var $subscriptionProducts = $subscription->nextSubscriptionOrder->subscriptionItems}
								{/if}
								<p n:foreach="$flashes as $flash" class="message message--{$flash->type}">
									{_$flash->message}
								</p>
								<div class="c-products-row u-mb-xs u-pt-md">
									<h3>{_basket_subscription_repeat_heading}</h3>
									{foreach $subscriptionProducts as $item}
										<div class="c-products-row__item">
											<div class="b-product-row b-product-row--subscription">
												<div class="b-product-row__img-wrap">
													<div class="b-product-row__img img">
														<div class="img__holder">
															{if isset($item->productVariant->images) && $item->productVariant->images->count()}
																{php $img = $imageResizer->getImg($item->productVariant->firstImage->filename, 'sm')}
																<img loading="lazy" src="{$img->src}" alt="" />
															{else}
																<img src="{$basePath}/static/img/illust/noimg.svg" alt="" width="126" height="126" />
															{/if}
														</div>
													</div>
												</div>
												<p class="b-product-row__flags">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend aaa">
															{_product}
														</span>
													{/if}
												</p>
												<div class="b-product-row__content u-last-m0">
													<h2 class="b-product-row__title">
														<a href="{plink $item->productVariant, v=>$item->productVariant->id}" class="b-product-row__link">{$item->productVariant->nameAnchor}</a>
													</h2>
												</div>
												<div class="b-product-row__amount">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend">
															{_amount}
														</span>
													{/if}
													<span class="amount">
														<span class="amount__wrap">
															<span class="amount__action amount__action--minus">
																<a class="js-link-ajax" n:href="decreaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	-
																</a>
															</span>
															<span class="amount__value">
																{$item->amount}
															</span>
															<span class="amount__action amount__action--plus">
																<a class="js-link-ajax" n:href="increaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	+
																</a>
															</span>
														</span>
													</span>
												</div>
												<div class="b-product-row__price">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend b-product-row__legend--price">
															{_price}
														</span>
													{/if}
													{if $item->type === 'SUBSCRIPTION'}
													<s class="price__old">
														{$item->amount * $item->productVariant->basicPrice->priceDPH|priceFormat}
													</s>
														{$item->amount * $item->productVariant->subscriptionPrice->priceDPH|priceFormat}
													{else}
														{$item->amount * $item->productVariant->basicPrice->priceDPH|priceFormat}
													{/if}
												</div>
												<div class="b-product-row__remove">
													<a href="#popup-confirm-subscription" class="u-font-sm" data-fancybox-custom data-fancybox-custom data-id="{$item->id}" data-subscription-hash="{$subscription->hash}">
														{('trash')|icon}
														<span class="u-vhide">
															{_remove}
														</span>
													</a>
												</div>
											</div>
										</div>
									{/foreach}
								</div>
								{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->nextSubscriptionOrder && count($subscription->nextSubscriptionOrder->activeOneTimeItems)}
									<h3>{_basket_subscription_repeat_one_time_heading}</h3>
								<div class="c-products-row u-mb-xs u-pt-md">
									{foreach $subscription->nextSubscriptionOrder->activeOneTimeItems as $item}
										<div class="c-products-row__item">
											<div class="b-product-row b-product-row--subscription">
												<div class="b-product-row__img-wrap">
													<div class="b-product-row__img img">
														<div class="img__holder">
															{if isset($item->productVariant->images) && $item->productVariant->images->count()}
																{php $img = $imageResizer->getImg($item->productVariant->firstImage->filename, 'sm')}
																<img loading="lazy" src="{$img->src}" alt=""  />
															{else}
																<img src="{$basePath}/static/img/illust/noimg.svg" alt="" width="126" height="126" />
															{/if}
														</div>
													</div>
												</div>
												<p class="b-product-row__flags">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend">
															{_product}
														</span>
													{/if}
												</p>
												<div class="b-product-row__content u-last-m0">
													<h2 class="b-product-row__title">
														<a href="{plink $item->productVariant, v=>$item->productVariant->id}" class="b-product-row__link">{$item->productVariant->nameAnchor}</a>
													</h2>
												</div>
												<div class="b-product-row__amount">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend">
															{_amount}
														</span>
													{/if}
													<span class="amount">
														<span class="amount__wrap">
															<span class="amount__action amount__action--minus">
																<a class="js-link-ajax" n:href="decreaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	-
																</a>
															</span>
															<span class="amount__value">
																{$item->amount}
															</span>
															<span class="amount__action amount__action--plus">
																<a class="js-link-ajax" n:href="increaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	+
																</a>
															</span>
														</span>
													</span>
												</div>
												<div class="b-product-row__price">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend b-product-row__legend--price">
															{_price}
														</span>
													{/if}
													{$item->amount * $item->productVariant->basicPrice->priceDPH|priceFormat}
												</div>
												<div class="b-product-row__remove">
													<a href="#popup-confirm-subscription" class="u-font-sm" data-fancybox-custom data-fancybox-custom data-id="{$item->id}" data-next-subscription-order-id="{$subscription->nextSubscriptionOrder->id}" data-subscription-hash="{$subscription->hash}">
														{('trash')|icon}
														<span class="u-vhide">
															{_remove}
														</span>
													</a>
												</div>
											</div>
										</div>
									{/foreach}
								</div>
							{/if}

								<div class="b-summary u-mb-md u-pt-md">
									<div class="b-summary__item">
										<div class="b-summary__label">
											{_'subscription_price_per'}:
										</div>
										<div class="b-summary__value b-summary__value--total">
											{$subscription->totalSubscriptionProducts|priceFormat}
										</div>
									</div>
									<div class="b-summary__item" n:if="$subscription->nextSubscriptionOrder && $subscription->nextSubscriptionOrder->totalOneTimePrice > 0">
										<div class="b-summary__label">
											{_'order_price_per_part_one_time'}:
										</div>
										<div class="b-summary__value b-summary__value--total">
											{$subscription->nextSubscriptionOrder->totalOneTimePrice|priceFormat}
										</div>
									</div>
									<div class="b-summary__item" n:if="$subscription->nextSubscriptionOrder && $subscription->nextSubscriptionOrder->totalOneTimePrice > 0">
										<div class="b-summary__label b-summary__label--total">
											{_'order_price_total'}
										</div>
										<div class="b-summary__value b-summary__value--total">
											{$subscription->nextSubscriptionOrder->totalPrice|priceFormat}
										</div>
									</div>
									<div class="b-summary__item">
										<div class="b-summary__label">
											{_'subscription_spare'}
										</div>
										<div class="b-summary__value">
											{$subscription->saleTotalSubscriptionProducts|priceFormat}
										</div>
									</div>
								</div>
							{/snippet}
							<div class="b-alert u-mb-lg">
								<div class="b-alert__inner">
									<h2>
										{_'subscription_question_title'}
									</h2>
									<p>
										{_'subscription_question_text'|noescape}
									</p>
									<a href="{plink 'UID|eshop'}" class="btn btn--arrow btn--sm">
										<span class="btn__text">
											<span class="item-icon item-icon--after">
												{_'subscription_products_add'}
												{('angle-right')|icon}
											</span>
										</span>
									</a>
								</div>
							</div>
							{include '../part/attached/products.latte', customTitle=>basket_products, class=>'u-mb-md'}
						</div>
						<div id="settings" class="b-subscription-tabs__fragment js-tabs__fragment" n:if="$lastOrderIsPaid">
							{control subscriptionForm}
						</div>
						<div id="order" class="b-subscription-tabs__fragment js-tabs__fragment" n:if="$lastOrderIsPaid">
							<div n:if="$subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->nextSubscriptionOrder" class="b-alert u-mb-lg">
								<div class="b-alert__inner">
									<h2>
										{_'subscription_next_order'}
									</h2>
									{*<p>
										<b>{_'subscription_awaiting_confirmation'}</b>
									</p>*}
									<div class="grid grid--center grid--middle u-mb-sm">
										{* <div class="grid__cell size--auto">
											<a n:if="($subscription->recurringPayment === null)" class="btn btn--sm" n:href="createRecurringPayment! $subscription->hash">
												<span class="btn__text">
													{_'subscription_add_new_card_from_subscription'}
												</span>
											</a>
										</div> *}
										{* <div class="grid__cell size--auto">
											<a n:if="($subscription->recurringPayment !== null)" class="btn btn--sm" n:href="cancelPayments! $subscription->hash">
												<span class="btn__text">
													{_'subscription_remove_card_from_subscription'}
												</span>
											</a>
										</div> *}
										{*<div class="grid__cell size--auto">
											<a href="#" class="btn btn--sm">
												<span class="btn__text">
													{_'subscription_confirm_btn'}
												</span>
											</a>
										</div>*}
										<div class="grid__cell size--auto">
											<a n:href="skipNextOrder! $subscription->hash" class="btn" onclick="return confirm({_'confirm_skip_order'})">
												<span class="btn__text">
													{_'subscription_skip_button'}
												</span>
											</a>
											<a n:href="skipNextOrder! $subscription->hash" class="btn btn--small" onclick="return confirm({_'confirm_skip_order'})">
												<span class="btn__text">
													{_'subscription_skip_button'}
												</span>
											</a>
										</div>
										{capture $tooltipContent}
											<div class="tippy-content__wrapper">
												<p><b>{_'subscription_better_prices_title'}</b></p>
												<div class="tippy-content__inner">
													{('popup-image')|icon}
													<ul>
														<li>{_'subscription_better_prices_item1'}</li>
														<li>{_'subscription_better_prices_item2'}</li>
														<li>{_'subscription_better_prices_item3'}</li>
													</ul>
												</div>
												<p class="u-text-center">
													<a href="{plink 'UID|subscription-landing'}">{_'subscription_better_prices_link'}</a>
												</p>
											</div>
										{/capture}
										<div class="grid__cell size--auto">
											<span class="js-tooltip js-tooltip--top" data-tooltip="{$tooltipContent}">
												{('tooltip')|icon}
											</span>
										</div>
									</div>
									<div class="b-alert__items" n:if="$subscription->reservedSubscriptionOrder">
										<div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_notification'}:
											</div>
											<div class="b-alert__value">
												{$subscription->reservedSubscriptionOrder->sendNotificationAfter|date}
											</div>
										</div>
										<div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_creation'}:
											</div>
											<div class="b-alert__value">
												{$subscription->reservedSubscriptionOrder->createOrderReservationAfter|date}
											</div>
										</div>
										<div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_expedition'}:
											</div>
											<div class="b-alert__value">
												{$subscription->reservedSubscriptionOrder->payOrderAfter|date}
											</div>
										</div>
										{* <div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_you_can_skip_order'}:
											</div>
											<div class="b-alert__value">
												<a onclick="return confirm({_'confirm_skip_order'})" n:href="skipNextOrder! $subscription->nextSubscriptionOrder->hash">{_'subscription_skip_button'}</a>
											</div>
										</div> *}
									</div>
									<div class="b-alert__items" n:if="!$subscription->reservedSubscriptionOrder">
										<div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_notification'}:
											</div>
											<div class="b-alert__value">
												{$subscription->nextSubscriptionOrder->sendNotificationAfter|date}
											</div>
										</div>
										<div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_creation'}:
											</div>
											<div class="b-alert__value">
												{$subscription->nextSubscriptionOrder->createOrderReservationAfter|date}
											</div>
										</div>
										<div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_expedition'}:
											</div>
											<div class="b-alert__value">
												{$subscription->nextSubscriptionOrder->payOrderAfter|date}
											</div>
										</div>
										{* <div class="b-alert__item">
											<div class="b-alert__label">
												{_'subscription_you_can_skip_order'}:
											</div>
											<div class="b-alert__value">
												<a onclick="return confirm({_'confirm_skip_order'})" n:href="skipNextOrder! $subscription->nextSubscriptionOrder->hash">{_'subscription_skip_button'}</a>
											</div>
										</div> *}
									</div>
								</div>
							</div>

							<div n:if="$subscription->status === App\Model\Subscription::STATUS_DEACTIVATED" class="b-alert u-mb-lg">
								<div class="b-alert__inner">
									<h2>
										{_'subscription_next_order'}
									</h2>
									<p>
										<b>{_'subscription_is_deactivated_you_have_no_order_planned'|noescape}</b>
									</p>
									<a n:href="'UID|subscriptionActivation',  $subscription->hash" class="btn btn--sm">
									<span class="btn__text">
											{_'subscription_activate'}
											</span>
									</a>
								</div>
							</div>

							{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->nextSubscriptionOrder}
							<h2 class="u-text-center">
								{_'subscription_recap_next_order_items'}
							</h2>
							{snippet nextOrderSubscriptionProducts}
								{var $subscriptionProducts = $subscription->subscriptionItems}
								{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->nextSubscriptionOrder}
									{var $subscriptionProducts = $subscription->nextSubscriptionOrder->subscriptionItems}
								{/if}
								<h3>{_basket_subscription_repeat_heading}</h3>
								<div class="c-products-row u-mb-xs u-pt-md">
									{foreach $subscriptionProducts as $item}
										<div class="c-products-row__item">
											<div class="b-product-row b-product-row--subscription">
												<div class="b-product-row__img-wrap">
													<div class="b-product-row__img img">
														<div class="img__holder">
															{if isset($item->productVariant->images) && $item->productVariant->images->count()}
																{php $img = $imageResizer->getImg($item->productVariant->firstImage->filename, 'sm')}
																<img loading="lazy" src="{$img->src}" alt="" />
															{else}
																<img src="{$basePath}/static/img/illust/noimg.svg" alt="" width="126" height="126" />
															{/if}
														</div>
													</div>
												</div>
												<p class="b-product-row__flags">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend aaa">
															{_product}
														</span>
													{/if}
												</p>
												<div class="b-product-row__content u-last-m0">
													<h2 class="b-product-row__title">
														<a href="{plink $item->productVariant, v=>$item->productVariant->id}" class="b-product-row__link">{$item->productVariant->nameAnchor}</a>
													</h2>
												</div>
												<div class="b-product-row__amount">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend">
															{_amount}
														</span>
													{/if}
													<span class="amount">
														<span class="amount__wrap">
															<span class="amount__action amount__action--minus">
																<a class="js-link-ajax" n:href="decreaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	-
																</a>
															</span>
															<span class="amount__value">
																{$item->amount}
															</span>
															<span class="amount__action amount__action--plus">
																<a class="js-link-ajax" n:href="increaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	+
																</a>
															</span>
														</span>
													</span>
												</div>
												<div class="b-product-row__price">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend b-product-row__legend--price">
															{_price}
														</span>
													{/if}
													<s class="price__old">
														{$item->amount * $item->productVariant->basicPrice->priceDPH|priceFormat}
													</s>
													{$item->amount * $item->productVariant->subscriptionPrice->priceDPH|priceFormat}
												</div>
												<div class="b-product-row__remove">
													<a href="#popup-confirm-subscription" class="u-font-sm" data-fancybox-custom data-fancybox-custom data-id="{$item->id}" data-subscription-hash="{$subscription->hash}">
														{('trash')|icon}
														<span class="u-vhide">
															{_remove}
														</span>
													</a>
												</div>
											</div>
										</div>
									{/foreach}
								</div>
								<div class="c-products-row u-mb-xs u-pt-md" n:if="$subscription->status === App\Model\Subscription::STATUS_ACTIVE && count($subscription->nextSubscriptionOrder->activeOneTimeItems) > 0">
									<h3>{_basket_subscription_repeat_one_time_heading}</h3>
									{foreach $subscription->nextSubscriptionOrder->activeOneTimeItems as $item}
										<div class="c-products-row__item">
											<div class="b-product-row b-product-row--subscription">
												<div class="b-product-row__img-wrap">
													<div class="b-product-row__img img">
														<div class="img__holder">
															{if isset($item->productVariant->images) && $item->productVariant->images->count()}
																{php $img = $imageResizer->getImg($item->productVariant->firstImage->filename, 'sm')}
																<img loading="lazy" src="{$img->src}" alt=""  />
															{else}
																<img src="{$basePath}/static/img/illust/noimg.svg" alt="" width="126" height="126" />
															{/if}
														</div>
													</div>
												</div>
												<p class="b-product-row__flags">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend">
															{_product}
														</span>
													{/if}
												</p>
												<div class="b-product-row__content u-last-m0">
													<h2 class="b-product-row__title">
														<a href="{plink $item->productVariant, v=>$item->productVariant->id}" class="b-product-row__link">{$item->productVariant->nameAnchor}</a>
													</h2>
												</div>
												<div class="b-product-row__amount">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend">
															{_amount}
														</span>
													{/if}
													<span class="amount">
														<span class="amount__wrap">
															<span class="amount__action amount__action--minus">
																<a class="js-link-ajax" n:href="decreaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	-
																</a>
															</span>
															<span class="amount__value">
																{$item->amount}
															</span>
															<span class="amount__action amount__action--plus">
																<a class="js-link-ajax" n:href="increaseAmountOnItem! $subscription->hash, $item->id, $item->amount">
																	+
																</a>
															</span>
														</span>
													</span>
												</div>
												<div class="b-product-row__price">
													{if $iterator->isFirst()}
														<span class="b-product-row__legend b-product-row__legend--price">
															{_price}
														</span>
													{/if}
													{if $item->type === 'SUBSCRIPTION'}
														<s class="price__old">
															{$item->amount * $item->productVariant->basicPrice->priceDPH|priceFormat}
														</s>
														{$item->amount * $item->productVariant->subscriptionPrice->priceDPH|priceFormat}
													{else}
														{$item->amount * $item->productVariant->basicPrice->priceDPH|priceFormat}
													{/if}
												</div>
												<div class="b-product-row__remove">
													<a href="#popup-confirm-subscription" class="u-font-sm" data-fancybox-custom data-fancybox-custom data-id="{$item->id}" data-next-subscription-order-id="{$subscription->nextSubscriptionOrder->id}" data-subscription-hash="{$subscription->hash}">
														{('trash')|icon}
														<span class="u-vhide">
															{_remove}
														</span>
													</a>
												</div>
											</div>
										</div>
									{/foreach}
								</div>

								<div class="b-summary u-mb-md u-pt-md">
									<div class="b-summary__item">
										<div class="b-summary__label">
											{_'subscription_price_per'}:
										</div>
										<div class="b-summary__value b-summary__value--total">
											{$subscription->totalSubscriptionProducts|priceFormat}
										</div>
									</div>
									<div class="b-summary__item" n:if="$subscription->nextSubscriptionOrder && $subscription->nextSubscriptionOrder->totalOneTimePrice > 0">
										<div class="b-summary__label">
											{_'order_price_per_part_one_time'}:
										</div>
										<div class="b-summary__value b-summary__value--total">
											{$subscription->nextSubscriptionOrder->totalOneTimePrice|priceFormat}
										</div>
									</div>
									<div class="b-summary__item" n:if="$subscription->nextSubscriptionOrder && $subscription->nextSubscriptionOrder->totalOneTimePrice > 0">
										<div class="b-summary__label b-summary__label--total">
											{_'order_price_total'}
										</div>
										<div class="b-summary__value b-summary__value--total">
											{$subscription->nextSubscriptionOrder->totalPrice|priceFormat}
										</div>
									</div>
									<div class="b-summary__item" n:if="$subscription->nextSubscriptionOrder">
										<div class="b-summary__label">
											{_'subscription_spare'}
										</div>
										<div class="b-summary__value">
											{$subscription->nextSubscriptionOrder->totalSavings|priceFormat}
										</div>
									</div>
								</div>
							 {/snippet}

							<div class="b-alert u-mb-lg">
								<div class="b-alert__inner">
									<h2>
										{_'subscription_forget_title'}
									</h2>
									<p>
										{_'subscription_forget_text'|noescape}
									</p>
									<a href="{plink 'UID|eshop'}" class="btn btn--arrow btn--sm">
										<span class="btn__text">
											<span class="item-icon item-icon--after">
												{_'subscription_forget_btn'}
												{('angle-right')|icon}
											</span>
										</span>
									</a>
								</div>
							</div>

							{* {snippet items}
								<span n:if='isset($isError)'>
									<p n:if="$isError" class="error">{$message}</p>
									<p n:if="!$isError" class="message-info">{$message}</p>
								</span>
								<span n:if='isset($message) && !isset($isError)'>
									<p class="message-info">{$message}</p>
								</span>
								{if $subscription->status === 'active' && $subscription->nextSubscriptionOrder}
									<div class="u-max-width--7-12 u-mx-auto">
										{_'subscription_explaination_how_to_add_items_to_order'}
									</div>
								{else}
									<div class="u-max-width--7-12 u-mx-auto">
										{_'subscription_no_items_in_order'}
									</div>
								{/if}
							{/snippet} *}


							<div class="u-mb-lg">
								{control subscriptionNextOrderForm}
							</div>
							{/if}

							{*<p class="u-text-center">
								<a href="#" class="btn btn--arrow btn--sm">
									<span class="btn__text">
										<span class="item-icon item-icon--after">
											{_'subscription_confirm_settings'}
											{('angle-right')|icon}
										</span>
									</span>
								</a>
							</p>*}
						</div>
					</div>
				</div>

				<div id="popup-confirm-subscription" class="b-subscription-popup u-hide">
					<div class="b-subscription-popup__inner">
						<h2>
							{_'subscription_remove_title'}
						</h2>
						<p>
							{_'subscription_remove_text'|noescape}
						</p>
						<a href="#" class="btn btn--arrow btn--sm">
							<span class="btn__text">
								<span class="item-icon item-icon--after">
									{_'subscription_remove_btn'}
									{('angle-right')|icon}
								</span>
							</span>
						</a>
					</div>
				</div>
			{/if}

			{include '../part/box/content.latte', class=>'u-mb-lg'}
		</div>
	</div>
