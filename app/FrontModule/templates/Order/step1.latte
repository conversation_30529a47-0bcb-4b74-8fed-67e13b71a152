{default $hideFooter = true}
{default $step = 2}
{if $showSubscriptionSettings}
	{var $step = 3}
{/if}
{block content}
	{snippetArea orderStep1Area}
		{snippet orderStep1}
			<div class="row-main">
				<div class="row-main__helper u-pt-xs">
					{* {include '../part/menu/breadcrumb.latte', class=>'u-mb-sm'} *}
				{include '../part/box/steps.latte', step=>$step}
				<h1 class="u-vhide">{$object->name}</h1>
				{control orderStep1Form}
				</div>
			</div>
		{/snippet}
	{/snippetArea}
{/block}
