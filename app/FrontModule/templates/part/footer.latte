<footer class="footer">
	<div class="footer__top">
		<div class="row-main">
			<div class="footer__grid grid">
				{if isset($pages->contact->id) &&
					(isset($pages->contact->cf->contact) && $pages->contact->cf->contact) &&
					(
						(isset($pages->contact->cf->contact->phone) && $pages->contact->cf->contact->phone)
						||
						(isset($pages->contact->cf->contact->email) && $pages->contact->cf->contact->email)
					)
				}
					<div class="footer__cell grid__cell size--6-12@md size--4-12@lg order--2 order--1@lg">
						<h2 class="h4 footer__title">
							{_title_contact}
						</h2>
						<div class="footer__contact contact">
							<ul class="contact__list">
								{if $mutation->isEshop || isset($_COOKIE['CLIENT_TESTING'])}
									<li n:if="isset($pages->contact->cf->contact->phone) && $pages->contact->cf->contact->phone" class="contact__item">
										<span class="contact__name">
											{_title_phone}
										</span>
										<span class="contact__contact">
											<a href="tel:{$pages->contact->cf->contact->phone|replace:' ',''}" class="contact__link item-icon">
												{$pages->contact->cf->contact->phone}
												{('phone'|icon)}
											</a>
										</span>
										{if isset($pages->contact->cf->contact->openingHours)}
										<span class="contact__opening">
											{_title_opening}: {$pages->contact->cf->contact->openingHours}
										</span>
										{/if}
									</li>
								{/if}
								<li n:if="isset($pages->contact->cf->contact->email) && $pages->contact->cf->contact->email" class="contact__item">
									<span class="contact__name">
										{_title_email}
									</span>
									<span class="contact__contact">
										<a href="mailto:{$pages->contact->cf->contact->email}" class="contact__link item-icon">
											{$pages->contact->cf->contact->email|obfuscateEmailAddresses|noescape}
											{('envelope'|icon)}
										</a>
									</span>
								</li>
							</ul>
						</div>
					</div>
				{/if}

				{if isset($pages->contact->id) &&
					(isset($pages->contact->cf->contact) && $pages->contact->cf->contact) &&
					(
						(isset($pages->contact->cf->contact->facebook) && $pages->contact->cf->contact->facebook)
						||
						(isset($pages->contact->cf->contact->instagram) && $pages->contact->cf->contact->instagram)
					)
				}
					<div class="footer__cell grid__cell size--6-12@md size--3-12@lg u-mr-auto order--3 order--2@lg">
						<h2 class="h4 footer__title">
							{_title_social}
						</h2>
						{include './box/social.latte'}
					</div>

				{/if}

				{if $mutation->getSetting(App\Model\Orm\MutationSetting\ContentSection::FooterNewsletter)}
				<div class="footer__cell footer__cell--form grid__cell size--5-12@lg order--1 order--3@lg">
					<h2 class="h4 footer__title">
						{_title_newsletter}
					</h2>
					{if $mutation->langCode == 'ae'}

						<form action="http://newsletter.eurovetsworld.com/account" method="post" id="frm-newsletterForm-form" class="f-subscribe">

							<p class="f-subscribe__main">
								<label class="f-subscribe__fix inp-fix">
									<input type="email" class="inp-text inp-text--rounded" placeholder="Enter your e-mail" name="email" id="frm-newsletterForm-form-email"
									       required >
								</label>
								<button type="submit" class="f-subscribe__btn btn btn--black btn--loader">
									<span class="btn__text btn__text--desktop">
										{_btn_send}
									</span>
									<span class="btn__text btn__text--mobile">
										{_ok}
									</span>
								</button>
							</p>

							<p class="f-subscribe__fix f-subscribe__fix--checkbox inp-fix">
								<label class="f-subscribe__checkbox inp-item inp-item--checkbox inp-item--sm error">
									<input type="checkbox" name="agree" value="1" id="frm-newsletterForm-form-agree"  required>
									<span>
										{('check'|icon)}
										{_label_agree_1} <a href="{plink 'UID|personalData'}" target="_blank">{_label_agree_2}</a>.
								</span>
								</label>
							</p>

							{*							<input type="hidden" name="antispamHash" value="7uv6a1rq">*}
							{*							<input type="hidden" name="_do" value="newsletterForm-form-submit">*}
							<input type="hidden" name="join" value="858740">
						</form>

					{else}
						{control newsletterForm}
					{/if}
				</div>
				{/if}
			</div>
		</div>
	</div>
	<div class="footer__bottom">
		<div class="row-main">
			<div class="grid grid--y-xxw">
				<div class="grid__cell size--autogrow">
					<div class="footer__inline">
					<ul class="footer__list">
						{var $cacheName = $cacheService->getName($object, \SuperKoderi\CacheService::MENU_FOOTER).$mutation->id}
						{cache $cacheName, tags => ['trees', 'tplCache', 'translates'], expire => '12 hours'}
							{foreach $pages->title->cf->footerMenu as $item}
								<li class="footer__item" n:if="
									isset($item->tree->public) && $item->tree->public && (
									($mutation->isEshop || isset($_COOKIE['CLIENT_TESTING'])) ||
									(!isset($item->showOnlyOnEshop) || (isset($item->showOnlyOnEshop) && !$item->showOnlyOnEshop))
									)
								">
									<a n:href="$item->tree" class="footer__link">
										{$item->tree->nameAnchor}
									</a>
								</li>
							{/foreach}
						{/cache}
						<li class="footer__item">
							<a href="#" class="footer__link" data-cookie-open>
								{_cookies_menu_item}
							</a>
						</li>
					</ul>
					<p class="footer__copyright">
						<span class="footer__separator">|</span>
						<span class="footer__copy">
							{capture $year}{2001|copyright}{/capture}
							{_copyright|replace:"%s",(string)$year}
						</span>
					</p>
					</div>
				</div>
				<div class="grid__cell size--auto u-ml-auto">
					<div class="footer__lang">
						{include './box/lang.latte', footer=>true}
					</div>
				</div>
			</div>
		</div>
	</div>
</footer>

{*
<footer class="footer">
	<div class="row-main">
		{var $cacheName = 'footer'}
		{cache $cacheName}
			{var $tags = []}
			<div class="footer__group">
				<p class="footer__title h3">
					{_title_services}
					{php $tags[] = 'title_services'}
				</p>
				<ul class="footer__list">
					{php $tags[] = $pages->services}
					{foreach $pages->services->crossroad as $fi}
						<li class="footer__item">
							{cache 'linkTree-'.$fi->id, tags=>$ct($fi)}
								<a n:href="ALIAS|$fi->alias" class="footer__link">
									{$fi->nameAnchor}
								</a>
							{/cache}
						</li>
					{/foreach}
				</ul>
			</div>
			{cache $cacheName, tags=>$ct($tags)}{/cache}
		{/cache}

		<p class="footer__copyrights">
			{php $year = 2016}
			&copy; {$year|copyright} {_copyright}
		</p>
	</div>
</footer>
*}
