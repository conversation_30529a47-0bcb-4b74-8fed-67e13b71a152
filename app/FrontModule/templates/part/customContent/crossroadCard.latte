{default $card = isset($customContentItem) && $customContentItem ? $customContentItem}

<div n:if="$card" class="b-landing-card">
	<div class="b-landing-card__inner">
		<div class="row-main">
			<div class="u-max-width--8-12 u-mx-auto">
				<div class="grid">
					<div n:foreach="$card->card as $item" class="grid__cell size--6-12@md">
						{var $isExternal = false}
						{var $link = null}
						{var $buttonText = null}
						{var $productVariant = null}
						{if isset($item->buttonExternal)}
							{var $link = $item->buttonExternal}
							{var $isExternal = strpos($link, 'http') === 0}
							{var $buttonText = $item->buttonText ?? null}
						{elseif isset($item->buttonInternal)}
							{capture $link}{plink $item->buttonInternal}{/capture}
							{var $buttonText = $item->buttonText ?? null}
						{elseif isset($item->buttonProduct)}
							{capture $link}{plink $item->buttonProduct}{/capture}
							{var $buttonText = $item->buttonText ?? null}
							{var $productVariant = $item->buttonProduct->getEntity()}
						{elseif isset($item->buttonSeolink)}
							{var $link = '/' . $item->buttonSeolink->url}
							{var $buttonText = $item->buttonText ?? null}
						{/if}
						<div n:class="b-landing-card__item, link-extend, ($item->variant ?? null) ? : b-landing-card__item--content">
							{if $item->variant ?? null}
								<p n:ifcontent class="b-landing-card__image-bg">
									{if ($item->image ?? null) && ($item->image->getEntity() ?? null)}
										{if $object->template == 'Page:landingTwo'}
											<picture>
												<source srcset="{$item->image->getSize('crossroadCardImageBgTwo')->src} 1x, {$item->image->getSize('crossroadCardImageBgTwoRetina')->src} 2x" loading="lazy">
												<img src="{$item->image->getSize('crossroadCardImageBgTwo')->src}" width="{$item->image->getSize('crossroadCardImageBgTwo')->width}" height="{$item->image->getSize('crossroadCardImageBgTwo')->height}" alt="{$item->image->name ?? null}" loading="lazy">
											</picture>
										{else}
											<img src="{$item->image->getSize('crossroadCardImageBg')->src}" width="{$item->image->getSize('crossroadCardImageBg')->width}" height="{$item->image->getSize('crossroadCardImageBg')->height}" alt="{$item->image->name ?? null}" loading="lazy">
										{/if}
										{* <img src="http://placeimg.com/410/488/any" width="410" height="488" alt="{$item->image->name ?? null}" loading="lazy"> *}
										{* <img src="/static/img/illust/crossroadCardImageBg.png" width="410" height="488" alt="{$item->image->name ?? null}" loading="lazy"> *}
									{else}
										<img src="/static/img/illust/noimg.svg" width="410" height="488" alt="" loading="lazy">
									{/if}
									<a n:if="$link && !$buttonText" href="{$link}" n:attr="target: $isExternal ? '_blank'" class="link-extend__link"></a>
								</p>
								<p n:ifcontent class="b-landing-card__button">
									<a n:if="$link && $buttonText" href="{$link}" n:attr="target: $isExternal ? '_blank'" class="btn btn--card link-extend__link">
										<span n:ifcontent class="btn__text">
											{$buttonText}
										</span>
									</a>
								</p>
							{else}
								<h3 class="b-landing-card__head">
									<span class="b-landing-card__head-icon">
										<span n:if="$item->color ?? null" style="--color: {$item->color|noescape}">
											<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30">
												<polygon points="10 0 10 10 0 10 0 20.38 10 20.38 10 30 20.38 30 20.38 20.38 30 20.38 30 10 20.38 10 20.38 0 10 0"/>
											</svg>
										</span>
									</span>
									{if isset($item->text)}
										{$item->text|noescape}
									{/if}
									{if isset($item->textColor)}
										<span n:if="$item->color ?? null" style="--color: {$item->color|noescape}">
											{$item->textColor|noescape}
										</span>
									{/if}
								</h3>
								<div n:if="$item->content ?? null" class="b-landing-card__text">{$item->content|noescape}</div>
								<p n:ifcontent class="b-landing-card__image">
									{if ($item->image ?? null) && ($item->image->getEntity() ?? null)}
										{if $object->template == 'Page:landingTwo'}
											<picture>
												<source srcset="{$item->image->getSize('crossroadCardImageTwo')->src} 1x, {$item->image->getSize('crossroadCardImageTwoRetina')->src} 2x" loading="lazy">
												<img src="{$item->image->getSize('crossroadCardImageTwo')->src}" width="{$item->image->getSize('crossroadCardImageTwo')->width}" height="{$item->image->getSize('crossroadCardImageTwo')->height}" alt="{$item->image->name ?? null}" loading="lazy">
											</picture>
										{else}
											<img src="{$item->image->getSize('crossroadCardImage')->src}" width="{$item->image->getSize('crossroadCardImage')->width}" height="{$item->image->getSize('crossroadCardImage')->height}" alt="{$item->image->name ?? null}" loading="lazy">
										{/if}
										{* <img src="http://placeimg.com/213/189/any" width="213" height="189" alt="{$item->image->name ?? null}" loading="lazy"> *}
										{* <img src="/static/img/illust/crossroadCardImage.png" width="213" height="189" alt="{$item->image->name ?? null}" loading="lazy"> *}
									{else}
										<img src="/static/img/illust/noimg.svg" width="213" height="189" alt="" loading="lazy">
									{/if}
								</p>
								{if $productVariant && $productVariant->isRealSale && ($mutation->isEshop || isset($_COOKIE['CLIENT_TESTING']))}
									<a n:if="$link && $buttonText" href="{$link}" n:attr="target: $isExternal ? '_blank'" class="link-extend__link"></a>
									{include 'part/basketButton.latte', product => $productVariant}
								{else}
									<p n:ifcontent class="b-landing-card__button">
										<a n:if="$link && $buttonText" href="{$link}" n:attr="target: $isExternal ? '_blank'" class="btn btn--card link-extend__link">
											<span n:ifcontent class="btn__text">
												{$buttonText}
											</span>
										</a>
									</p>
								{/if}
							{/if}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
