{default $orders = false}
{default $showDetail = false}
{default $title = true}

<section n:if="$orders->count()" class="c-table u-mb-lg u-last-m-0">
	<h2 n:if="$title" class="c-table__title">
		{_my_orders}
	</h2>
	{if $showDetail}
		<table class="c-table__table">
			{foreach $orders as $order}
				<tr id="order-{$order->number}" class="c-table__row js-open-table">
					<td class="c-table__cell c-table__cell--date">
						{$order->created|date:"d. m. Y"}
					</td>
					<td class="c-table__cell c-table__cell--number">
						{$order->number}
					</td>
					<td class="c-table__cell c-table__cell--items">
						<a href="#" class="c-table__link js-open-table__link link-mask">
							{$order->products->count() + $order->subscriptions->count()} {translate}{$order->products->count() + $order->subscriptions->count()|plural:'items_1', 'items_2_4', 'items_5'}{/translate}
						</a>
					</td>
					<td class="c-table__cell c-table__cell--price">
						<b>
							{$order->totalPriceDPH|priceFormat}
						</b>
					</td>
					<td class="c-table__cell c-table__cell--status">
						<span class="status status{if $order->status=='done'}--ok{elseif $order->status=='cancel'}{else}{/if}">
							{translate}order_status_{$order->status}{/translate}
						</span>
						<span n:if="$order->subscriptionOrder">
							Součástí předplatného {$order->subscriptionOrder->subscription->name}
						</span>
					</td>
				</tr>
				<tr class="js-open-table__content" style="display:none">
					<td colspan="5" class="c-table__content u-last-m0">
						{include '../crossroad/products-row.latte', orderHistory=>true, order=>$order, subscriptionProductInCart => false}
						<div class="c-table__btns">
							<p class="c-table__btn u-text-right">
								<a n:if="$order->showRetryPayment" href="{plink 'UID|retryOnlinePayment', $order->number, $order->hash, true}" class="btn btn--gray">
									<span class="btn__text">
										{_btn_order_retry_payment}
									</span>
								</a>
							</p>
							<p class="c-table__btn u-text-right">
								<a n:href="this, repeatOrder=>$order->id" class="btn">
									<span class="btn__text">
										{_btn_order_repeat}
									</span>
								</a>
							</p>
						</div>
					</td>
				</tr>
			{/foreach}
		</table>
	{else}
		<table class="c-table__table u-mb-lg">
			{foreach $orders as $order}
				<tr id="order-{$order->number}" class="c-table__row">
					<td class="c-table__cell c-table__cell--date">
						{$order->created|date:"d. m. Y"}
					</td>
					<td class="c-table__cell c-table__cell--number">
						{$order->number}
					</td>
					<td class="c-table__cell c-table__cell--items">
						<a href="{$pages->userOrderHistory->alias}#order-{$order->number}" class="c-table__link">
							{$order->products->count() + $order->subscriptions->count()} {translate}{$order->products->count() + $order->subscriptions->count()|plural:'items_1', 'items_2_4', 'items_5'}{/translate}
						</a>
					</td>
					<td class="c-table__cell c-table__cell--price">
						<b>
							{$order->totalPriceDPH|priceFormat}
						</b>
					</td>
					<td class="c-table__cell c-table__cell--status">
						<span class="status status{if $order->status=='done'}--ok{elseif $order->status=='cancel'}{else}{/if}">
							{translate}order_status_{$order->status}{/translate}
						</span>
					</td>
				</tr>
			{/foreach}
		</table>
	{/if}

	{if $showLink && $ordersAll->count() > $orders->count()}
		<p class="c-table__more u-mb-lg u-text-right">
			<a n:href="'UID|userOrderHistory'" class="c-table__link">
				{_btn_show_orders}
			</a>
		</p>
	{/if}
</section>
