{default $subscriptions = false}
{default $showDetail = false}
{default $title = false}

<section role="region" class="c-table u-mb-lg u-last-m-0">
	{if $title}
		<h2 class="c-table__title">
			{_my_subscription}
		</h2>
	{/if}
	<div class="c-table__table u-mb-lg">
		<p class="c-table__row c-table__row--head">
			<span class="c-table__cell c-table__cell--head">
				{_'subscription_name'}
			</span>
			<span class="c-table__cell c-table__cell--head">
				{_'subscription_interval'}
			</span>
			<span class="c-table__cell c-table__cell--head">
				{_'subscription_number_of_items'}
			</span>
			<span class="c-table__cell c-table__cell--head">
				{_'subscription_price'}
			</span>
			<span class="c-table__cell c-table__cell--head">
				{_'subscription_next_order'}
			</span>
		</p>
		{foreach $subscriptions as $subscription}
			<p class="c-table__row">
				<span class="c-table__cell c-table__cell--subname">
					<strong>{$subscription->name}</strong>
				</span>
				<span class="c-table__cell c-table__cell--interval">
					<span>{_'subscription_interval_label'}:</span> {$subscription->interval} {translate}{$subscription->interval|plural:'weeks_1', 'weeks_2_4', 'weeks_5'}{/translate}
				</span>
				<span class="c-table__cell c-table__cell--items">
					{$subscription->items->count()} {translate}{$subscription->items->count()|plural:'items_1', 'items_2_4', 'items_5'}{/translate}
				</span>
				<span class="c-table__cell c-table__cell--price">
					<b>
						{$subscription->totalSubscriptionProducts|priceFormat}
					</b>
				</span>
				<span class="c-table__cell c-table__cell--subdate">
					{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->reservedSubscriptionOrder}
						{$subscription->reservedSubscriptionOrder->payOrderAfter|date}
					{/if}

					{if $subscription->status === App\Model\Subscription::STATUS_ACTIVE && $subscription->reservedSubscriptionOrder == null && $subscription->nextSubscriptionOrder}
						{$subscription->nextSubscriptionOrder->payOrderAfter|date}
					{/if}
				</span>
				<span class="c-table__cell c-table__cell--substate">
					{if $subscription->status == App\Model\Subscription::STATUS_ACTIVE}
						<span class="u-color-green"><b>{translate}subscription_status_{$subscription->status}{/translate}</b></span>
					{else}
						<span class="u-color-gray"><b>{translate}subscription_status_{$subscription->status}{/translate}</b></span>
					{/if}
				</span>
				<span class="c-table__cell c-table__cell--subactions">
					<a href="{plink 'UID|subscriptionDetail', $subscription->hash}">
						{_'subscription_detail'}
					</a>
					<span>|</span>
					{* <a n:href="deactivateSubscription! $subscription->hash" n:if="$subscription->status === App\Model\Subscription::STATUS_ACTIVE">
						{_'deactivate'}
					</a> *}
					<a href="#popup-deactivate-subscription" data-fancybox-deactivate data-subscription-hash="{$subscription->hash}" n:if="$subscription->status !== App\Model\Subscription::STATUS_DEACTIVATED">
						{_'deactivate'}
					</a>
					<a n:href="'UID|subscriptionActivation',  $subscription->hash" n:if="$subscription->status === App\Model\Subscription::STATUS_DEACTIVATED">
						{_'activate'}
					</a>
				</span>
			</p>
		{/foreach}
	</div>
	<p class="c-table__more u-mb-lg u-text-right">
		<a href="#popup-add-subscription" data-fancybox>
			<b>{_'subscription_add'}</b>
		</a>
	</p>
</section>

<div id="popup-deactivate-subscription" class="b-subscription-popup u-hide">
	<div class="b-subscription-popup__inner">
		<h2>
			{_'subscription_deactivate_title'}
		</h2>
		<p>
			{_'subscription_deactivate_text'|noescape}
		</p>

		{control subscriptionCancelForm}
{*
		<a href="#" class="btn btn--arrow btn--sm">
			<span class="btn__text">
				<span class="item-icon item-icon--after">
					{_'subscription_deactivate_btn'}
					{('angle-right')|icon}
				</span>
			</span>
		</a>
*}
	</div>
</div>
