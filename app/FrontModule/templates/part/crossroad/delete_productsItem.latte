{default $showAnnotation = FALSE}
{default $isAccessories = FALSE}
{default $isCompare = FALSE}
{default $isSet = FALSE}

<li class="c-products__item">
	<div class="c-products__inner">
		<div class="c-products__top">
			<div class="c-products__img">
				{if $product->images->count() > 0}
					{php $img = $imageResizer->getImg($product->firstImage->filename, 's')}
					<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" />
				{else}
					<img src="{$basePath}/static/img/illust/noimg.svg" alt="" width="225" height="225" />
				{/if}
			</div>
			<div class="c-products__color">
				{*{if $product->getParameterByUid('color')->values}*}
					{*{foreach $product->getParameterByUid('color')->values as $color}*}
						{*<span class="c-products__variation" style="{if isset($color->image) && $color->image->url}background-image: url('{$color->image->url|noescape}'){else}background-color: {$color->annotation|noescape};{/if}">*}
							{*<span class="vhide">*}
								{*{$color->value}*}
							{*</span>*}
						{*</span>*}
					{*{/foreach}*}
				{*{/if}*}
			</div>
			<{if isSet($title)}{$title}{else}h2{/if} class="c-products__title">
				<a href="{$lg}/{$product->alias}{if isset($landingDetail) && $landingDetail}?landing={$landingDetail->id}{/if}" class="c-products__link">
					<span>
						{$product->nameAnchor}
					</span>
				</a>
			</{if isSet($title)}{$title}{else}h2{/if}>
			{*{php $reviewInfo = $product->getReviewInfo()}*}
			{*<p class="c-products__stars">*}
				{*<span class="stars stars--small">*}
					{*<span class="stars__wrap">*}
						{*<span class="icon icon--star"></span>*}
						{*<span class="icon icon--star"></span>*}
						{*<span class="icon icon--star"></span>*}
						{*<span class="icon icon--star"></span>*}
						{*<span class="icon icon--star"></span>*}
						{*<span class="stars__in" style="width: {$reviewInfo['percent']}%;">*}
							{*<span class="icon icon--star"></span>*}
							{*<span class="icon icon--star"></span>*}
							{*<span class="icon icon--star"></span>*}
							{*<span class="icon icon--star"></span>*}
							{*<span class="icon icon--star"></span>*}
						{*</span>*}
						{*<span class="vhide">{_review}: {$reviewInfo['percent']}%</span>*}
					{*</span>*}
					{*<span class="stars__info">*}
						{*<span class="vhide">{_review_count}:</span> {$reviewInfo['count']}&times;*}
					{*</span>*}
				{*</span>*}
			{*</p>*}
			{if $product->annotation && $showAnnotation}
				<p class="c-products__desc">
					{$product->annotation|texy|noescape}
				</p>
			{/if}

			{if !$isSet}
				<div class="c-products__wrap">
					<p class="c-products__price">
						<strong>{$product->priceFinalDPH|priceFormat}</strong>
						{if $product->discount > 0}
							{if $product->priceOriginDPH}<del>{$product->priceOriginDPH|priceFormat}</del>{/if}
						{/if}
					</p>
					{*<p class="c-products__availability color-green">{$product->supplyString}</p>*}
				</div>
			{/if}

			<p class="c-products__flags flags">
				{if !$product->isOld}
					{if $product->isInPrepare}
						<span class="flags__item flags__item--prepare">{_prepare}</span>
					{else}
						<span class="flags__item flags__item--bestseller" n:if="$product->isBestseller">{_flag_bestseller}</span>
						<span class="flags__item flags__item--top" n:if="$product->isTopProduct">{_flag_top}</span>
						<span class="flags__item flags__item--catalog" n:if="$product->isPromotional">{_flag_promotional}</span>
						<span class="flags__item flags__item--new" n:if="$product->isNew">{_flag_new}</span>
						<span class="flags__item flags__item--action" n:if="$product->isAction">
							<span class="flags__inner">
								{_flag_action}
								{if $product->discount > 0}
									<span class="flags__percent">
										{if $product->discountType == 1}
											-{$product->discount}%
										{else}
											-{$product->discount|priceFormat}
										{/if}
									</span>
								{/if}
							</span>
						</span>
						<span class="flags__item flags__item--set" n:if="$product->isSet">{_flag_set}</span>
					{/if}
				{else}
					<span class="flags__item flags__item--sold">{_sold_out}</span>
				{/if}
			</p>
		</div>

		{if $isCompare}
			<dl class="c-products__params">
				{foreach $allParamList as $param}
					{var $c=$product->getParameterById($param['id'])}
					{if $c}
						<dt class="c-products__param-item">{$c->name}:</dt>
						{if $c->type == "text" || $c->type == "number"}
							<dd class="c-products__param-item">{$c->values} {$c->unit}</dd>
						{elseif $c->type == "bool"}
							{if $c->values == 1}
								<dd class="c-products__param-item">{_yes}</dd>
							{else}
								<dd class="c-products__param-item">{_no}</dd>
							{/if}
						{else}
							<dd class="c-products__param-item">
								{if is_array($c->values) && count($c->values)}
									{foreach $c->values as $value}
										{$value->value}{if !$iterator->last}, {/if}
									{/foreach}
								{else}
									{$c->values}
								{/if}
							</dd>
						{/if}
					{else}
						<dt class="c-products__param-item">{$param['name']}:</dt>
						<dd class="c-products__param-item">–</dd>
					{/if}
				{/foreach}
			</dl>

			<a n:href="compareClick!, productId => $product->id" class="c-products__remove icon icon--cross" title="{_remove}">
				<span class="vhide">{_remove}</span>
			</a>
		{/if}

		{if !$isSet && !$product->isOld && !$product->notSoldSeparately && !$product->isInPrepare && $product->priceFinalDPH > 0}
			<form action='{link "UID|preprebasket"}' class="c-products__form f-basket-add{if !$isCompare} c-products__hover{/if}">
				<p class="c-products__btn">
					<input type="hidden" name="id" value="{$product->id}" />
					<label for="amount-{$product->id}" class="vhide">{_count}</label>
					<span class="inp__count">
						<input type="number" name="amount" value="1" class="inp__text" id="amount-{$product->id}" />
						<span class="inp__count__unit">ks</span>
						<a href="#" class="inp__count__tool inp__count__tool--plus" data-step="1">+</a>
						<a href="#" class="inp__count__tool inp__count__tool--minus" data-step="-1">–</a>
					</span>
					<button type="submit" class="btn btn--primary"
						onclick="dataLayer.push({'event': 'ga.event','eventCategory': 'Přidání do košíku','eventNonInteraction': true});"
					>
						<span class="btn__text">
							<span class="btn__loading">
								{_btn_add_basket_short}
							</span>
						</span>
					</button>
				</p>
			</form>
		{/if}
	</div>
</li>
