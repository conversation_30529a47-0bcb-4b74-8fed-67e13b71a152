{default $showCountUnit = false}
{default $isRent = false}
{default $showRemove = true}

{if $orderItem}
	<div class="b-product-small">
		<div class="b-product-small__wrap">
			<div class="b-product-small__main">
				{if $orderItem->type == 'product' }
					{var $link = $presenter->link('ALIAS|'. $orderItem->variant->alias, ['v' => $orderItem->variant->id])}
				{/if}

				{if isset($link)}<a href="{$link}" class="b-product-small__link">{/if}
					<span class="b-product-small__img">
						{if ($orderItem->type == 'product' || $orderItem->type == 'rent') && $orderItem->variant->firstImage}
							{php $img = $imageResizer->getImg($orderItem->variant->firstImage->filename, 'sm')}
							<img loading="lazy" src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}"/>
						{else}
							<img loading="lazy" src="{$basePath}/static/img/illust/noimg.svg" alt="" width="50" height="50" />
						{/if}
					</span>
					<span class="b-product-small__name">
						{$orderItem->name}
						{if $orderItem->variantName}
							({$orderItem->variantNameNoBreak|noescape})
						{/if}
					</span>
				{if isset($link)}</a>{/if}
			</div>
			{if !$isRent}
				<div class="b-product-small__count">
					<p class="inp inp--group inp--count b-product__count">
					<span class="inp__preppend">
						<a n:href="sub!, id=>$orderItem->uniqueKey" class="btn btn--count" data-add="-1">
							{('polygon')|icon}
						</a>
					</span>
						<span class="inp__fix">
						<label class="inp__label u-vhide">
							{_count_title}
						</label>
						{php $maxVal = 0}
						{*{if $orderItem->product && $orderItem->product->voucher && $orderItem->product->voucher->public}*}
							{*{php $maxVal = 999999}*}
						{if $orderItem->variant}
							{php $maxVal = $orderItem->variant->totalSupplyCount}
						{/if}

						<input type="number" class="inp__text inp__text--count js-auto-submit {if $showCountUnit}u-text-right{/if}" name="amount[{$orderItem->uniqueKey}]" value="{$orderItem->amount}" min="0"
							   data-max="{$maxVal}" {if ($orderItem->variant && $orderItem->variant->isOpenBox)} max="{$maxVal}"{/if}
						>
							{if $showCountUnit}
								<span class="inp__unit">
								{_count_short}
							</span>
							{/if}
					</span>
						<span class="inp__append">
						<a n:href="add!, id=>$orderItem->uniqueKey" class="btn btn--count btn--count-plus {*if $orderItem->amount == $maxVal}is-disabled{/if*}" data-add="1">
							{('polygon')|icon}
						</a>
					</span>
					</p>
				</div>
			{/if}
			<p class="b-product-small__price">
				{$orderItem->totalPriceDPH|priceFormat}
			</p>


			{if $showRemove}
				<a n:href="remove!, id => $orderItem->uniqueKey" class="b-product-small__close js-link-ajax">
					{('close')|icon}
				</a>
			{/if}
		</div>


		{if $orderItem->insurances}
			{foreach $orderItem->insurances as $i}
				<p class="b-product-small__extra">
					<span class="b-product-small__name">
						{$i->name}
					</span>
					<span class="b-product-small__count">
						1
					</span>
					<strong class="b-product-small__price">
						{$i->unitPriceDPH|priceFormat}
					</strong>
					<a n:href="removeSub!, id=>$orderItem->uniqueKey, subKey=>$i->getItemId()"
						class="b-product-small__close js-link-ajax">
						{('close')|icon}
					</a>
				</p>
			{/foreach}
		{/if}
		{if $orderItem->services}
			{foreach $orderItem->services as $i}
				<p class="b-product-small__extra">
					<span class="b-product-small__name">
						{$i->name}
					</span>
					<span class="b-product-small__count">
						1
					</span>
					<strong class="b-product-small__price">
						{$i->unitPriceDPH|priceFormat}
					</strong>
					<a n:href="removeSub!, id=>$orderItem->uniqueKey, subKey=>$i->getItemId()"
						class="b-product-small__close js-link-ajax">
						{('close')|icon}
					</a>
				</p>
			{/foreach}
		{/if}
		{if $orderItem->warranties}
			{foreach $orderItem->warranties as $i}
				<p class="b-product-small__extra">
					<span class="b-product-small__name">
						{$i->name}
					</span>
					<span class="b-product-small__count">
						1
					</span>
					<strong class="b-product-small__price">
						{$i->unitPriceDPH|priceFormat}
					</strong>
					<a n:href="removeSub!, id=>$orderItem->uniqueKey, subKey=>$i->getItemId()"
						class="b-product-small__close js-link-ajax">
						{('close')|icon}
					</a>
				</p>
			{/foreach}
		{/if}


	</div>
{/if}
