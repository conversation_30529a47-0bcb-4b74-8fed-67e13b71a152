{default $class = 'u-mb-lg'}
{default $category = false}

<div class="b-intro {$class}">
	<div class="b-intro__wrap img-{$object->id}">
		<div class="row-main">
			<div class="b-intro__inner">
				<div class="grid">
					<div class="grid__cell size--5-12@xl">
						<h1 class="b-intro__title">
							{$object->nameTitle}
						</h1>
						{if $object->annotation}
							<p class="b-intro__annot">
								{$object->annotation|texy|noescape}
							</p>
						{/if}
					</div>
					<div class="grid__cell size--7-12 u-hide--xl-down no-print">
						{include './polygons.latte', class=>'u-mb-0'}
					</div>
				</div>

				<style>
					.img-{$object->id} {
						background-image: url("{if $object->images->count()}{php $img = $imageResizer->getImg($object->firstImage->filename, 'md')}{$img->src|noescape}{else}./static/img/illust/banner-category.jpg{/if}");
					}
					@media (min-width: 480px) {
						.img-{$object->id} {
							background-image: url("{if $object->images->count()}{php $img = $imageResizer->getImg($object->firstImage->filename, 'lg')}{$img->src|noescape}{else}./static/img/illust/banner-category.jpg{/if}");
						}
					}
					@media (min-width: 750px) {
						.img-{$object->id} {
							background-image: url("{if $object->images->count()}{php $img = $imageResizer->getImg($object->firstImage->filename, 'xl')}{$img->src|noescape}{else}./static/img/illust/banner-category.jpg{/if}");
						}
					}
					@media (min-width: 1000px) {
						.img-{$object->id} {
							background-image: url("{if $object->images->count()}{php $img = $imageResizer->getImg($object->firstImage->filename, 'max')}{$img->src|noescape}{else}./static/img/illust/banner-category.jpg{/if}");
						}
					}
				</style>
			</div>
		</div>
	</div>
</div>
