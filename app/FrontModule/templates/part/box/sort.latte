{default $class = 'u-mb-xxs'}

<div class="f-sort {$class}">
	{*
	<span class="inp__fix select2 f-sort__select u-mb-sm">
		<select name="order" id="order" class="inp__select select2__inp" size="4" data-hide-search="true">
			{php $cleanFilterParamCopy = $cleanFilterParam}
			{php unset($cleanFilterParamCopy['order'])}
			{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
			{php $linkBestSellers = urldecode(htmlspecialchars_decode($link))}
			<option value="{$linkBestSellers}" label="{_best_sellers}" {if $catalogOrder == 'bestseller'}selected{/if}>
				{_best_sellers}
			</option>

			{php $cleanFilterParamCopy = $cleanFilterParam}
			{php $cleanFilterParamCopy['order'] = 'newest'}
			{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
			{php $linkLatest = urldecode(htmlspecialchars_decode($link))}
			<option value="{$linkLatest}" label="{_latest}" {if $catalogOrder == 'newest'}selected{/if}>
				{_latest}
			</option>

			{php $cleanFilterParamCopy = $cleanFilterParam}
			{php $cleanFilterParamCopy['order'] = 'cheapest'}
			{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
			{php $linkCheapest = urldecode(htmlspecialchars_decode($link))}
			<option value="{$linkCheapest}" label="{_cheapest}" {if $catalogOrder == 'cheapest'}selected{/if}>
				{_cheapest}
			</option>

			{php $cleanFilterParamCopy = $cleanFilterParam}
			{php $cleanFilterParamCopy['order'] = 'expensive'}
			{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
			{php $linkMostExpensive = urldecode(htmlspecialchars_decode($link))}
			<option value="{$linkMostExpensive}" label="{_most_expensive}" {if $catalogOrder == 'expensive'}selected{/if}>
				{_most_expensive}
			</option>
		</select>
	</span>
	*}

	{*
	<div class="inp-custom-select u-mb-sm f-sort__select u-vhide">
		<a href="#" class="inp-custom-select__select inp-icon inp-icon--after">
			<span class="inp-custom-select__select-wrap">
				{_best_sellers}
			</span>
			<span class="inp-icon__icon">
				{(angle-down)|icon}
			</span>
		</a>
		<div class="inp-custom-select__wrapper">
			<label class="inp-custom-select__item inp-item js-tabs-filter__link">
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{php unset($cleanFilterParamCopy['order'])}
				{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
				{php $linkBestSellers = urldecode(htmlspecialchars_decode($link))}
				<input type="radio" name="product-sort" value="{$linkBestSellers}" class="inp-item__inp" {if $catalogOrder == 'bestseller'}checked{/if}>
				<a href="{$linkBestSellers}" class="inp-custom-select__link inp-item__text">
					{_best_sellers}
				</a>
			</label>
			<label class="inp-custom-select__item inp-item js-tabs-filter__link">
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{php $cleanFilterParamCopy['order'] = 'newest'}
				{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
				{php $linkLatest = urldecode(htmlspecialchars_decode($link))}
				<input type="radio" name="product-sort" value="{$linkLatest}" class="inp-item__inp" {if $catalogOrder == 'newest'}checked{/if}>
				<a href="{$linkLatest}" class="inp-item__text inp-custom-select__link">
					{_latest}
				</a>
			</label>
			<label class="inp-custom-select__item inp-item js-tabs-filter__link">
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{php $cleanFilterParamCopy['order'] = 'cheapest'}
				{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
				{php $linkCheapest = urldecode(htmlspecialchars_decode($link))}
				<input type="radio" name="product-sort" value="{$linkCheapest}" class="inp-item__inp" {if $catalogOrder == 'cheapest'}checked{/if}>
				<a href="{$linkCheapest}" class="inp-item__text inp-custom-select__link">
					{_cheapest}
				</a>
			</label>
			<label class="inp-custom-select__item inp-item js-tabs-filter__link">
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{php $cleanFilterParamCopy['order'] = 'expensive'}
				{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
				{php $linkMostExpensive = urldecode(htmlspecialchars_decode($link))}
				<input type="radio" name="product-sort" value="{$linkMostExpensive}" class="inp-item__inp" {if $catalogOrder == 'expensive'}checked{/if}>
				<a href="{$linkMostExpensive}" class="inp-item__text inp-custom-select__link">
					{_most_expensive}
				</a>
			</label>
		</div>
	</div>
	*}

	<div class="f-sort__inner">
		<p class="f-sort__title f-sort__title--long">
			{_title_sort}:
		</p>
		<p class="f-sort__title f-sort__title--short">
			{_title_sort_short}:
		</p>
		<ul class="f-sort__list">

			{if $catalogOrder == 'bestseller'}
				<li class="f-sort__item is-active is-reversed">
					<span class="f-sort__link">
						{*{_best_sellers}*}
						{_sort_default}
					</span>
					{*<span class="f-sort__arrow">↓</span>*}
				</li>
			{else}
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{php unset($cleanFilterParamCopy['order'])}
				{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
				{php $linkBestSellers = urldecode(htmlspecialchars_decode($link))}
				<li class="f-sort__item">
					<a href="{$linkBestSellers}" class="f-sort__link" {if $linkSeo->hasNofollow($link, ['filter' => $cleanFilterParamCopy])}rel="nofollow"{/if}>
						{*{_best_sellers}*}
						{_sort_default}
					</a>
				</li>
			{/if}

			{if $catalogOrder == 'alphabetic'}

				<li class="f-sort__item">
					{php $cleanFilterParamCopy = $cleanFilterParam}
					{php $cleanFilterParamCopy['order'] = 'alphabeticBottom'}
					{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
					{php $linkMostExpensive = urldecode(htmlspecialchars_decode($link))}
					<a href="{$linkMostExpensive}" class="f-sort__link is-active" {if $linkSeo->hasNofollow($link, ['filter' => $cleanFilterParamCopy])}rel="nofollow"{/if}>
						{_sort_alphabetic}
						<span class="f-sort__arrow">&darr;</span>
					</a>
				</li>
			{else}
				<li class="f-sort__item">
					{php $cleanFilterParamCopy = $cleanFilterParam}
					{php $cleanFilterParamCopy['order'] = 'alphabetic'}
					{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
					{php $linkCheapest = urldecode(htmlspecialchars_decode($link))}
					<a href="{$linkCheapest}" class="f-sort__link{if $catalogOrder == 'alphabetic' || $catalogOrder == 'alphabeticBottom'} is-active{/if}" {if $linkSeo->hasNofollow($link, ['filter' => $cleanFilterParamCopy])}rel="nofollow"{/if}>
						{*{_cheapest}*}
						{_sort_alphabetic}
						{if $catalogOrder == 'alphabetic'}
							<span class="f-sort__arrow">&darr;</span>
						{elseif $catalogOrder == 'alphabeticBottom'}
							<span class="f-sort__arrow">&uarr;</span>
						{/if}
					</a>
				</li>
			{/if}


			{if $catalogOrder == 'cheapest'}
				{*<li class="f-sort__item is-active">*}
					{*<span class="f-sort__link">*}
						{*{_cheapest}*}
					{*</span>*}
					{*<span class="f-sort__arrow">↓</span>*}
				{*</li>*}
				{*TODO kelly - stav kdy je razeni zvoleno - musi mit stale link a byt zvirazneno*}
				<li class="f-sort__item">
					{php $cleanFilterParamCopy = $cleanFilterParam}
					{php $cleanFilterParamCopy['order'] = 'expensive'}
					{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
					{php $linkMostExpensive = urldecode(htmlspecialchars_decode($link))}
					<a href="{$linkMostExpensive}" class="f-sort__link is-active" {if $linkSeo->hasNofollow($link, ['filter' => $cleanFilterParamCopy])}rel="nofollow"{/if}>
						{*{_most_expensive}*}
						{_sort_price}
						<span class="f-sort__arrow">&darr;</span>
					</a>
				</li>

			{else}

				<li class="f-sort__item">
					{php $cleanFilterParamCopy = $cleanFilterParam}
					{php $cleanFilterParamCopy['order'] = 'cheapest'}
					{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParamCopy}{/capture}
					{php $linkCheapest = urldecode(htmlspecialchars_decode($link))}
					<a href="{$linkCheapest}" class="f-sort__link{if $catalogOrder == 'cheapest' || $catalogOrder == 'expensive'} is-active{/if}" {if $linkSeo->hasNofollow($link, ['filter' => $cleanFilterParamCopy])}rel="nofollow"{/if}>
						{_sort_price}
						{if $catalogOrder == 'cheapest'}
							<span class="f-sort__arrow">&darr;</span>
						{elseif $catalogOrder == 'expensive'}
							<span class="f-sort__arrow">&uarr;</span>
						{/if}
					</a>
				</li>
			{/if}
		</ul>
	</div>
</div>

