{varType SuperKoderi\Gtm $gtm}
{default $class = false}
{default $crossroad = []}
{default $hasVideo = false}
{varType Nextras\Dbal\Utils\DateTimeImmutable $dateTimeNow}

{php $crossroad = $crossroad ?? []}
{foreach $crossroad as $c}
	{if isset($c->video) && $c->video}
		{php $hasVideo = true}
		{breakIf $hasVideo = true}
	{/if}
{/foreach}

<section n:if="$crossroad" class="b-carousel {$class}">
	<div class="b-carousel__list grid grid--x-0 grid--y-0 grid--scroll" {if !$hasVideo}data-autoplay="true"{/if}>
		{foreach $crossroad as $key => $c}
			{continueIf App\Model\CustomField\CustomFieldHelper::isHideSlider($c, $dateTimeNow)}
			<div class="b-carousel__item grid__cell">
				<article class="b-carousel__inner{if isset($c->video) && $c->video} js-video{/if}" data-gtm-push='{$gtm->getOnDemandEvent('HP_Banner_' . $key)->render()}'> {* {"gtm": "testCarousel"} *}
					{if isset($c->img) && empty($c->video)}
						{* <div class="b-carousel__img img {if $isSubscriptionEnabled}img--banner{else}img--16-9{/if} img--fill"> *}
						<div class="b-carousel__img img img--banner img--fill">
							<span class="img__holder">
								{php $imgSm = $imageResizer->getImg($c->img->filename, 'sm')}
								{php $imgMd = $imageResizer->getImg($c->img->filename, 'md')}
								{php $imgLg = $imageResizer->getImg($c->img->filename, 'lg')}
								{php $imgXl = $imageResizer->getImg($c->img->filename, 'xl')}
								{if $iterator->first}
									<img
										srcset="
											{$imgSm->src} 350w,
											{$imgMd->src} 500w,
											{$imgLg->src} 900w,
											{$imgXl->src} 1300w"
										sizes="
											(min-width: 1300px) 1260px,
											100vw"
										src="{$imgXl->src}"
										alt=""
										fetchpriority="high"
									>
								{else}
									<img
										srcset="
											{$imgSm->src} 350w,
											{$imgMd->src} 500w,
											{$imgLg->src} 900w,
											{$imgXl->src} 1300w"
										sizes="
											(min-width: 1300px) 1260px,
											100vw"
										src="{$imgXl->src}"
										alt=""
										loading="lazy"
									>
								{/if}
							</span>
						</div>
					{/if}
					<div n:if="isset($c->video) && $c->video" class="b-carousel__video img img--16-9 js-video__video">
						{php $id = explode('=', $c->video)[count(explode('=', $c->video))-1]}
						<span class="img__holder">
							{php $ua = htmlentities($_SERVER['HTTP_USER_AGENT'], ENT_QUOTES, 'UTF-8')}
							{if (preg_match('~MSIE|Internet Explorer~i', $ua) || (strpos($ua, 'Trident/7.0; rv:11.0') !== false))}
								{* IE *}
								<iframe loading="lazy" src="//www.youtube.com/embed/{$id}?controls=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
							{else}
								{* Není IE *}
								<lite-youtube videoid="{$id}" params="controls=0"></lite-youtube>
							{/if}
						</span>
					</div>

					<div class="b-carousel__holder{if isset($c->align) && $c->align === 'right'} b-carousel__holder--right{/if} u-last-m0">
						<h2 n:if="!empty($c->title)" class="b-carousel__title">
							{$c->title}
						</h2>
						<p n:if="!empty($c->annot)" class="b-carousel__annot">
							{$c->annot}
						</p>
						<p n:if="($c->btnText ?? false) && (!empty($c->src) || !empty($c->srcSeoFilter->url) || !empty($c->srcMyUrl))" class="b-carousel__btn">
							<a href="{if !empty($c->srcMyUrl)}{$c->srcMyUrl}{elseif !empty($c->srcSeoFilter->url)}{$c->srcSeoFilter->url}{else}{plink $c->src}{/if}" class="btn link-mask" data-gtm-push-click="{$gtm->getOnDemandEvent('c_HP_Banner_' . $key)->render()}">
								<span class="btn__text">
									{$c->btnText}
								</span>
							</a>
						</p>
					</div>
				</article>
			</div>
		{/foreach}
	</div>
	<a href="#" class="b-carousel__prev js-carousel__prev">
		{('angle-left-thin')|icon}
		<span class="u-vhide">
			{_paging_prev}
		</span>
	</a>
	<a href="#" class="b-carousel__next js-carousel__next">
		{('angle-right-thin')|icon}
		<span class="u-vhide">
			{_paging_next}
		</span>
	</a>
</section>
