{default $class = 'u-mb-md'}
{default $step = 1}

<div class="b-steps u-bg--blue {$class}">
	<div class="row-main">
		<ol class="b-steps__list">
			<li class="b-steps__item {if $step == 1} is-active {/if}">
				{if $step > 1}
					<a n:href="'UID|basket'" class="b-steps__inner">
				{else}
					<span class="b-steps__inner">
				{/if}
					<span class="b-steps__text">
						{_shopping_basket}
					</span>
				</{if $step > 1}a{else}span{/if}>
			</li>
			<li class="b-steps__item {if $step == 2} is-active {/if}">
				{if $step == 1 || $step > 2}
					<a n:href="'UID|step1'" class="b-steps__inner">
				{else}
					<span class="b-steps__inner">
				{/if}
					<span class="b-steps__text">
						{_shipping_payment}
					</span>
				</{if $step == 1 || $step > 2}a{else}span{/if}>
			</li>
			<li class="b-steps__item {if $step == 3} is-active {/if}">
				{if $step == 2}
					<a {*n:href="{$pages->step2}"*} href="#" class="b-steps__inner js-form-submit" data-submit-target=".f-basket">
				{else}
					<span class="b-steps__inner">
				{/if}
					<span class="b-steps__text">
						{_personal_info}
					</span>
				</{if $step == 2}a{else}span{/if}>
			</li>
			<li class="b-steps__item {if $step == 4} is-active {/if}">
				<span class="b-steps__inner">
					<span class="b-steps__text">
						{_order_success}
					</span>
				</span>
			</li>
		</ol>
	</div>
</div>

{*
	<div class="s-std s-std--grey u-pt-0">
		<div class="row-main">
			<div class="b-steps is-active{$step}">
				<ul class="b-steps__list">
					<li class="b-steps__item">
						{if $step == 1}
							<strong class="b-steps__inner">{_title_step1}</strong>
						{else}
							<a n:href="{$pages->basket}" class="b-steps__inner is-prev">{_title_step1}</a>
						{/if}
					</li>
					<li class="b-steps__item">
						<span class="b-steps__icon icon icon--arrow-right"></span>
						{if $step == 2}
							<strong class="b-steps__inner">{_title_step2}</strong>
						{else}
							<a n:href="{$pages->step1}" class="b-steps__inner is-next">{_title_step2}</a>
						{/if}
					</li>
					<li class="b-steps__item">
						<span class="b-steps__icon icon icon--arrow-right"></span>
						{if $step == 2}
							<a n:href="{$pages->step2}" class="b-steps__inner is-next">{_title_step3}</a>
						{else}
							<span class="b-steps__inner">{_title_step3}</span>
						{/if}
						<span class="b-steps__inner"></span>
					</li>
				</ul>
			</div>
		</div>
	</div>
*}
