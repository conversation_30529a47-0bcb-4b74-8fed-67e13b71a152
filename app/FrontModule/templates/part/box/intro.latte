{default $page = $object}
{if $page->template != 'Article:detail' || ($page->template == 'Article:detail' && !$page->hideBigImage)}
	{if ($page->images && $page->images->count() > 0 &&
		( ( $page->hideFirstImage && $page->images && $page->images->count() > 1 ) || !$page->hideFirstImage ) )}

		<p class="b-intro u-mb-lg">
			{if $page->hideFirstImage}
				{foreach $page->images as $i}
					{if $iterator->getCounter() == 2}
						{php $imgBig = $imageResizer->getImg($i->filename, 'xl')}
						{php $img = $imageResizer->getImg($i->filename, 'xl-16-9')}
						<a href="{$imgBig->src}" class="img img--16-9" data-fancybox="gallery">
							<span class="img__holder">
								<img src="{$img->src}" alt="{$i->name}" fetchpriority="high">
							</span>
						</a>
					{/if}
					{breakIf $iterator->getCounter() == 2}
				{/foreach}
			{else}
				{php $imgBig = $imageResizer->getImg($page->firstImage->filename, 'xl')}
				{php $img = $imageResizer->getImg($page->firstImage->filename, 'xl-16-9')}
				<a href="{$imgBig->src}" class="img img--16-9" data-fancybox="gallery">
					<span class="img__holder">
						<img src="{$img->src}" alt="{$page->firstImage->name}" fetchpriority="high">
					</span>
				</a>
			{/if}
		</p>

	{/if}
{/if}
