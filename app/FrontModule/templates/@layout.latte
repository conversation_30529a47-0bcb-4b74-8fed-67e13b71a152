<!DOCTYPE html>
<html lang="{$lgCode}" class="no-js">
<head>
	<meta charset="utf-8">
	<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
	<meta name="author" content="{_author}">
	{if isset($seolink) && $seolink && $seolink->metaKey}
		<meta name="keywords" content="{$seolink->metaKey}">
	{elseif $object->keywords}
		<meta name="keywords" content="{$object->keywords}">
	{/if}

	{if isset($seolink) && $seolink && $seolink->metaDescription}
		<meta name="description" content="{$seolink->metaDescription}">

	{elseif $object->description || $object->annotation}
		<meta name="description" content="{if $object->description}{$object->description}{else}{$object->annotation|texy:true}{/if}">
	{/if}

	{if (isset($object->forceNoIndex) && $object->forceNoIndex) || (isset($seolink) && $seolink && $seolink->noIndex) || (isset($isGiftProduct) && $isGiftProduct)}
		<meta name="robots" content="noindex, follow">
	{else}
		<meta name="robots" content="{$robots}" n:ifset="$robots">
	{/if}

	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">

	<title n:snippet="title">
		{if isset($seolink) && $seolink && $seolink->metaTitle}
			{$seolink->metaTitle}
		{elseif isset($h1Text)} {* H1 po filtraci *}
			{$h1Text}
		{elseif isset($object->nameTitle)}
			{$object->nameTitle}
		{/if}
		{if $presenter->name == 'Front:Catalog' && isset($currentPage) && $currentPage > 1} {_page} {$currentPage} {_page_from} {$pagesCount} {/if}
		{if !$isHomepage} {* Přídavek za title, který se dává jen pro ne homepage stránky *}
			| {_title}
		{/if}
	</title>

	{* <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,400&display=swap"/> *}

	{include './style.latte', object=>$object}
	{control canonicalUrl}

	{* <link rel="dns-prefetch" href="https://www.google-analytics.com"> *}
	<link rel="dns-prefetch" href="https://www.googletagmanager.com"> {*{/gtm.js}*}
	{* <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin /> *}
	{* <link rel="preconnect" href="https://www.google.com" crossorigin> *}
	<link rel="preconnect" href="https://www.youtube.com" crossorigin>
	<link rel="preconnect" href="https://skyproxy.superkoders.rocks" crossorigin>
	<link rel="preconnect" href="https://connect.facebook.net" crossorigin>
	{* <link rel="preconnect" href="https://static.doubleclick.net" crossorigin> *}
	{* <link rel="preconnect" href="https://client.crisp.chat" crossorigin> *}
	<link n:foreach="$hrefLangs as $hrefLang => $href" rel="alternate" hreflang="{$hrefLang}" href="{$href}"/>

	{include 'part/meta.latte'}
	{include 'part/structured_data.latte'}
	{include 'part/tracking/gaHeader.latte'}

	{if isset($fbCode) && $presenter->name == 'Front:Homepage'}
	<meta name="facebook-domain-verification" content="{$fbCode}" />
	{/if}

	{*if isset($crispSiteId) && $crispSiteId}
		<script type="text/javascript">window.$crisp=[];window.CRISP_WEBSITE_ID="23c479f5-e815-4792-8069-b5ed3abf1ad2";(function(){ d=document;s=d.createElement("script");s.src="https://client.crisp.chat/l.js";s.async=1;d.getElementsByTagName("head")[0].appendChild(s);})();</script>
	{/if*}
	{var $scripts = [
		['/static/js/jquery-3.7.1.min.js?t=' . $webVersion],
		['/static/js/app.js?t=' . $webVersion],
	] }
	{* ['https://cdn.polyfill.io/v2/polyfill.min.js?features=default,Array.prototype.includes,Object.values,Array.prototype.find,Array.prototype.indexOf,IntersectionObserver,AbortController,Promise', 'sha384-MkzMfol4gHD02or3FY09Z50QMRiGpMftT2AaNtbkRq3L64rwfSDz0DnN/xdmF0jW'], *}
	{foreach $scripts as $script}
		<link rel="preload" as="script" href="{$script[0]}"{if isset($script[1])} integrity="{$script[1]}" crossorigin="anonymous"{/if}>
	{/foreach}

	{* <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,400&display=swap" rel="stylesheet" media="print" onload="this.media='all'"> *}

	{* LCP preload *}
	{if $object->template == 'Homepage:default'}
		{if isset($object->cf->carousel)}
			{foreach $object->cf->carousel as $c}
				{if isset($c->video) && $c->video}
					{php $id = explode('=', $c->video)[count(explode('=', $c->video))-1]}
					<link rel="preload" href="https://i.ytimg.com/vi/{$id}/sddefault.jpg" as="image">
				{/if}
			{/foreach}
		{/if}
	{/if}
	{if in_array($mutation->langCode, ['sk', 'cs']) && $presenterName == 'Order'}
		<script src="https://widget.packeta.com/v6/www/js/library.js"{* integrity="sha384-UWrBXVfwNMRV0lDhnzqbUAohi2drWYXbqVbpAaF7cKaVMVo3erpuVMd0f38FTUu8" crossorigin="anonymous"*}></script>
	{/if}
	<script>
		(function () {
			var root = document.documentElement;
			var className = document.documentElement.className;
			className = className.replace('no-js', 'js');

			var mediaHover = window.matchMedia('(hover: none), (pointer: coarse), (pointer: none)');
			mediaHover.addListener(function(media) {
				document.documentElement.classList[media.matches ? 'add' : 'remove']('no-hoverevents');
				document.documentElement.classList[!media.matches ? 'add' : 'remove']('hoverevents');
			});
			className += (mediaHover.matches ? ' no-hoverevents' : ' hoverevents');

			var supportsCover = 'CSS' in window && typeof CSS.supports === 'function' && CSS.supports('object-fit: cover');
			className += (supportsCover ? ' ' : ' no-') + 'objectfit';

			// fix iOS zoom issue: https://docs.google.com/document/d/1KclJmXyuuErcvit-kwCC6K2J7dClRef43oyGVCqWxFE/edit#heading=h.sgbqg5nzhvu9
			var ua = navigator.userAgent.toLowerCase();
			var isIOS = /ipad|iphone|ipod/.test(ua) && !window.MSStream;

			if (isIOS === true) {
				var viewportTag = document.querySelector("meta[name=viewport]");
				viewportTag.setAttribute("content", "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no");
			}

			document.documentElement.className = className;
		}());

		// Cookie head script
		(function () {
			var match = document.cookie.match(new RegExp('(^| )SKcookieConsent=([^;]+)'));
			document.documentElement.dataset.showCookie = true;
			if (match) {
				try {
					var cookieState = JSON.parse(match[2]);
					if(cookieState.id && cookieState.datetime && cookieState.storages) {
						document.documentElement.dataset.showCookie = false;
						window.cookieState = cookieState;
					};
				} catch (error) {}
			}
		}());
	</script>
</head>
<body>

{include 'part/tracking/gaBody.latte'}
{include 'part/menu/accessibility.latte'}

<div n:snippet="gtmEvents">
	{include 'part/tracking/gtm_events.latte'}
</div>

{capture $abandonedHtml}
	{spaceless}
		{*MUSI se vyrenderovat prvni pred ostatníma kosikama*}
		{if isset($renderAbandonedBasket) && $renderAbandonedBasket && isset($abandonedOrder)}
		<div class="u-pt-lg u-mb-lg u-js-hide">
			<div class="row-main">
				data-cookie-name="abandoned-basket" data-cookie-days="1" data-cookie-value="1"
				<div id="popup-abandoned-basket" class="js-fancybox-open-after-load">
					<div class="u-min-max-width--4-12">
						{control orderBasket:abandoned, $abandonedOrder}
					</div>
				</div>
			</div>
		</div>
		{/if}
	{/spaceless}
{/capture}

<div class="mother">
	{include 'part/box/systmsgInternal.latte'}
	{include 'part/box/userBasedMessages.latte'}
	{include 'part/box/systmsg.latte'}


	<div n:tag-if='$object->template == "Page:landingAnniversary" || $object->template == "Page:landingTickets"' class="u-mb-sm">
		{include 'part/box/main-benefits.latte'}
		{snippetArea headerArea} {* Označení bloku, ve kterém jsou snippety *}
			{include 'part/header.latte'}
		{/snippetArea}
	</div>

	<main id="main" class="main {if $object->template == 'Homepage:default'}u-pt-xs{else}{if $object->template != 'Page:landingAnniversary' || $object->template != 'Page:landingTickets'}u-pt-xxs{/if}{/if} {if $object->template == 'Page:landing'}p-landing-page{/if}{if $object->template == 'Page:landingTwo'}p-landing-page p-landing-page--two{/if}{if $object->template == 'Page:landingAnniversary'}p-landing-anniversary{/if}{if $object->template == 'Page:landingTickets'}p-landing-tickets{/if}{if $object->template == 'Page:landingSubscription'}p-landing-subscription{/if}{if $object->template == 'Page:landingRockets'}p-landing-rockets{/if}{if $object->template == 'Page:lottery'}p-landing-lottery{/if}">
		{*{snippet content}*}
			{include #content}
		{*{/snippet}*}
	</main>
	{include 'part/menu/mobile.latte'}
	{include 'part/box/adminLink.latte'}
	{include 'part/box/cookie.latte'}

	<div n:if="$showLangPopup" class="u-pt-md u-mb-md u-hide">
		{include './part/modal/lang.latte', id=>'popup-lang', customTitle=>'title_modal_lang'}
	</div>

	{include 'part/footer.latte'}
</div>

{if isset($abandonedHtml) && $abandonedHtml}
	{$abandonedHtml}
{/if}

<div class="body-loader"></div>
{* <img n:if="$qrCookie" src="{$qrCookie}/qr-m/{$mutation->id}" style="display:none;"> *}

{foreach $scripts as $script}
	<script src="{$script[0]}"{if isset($script[1])} integrity="{$script[1]}" crossorigin="anonymous"{/if}></script>
{/foreach}

{if in_array($mutation->langCode, ['sk', 'cs']) && $presenterName == 'Order'}
	<script src="/static/js/cookie.js"></script>
{/if}

<script>
	{* (function() {
		var root = document.documentElement;
		var scrollbarWidth = window.outerWidth - root.clientWidth;
		root.style.setProperty('--scrollbar-width', scrollbarWidth + 'px');
	}()); *}
	App.run({
		countryCode: {if $mutation->langCode == 'ae'}'AE'{else}{(explode('-', $mutation->langMenu)[0] ?? 'EN')}{/if},
		apiKey: {$googleApiKey},
		{* apiKey: 'AIzaSyAKq5o-C_3qMY5i3J5cuyyRrZ0CJmuy050', *} {* SK api key *}
		assetsUrl: {$basePath}+"static/",

		filterType: {
			vet: {isset($_GET['veterinary']) && $_GET['veterinary'] ? 'vet' : null},
			shop: {isset($_GET['shop']) && $_GET['shop'] ? 'shop' : null},
		},
		filterSearch: {
			city: {isset($_GET['search']) && $_GET['search'] ? $_GET['search'] : null},
		},

		itemsPerPage: 10,
		{if $object->uid == 'whereToBuy'}
		markers: [
			{foreach $places as $place}
				{if $place->lat && $place->lon}
				{
					id: {$iterator->getCounter()},
					type: {if $place->type == "Obchod" || $place->type == "obchod" || $place->type == "Pet Store" || $place->type == "pet"}1{elseif $place->type == "Veterinář" || $place->type == 'Veterinár' || $place->type == 'Veterinary Clinic' || $place->type == "vet"}2{elseif $place->type == "Uloženka"}3{else}0{/if},
					name: {$place->name},
					address: {$place->street} + ', ' {if $place->city}+{$place->city}+ ' '{/if}{if $place->zip}+{$place->zip}{/if},
					position: { "lat": {$place->lat}, "lng": {$place->lon} },
					tel: {$place->phone},
					opened: {$place->openTime},
					email: {$place->email},
					web: {$place->webLink},
				},
				{/if}
			{/foreach}
		],
		{/if}
	});
</script>
</body>
</html>
