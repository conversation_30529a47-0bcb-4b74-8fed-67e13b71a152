{block content}
	<div class="row-main">
		{include '../part/crossroad/categories.latte', title=>'title_hp_categories', class=>'c-categories--lg u-mb-xs', hp=>true}
		{snippet animalFilter}
		{if $user->loggedIn && ($mutation->isEshop || isset($_COOKIE['CLIENT_TESTING']))}
			{control animalFilter}
		{/if}
		{/snippet}

		{* TODO - box "Darek zdarma" - jeste neobjednal žádný vzorek zdarma*}
		{*
		{if $user->loggedIn && $mutation->isEshop && $userEntity->sampleBoughtFreeCount == 0}
			...
		{/if}
		*}


		{php $allowToShowBox = true}
		{if ($mutation->isEshop || isset($_COOKIE['CLIENT_TESTING'])) && (($user->loggedIn && $userEntity->animals->count() > 1))}
			{php $allowToShowBox = false}
		{/if}

		{if ($mutation->isEshop || isset($_COOKIE['CLIENT_TESTING']))}
			{php $allowToShowBox = false}
		{/if}

		<div class="b-hp-grid{if ($mutation->isEshop || isset($_COOKIE['CLIENT_TESTING'])) && $allowToShowBox} b-hp-grid--logged{/if}">
			{include '../part/box/carousel.latte', class=>'b-hp-grid__carousel', crossroad => $object->cf?->carousel ?? []}
			{if $isSubscriptionEnabled}
				<div class='b-hp-grid__guide'>
					<div class='b-hp-grid__item'>
						<div class="b-hp-grid__banner">
							<img src="{$domainUrl}/static/img/illust/calibra-repeat-{$mutation->langCode}.jpg" alt="Calibra Repeat" width="820" height="470" />
							<div class="b-hp-grid__banner-inner">
								<a href="{plink 'UID|subscription-landing'}" class="btn btn--small btn--white link-mask">
									<span class="btn__text">
										{_find_more}
									</span>
								</a>
							</div>
						</div>
					</div>
					<div class='b-hp-grid__item'>
						{include '../part/form/guideStart.latte', class=>'u-mb-lg'}
					</div>
				</div>
			{else}
					{include '../part/form/guideStart.latte', class=>'b-hp-grid__guide u-mb-lg'}
			{/if}

			{if $mutation->isEshop || isset($_COOKIE['CLIENT_TESTING'])}
				{* bestseller*}
				{if isset($bestsellers) && $bestsellers->count()}
					{include '../part/attached/products.latte', class=>'b-hp-grid__bestseller u-mb-md', products=>$bestsellers, customTitle=>title_bestsellers, useSelectVariants=>true}
				{/if}

				{*pro eshop vždy zobrazime nejaky box*}
				{if $user->loggedIn}
					{if $userEntity->animals->count() == 0} {*nemá zvíře*}
						{include '../part/box/shadow.latte', wide=>true, class=>'b-hp-grid__news'}
						{*include '../part/box/hp-add-pet.latte', class=>'b-hp-grid__gift u-mb-md'*}{* TMP SK hidden *}
					{else}
						{include '../part/box/shadow.latte', wide=>true, class=>'b-hp-grid__news'}
						{*nove: skryto, tedy clanky na celou stranku, viz podminka vyse :-( *}
						{*box na info o vzorích, směřujíích do filtru*}
{*						{include '../part/box/hp-free-sample.latte', class=>'b-hp-grid__gift u-mb-md'}*}
					{/if}
				{else}
					{if in_array($mutation->langCode, ['sk', 'cs', 'pl', 'ro'], true)}
						{include '../part/box/shadow.latte', wide=>true, class=>'b-hp-grid__news'}
					{else}
						{include '../part/box/shadow.latte', wide=>false, class=>'b-hp-grid__news'}
						{include '../part/box/hp-register.latte', class=>'b-hp-grid__gift u-mb-md'}
					{/if}
				{/if}

			{else}
				{include '../part/box/shadow.latte', wide=>true, class=>'b-hp-grid__news'}
			{/if}
		</div>

		{include '../part/crossroad/lines.latte'}

		<div class="u-max-width--8-12 u-mx-auto">
			{include '../part/box/content.latte'}
			{include '../part/attached/files.latte'}
			{include '../part/attached/links.latte'}
			{include '../part/attached/gallery.latte'}
		</div>
		{include '../part/attached/pages.latte', crossroad=>$object->pages}
		{include '../part/attached/products.latte'}
		{include '../part/box/benefits.latte'}
	</div>
{/block}
