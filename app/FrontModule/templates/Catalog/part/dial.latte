{dump "box"}
{dump $box}
<fieldset class="f-filter__group{if $name == 'animalType' || $name == 'dogSize'} f-filter__group--inline{/if} {*js-filter*} js-open {if true || isset($filter->openBox['dial-'.$name]) || $box->isOpen() || $isOpenInCookie($name)}is-open{/if}" data-internaluid="{$name}">
	<p class="f-filter__title">
		<a href="#" class="f-filter__name js-open__link">
			<strong>
				{*{if isset($title)}*}
					{*{translate}pname_{$title}{/translate}*}
				{*{else}*}
					{*{translate}filter_title_{$name}{/translate}*}
				{*{/if}*}
{*				{translate}pname_{$allParameters[$name]->id}{/translate}*}
{*				{$box->getName()}...*}
				{_$box->title}
			</strong>
			{('angle-down')|icon}
		</a>
		{*{if isset($tooltips[$name]) && $tooltips[$name]}*}
		{*<a href="#" class="f-filter__info tooltip js-etarget" aria-label="{_help}" data-title="{$tooltips[$name]}">*}
			{*{('question-polygon')|icon}*}
		{*</a>*}
		{*{/if}*}
	</p>
	<div class="f-filter__inner js-open__content {if isset($filter->openBox['dial-'.$name]) || $box->isOpen() || $isOpenInCookie($name)}is-open{/if}">
		{if $name == 'dogSize'}
			{* vyjímka pro velikost psa *}
			<div class="b-breed">
				<div class="b-breed__list-wrap">
					<ul class="b-breed__list grid">
						{foreach $box->getItems() as $value}

							{capture $link}{link 'this', filter => $value->followingFilterParameters, 'pager-page' => null}{/capture}
							{php $link = urldecode(htmlspecialchars_decode($link))}

							{if !isset($value->isChecked) || !isset($value->count) ||
								(isset($value->count) && $value->count == 0 && isset($value->isChecked) && !$value->isChecked)}
								{php $disabled = true}
							{else}
								{php $disabled = false}
							{/if}
							<li class="b-breed__item grid__cell grid__cell--eq size--4-12">
								<label class="b-breed__checkbox inp-item inp-item--checkbox inp-item--icon{if $disabled} inp-item--disabled{/if}">

									<input type="checkbox" name="{$value->inputName}" value="{$value->inputValue}" class="b-breed__inp" {if !$value->isChecked && $value->count == 0} disabled{/if} {if isset($value->isChecked) && $value->isChecked} checked="checked"{/if}>

									<span>
										{('check')|icon}
{*										{if $value->entity->alias == 'small'}*}
										{if $value->inputValue == 6559}
											{include '../../part/svg/breed-small.svg'}
{*										{elseif $value->entity->alias == 'medium'}*}
										{elseif $value->inputValue == 6592}
											{include '../../part/svg/breed-medium.svg'}
{*										{elseif $value->entity->alias == 'large'}*}
										{elseif $value->inputValue == 6387}
											{include '../../part/svg/breed-big.svg'}
										{/if}
{**}
										<a class="b-breed__link" href="{$link}" {if $linkSeo->hasNofollow($link, ['filter' => $value->followingFilterParameters])}rel="nofollow"{/if}>
											<strong>
												{translate}pvalue_{$value->inputValue}{/translate}
{*												{$value->name}*}
												{if $unit} {$unit}{/if}
											</strong>
{*											{ifset $value->annotation}*}
												<br>{translate}pvalue_short_{$value->inputValue}{/translate}
{*											{/ifset}*}
										</a>
									</span>
								</label>
							</li>
						{/foreach}
					</ul>
				</div>
				{*
				<p class="b-breed__link-wrap">
					<a href="#" class="b-breed__link">
						{_btn_select_breed}
					</a>
				</p>
				*}
			</div>
		{else}
			<div class="inp-items{if $name == 'animalType'} inp-items--inline{/if}">
				{foreach $box->getItems() as $value}

				{capture $link}{link 'this', filter => $value->followingFilterParameters, 'pager-page' => null}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}

					{first}<ul class="f-filter__list inp-items__list{*f-filter__list{if $name=='color'} f-filter__list--inline{/if}*}">{/first}

						{* Skip virtual flags that are not set right now in Flag settings *}
						{continueIf !isset($flagEntitiesByVirtualFlagId[$value->inputValue]) && $box->name === 'virtualFlag'}

						{*TODO: refactor - to model*}
						<li class="f-filter__item inp-items__item{*f-filter__item*}">
							{if !isset($value->isChecked) || !isset($value->count) ||
									(isset($value->count) && $value->count == 0 && isset($value->isChecked) && !$value->isChecked)}
								{php $disabled = true}
							{else}
								{php $disabled = false}
							{/if}
							<label class="inp-item inp-item--checkbox{if $disabled} inp-item--disabled{/if}" >
								<input type="checkbox" name="{$value->inputName}" value="{$value->inputValue}" data-action="change->FilterGroup#submitForm" {if !$value->isChecked && $value->count == 0} disabled{/if} {if isset($value->isChecked) && $value->isChecked} checked{/if}>
								<span>
									{('check')|icon}
									{if !isset($flagEntitiesByVirtualFlagId[$value->inputValue])}
										<a href="{$link}" class="f-filter__link js-filter__checkbox"{if $linkSeo->hasNofollow($link, ['filter' => $value->followingFilterParameters])}rel="nofollow"{/if} >
											{$value->name}{*translate}pvalue_{$value->inputValue}{/translate*}{if $unit} {$unit}{/if}
										</a>
									{else}
										{var $flag = $flagEntitiesByVirtualFlagId[$value->inputValue]}
										{capture $flagName}{translate}pvalue_{$value->inputValue}{/translate}{/capture}
										{include '../../part/core/flag.latte', link=>$link, name=>$flagName, color=>$flag->color, class=>'js-filter__checkbox', lg=>false}
									{/if}
								</span>
							</label>
						</li>
					{last}</ul>{/last}
				{/foreach}
			</div>
		{/if}
	</div>
</fieldset>

