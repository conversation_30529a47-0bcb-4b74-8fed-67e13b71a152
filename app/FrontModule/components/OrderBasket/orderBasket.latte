
{default $class = 'u-mb-spec'}
{default $isSubscriptionEnabled = false}

<form n:name="form" class="f-basket {$class} js-ajax-form">
	{control messageForForm, $flashes, $form, TRUE, ['class'=>'f-basket__message']}

	{*TODO - ma to tu byt? - doplnil jsem skutecnou honotu $freeFrom*}
	<p n:if="$basket->isFreeTransportNotice()" class="f-basket__message message message--sm u-mb-md">
		{capture $price}{$basket->getTransportFreeFrom()-$basket->getProductsPriceDPH()|priceFormat}{/capture}
		{_free_shipping_remaining|replace:'%s',(string)$price}
		<a href="#" class="message__close">
			{('cross')|icon}
			<span class="u-vhide">
				{_close}
			</span>
		</a>
	</p>
	{* produkty a slevy *}
	{if $isSubscriptionEnabled}
		{include FE_TEMPLATE_DIR . '/part/crossroad/products-row-subscription-enabled.latte', form=>$form}
	{else}
		{include FE_TEMPLATE_DIR . '/part/crossroad/products-row.latte', form=>$form}
	{/if}

	{* dárky *}
	{if $isSubscriptionEnabled && !$basket->containsSubscriptionProducts()}
		{include FE_TEMPLATE_DIR . '/part/crossroad/gifts.latte', class=>'u-mb-xxs'}
	{/if}

	<div class="f-basket__nav aaa">
		<div class="grid">
			<div class="grid__cell size--6-12@xl order--2@md">
				{if $isSubscriptionEnabled && $basket->containsSubscriptionProducts()}
					<p class="f-basket__summary">
						<span>
							{_order_price_per_part_subscription}
						</span>
						<strong class="f-basket__price">
							{$order->totalSubscriptionPriceItemOnlyDPH|priceFormat}
						</strong>
					</p>
					<p class="f-basket__summary">
						<span>
							{_order_price_per_part_one_time}
						</span>
						<strong class="f-basket__price">
							{$order->totalOneTimePriceItemOnlyDPH|priceFormat}
						</strong>
					</p>
					<p class="f-basket__summary">
						<span>
							{_order_price_total}
						</span>
						<strong class="f-basket__price f-basket__price--big">
							{$order->totalPriceItemOnlyDPH|priceFormat}
						</strong>
					</p>
				{else}
					<p class="f-basket__summary">
						<span>
							{_price_total}
						</span>
						<strong class="f-basket__price">
							{$order->totalPriceItemOnlyDPH|priceFormat}
						</strong>
					</p>
				{/if}

				{*include FE_TEMPLATE_DIR . '/part/box/coupon.latte'*}

				<p class="f-basket__next">
					<button type="submit" n:name="continue2" value="send" class="f-basket__btn btn btn--secondary btn--arrow btn--big btn--xl btn--loader">
						<span class="btn__text">
							<span class="item-icon item-icon--after">
								{_continue_next}
								{('angle-right')|icon}
							</span>
						</span>
					</button>
				</p>
			</div>
			<div class="grid__cell size--6-12@xl order--1@md">
				<div class="b-subscription-ad" n:if="$isSubscriptionEnabled">
					<div class="b-subscription-ad__content">
						<p class="b-subscription-ad__logo">
							<img src="/static/img/logo-ad.png" width="193" height="31" alt="" loading="lazy">
						</p>
						<div class="b-subscription-ad__text">
							<p>
								{_ad_subscription_text|noescape}
							</p>
							<ul>
								<li>
									{_ad_subscription_1|noescape}
								</li>
								<li>
									{_ad_subscription_2|noescape}
								</li>
								<li>
									{_ad_subscription_3|noescape}
								</li>
							</ul>
						</div>
						<p class="b-subscription-ad__button">
							<a href="{plink 'UID|subscription-landing'}" class="btn btn--border btn--small">
								<span class="btn__text">
									{_ad_subscription_btn}
								</span>
							</a>
						</p>
					</div>
				</div>
				<p class="f-basket__prev">
					<a href="{plink 'UID|eshop'}" class="f-basket__link">
						{_btn_continue_shopping}
					</a>
				</p>
			</div>
		</div>
	</div>

</form>
