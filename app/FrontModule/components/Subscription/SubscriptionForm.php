<?php declare(strict_types = 1);

namespace SuperKoderi\Components;

use App\Model\Mutation;
use App\Model\Subscription;
use App\Model\SubscriptionModel;
use App\Model\SubscriptionOrder;
use App\Model\SubscriptionOrderAddress;
use App\Model\SubscriptionOrderModel;
use App\Model\Tree;
use App\Model\User;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Forms\Control;
use Nette\Forms\Form;
use Nette\Http\Session;
use Nette\Utils\ArrayHash;
use SuperKoderi\EasyMessages;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\TranslatorDB;
use Throwable;
use Tracy;
use function array_column;

class SubscriptionForm extends UI\Control
{

	use hasMessageForFormComponentTrait;

	private Tree $object;

	private array $allowedFutureIntervals = [];

	public function __construct(
		private readonly TranslatorDB $translator,
		Session $session,
		private readonly SubscriptionModel $subscriptionModel,
		private readonly SubscriptionOrderModel $subscriptionOrderModel,
		private Mutation $mutation,
		private readonly EasyMessages $easyMessages,
		private readonly ISubscriptionCancelFormFactory $subscriptionCancelFormFactory,
		private Subscription|null $subscription = null,
		private readonly User|null $user = null,
		private readonly SubscriptionOrder|null $subscriptionOrder = null,
	)
	{
		$this->allowedFutureIntervals = $this->processIntervalsForChange();
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->subscriptionOrder = $this->subscriptionOrder;
		$this->template->subscription = $this->subscription;
		$this->template->allowedFutureIntervals = $this->allowedFutureIntervals;
		$this->template->chosenTransportType = $this->subscriptionOrder instanceof SubscriptionOrder && $this->subscriptionOrder->address instanceof SubscriptionOrderAddress ? $this->subscriptionOrder->address->transportType : $this->subscription->transportType;
		$this->template->render(__DIR__ . '/subscriptionForm.latte');
	}

	/**
	 * Edit form factory.
	 */
	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$states = $this->mutation->getStates();

		if ($this->subscriptionOrder === null) {
			$form->addText('name', 'subscription_name')
				->setRequired('subscription_name_required');

			$form->addText('interval', 'subscription_interval')->setRequired(false);
			$options = [
				2 => '2 ' . $this->translator->translate('weeks_2_4'), 3 => '3 ' . $this->translator->translate('weeks_2_4'), 4 => '4 ' . $this->translator->translate('weeks_2_4'), 5 => '5 ' . $this->translator->translate('weeks_5'), 6 => '6 ' . $this->translator->translate('weeks_5'), 7 => '7 ' . $this->translator->translate('weeks_5'), 8 => '8 ' . $this->translator->translate('weeks_5'), 9 => '9 ' . $this->translator->translate('weeks_5'), 10 => '10 ' . $this->translator->translate('weeks_5'), 11 => '11 ' . $this->translator->translate('weeks_5'), 12 => '12 ' . $this->translator->translate('weeks_5'),
			];

			$form->addSelect('intervalSelect', 'subscription_interval', $options)
				->setPrompt('choose_interval');
		}

		$transportsArrayHash = $this->subscription->mutation->subscriptionTransportsArray;
		$transportArray = [];
		foreach ($transportsArrayHash as $key => $transport) {
			$transportArray[$key] = $transport->name;
		}
		$form->addRadioList('transportType', 'transport', $transportArray)
			->setRequired('messageErrorStep1');

		$paymentsArray = array_column($this->mutation->subscriptionPaymentsArray, 'name', 'key');
		$subPaymentsArray = array_column($this->mutation->subscriptionSubTypePaymentsArray, 'name', 'key');
		$form->addSelect('paymentType', 'payment', $paymentsArray)->setRequired('payment')->setDisabled(true);
		$form->addSelect('subPaymentType', 'subPaymentType', $subPaymentsArray)->setRequired('subPaymentType')->setDisabled(true);

		$form->addText('zasilkovnaId', 'zasilkovnaId')->setRequired(false);
		$form->addHidden('pplParcelCode');
		$form->addHidden('transportInfoText');
		$form->addSelect('state', 'state', $states)->setRequired()->setDefaultValue($this->mutation->countryDefault);

		$form->addText('firstname', 'name')->setRequired();
		$form->addText('lastname', 'surname')->setRequired();
		$form->addText('phone', 'phone')->setRequired();
		$form->addText('street', 'street')->setRequired();
		$form->addText('city', 'city')->setRequired()->setDefaultValue('');
		$form->addInteger('zip', 'zip')->setRequired();

		$form->addCheckbox('deliveryTab', 'is_address_different');
		// phpcs:ignore
		/** @var Control $deliveryTab */
		$deliveryTab = $form['deliveryTab'];

		$form->addText('ic', 'ic');
		$form->addText('dic', 'dic');
		$form->addText('company', 'company');

		$form->addText('dFirstname', 'name')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dLastname', 'surname')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dCompany', 'company');

		$form->addText('dStreet', 'street')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dCity', 'city')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dZip', 'zip')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addSelect('dState', 'state', $states)->setDefaultValue($this->mutation->countryDefault);
		$form->addText('dInfo', 'info');
		$form->addText('dPhone', 'phone');

		if ($this->subscriptionOrder)  {
			$form->addDate('orderDate', 'subscription_next_order_date')
				->addRule(Form::RANGE, 'orderDate_range', [date('Y-m-d', strtotime('+3 days')), date('Y-m-d', strtotime('+3 months'))]);
		}

		$form->addTextArea('infotext', 'note');

		//$form->addCheckbox('orderNow', 'subscription_order_now');

		$form->addSubmit('save', 'btn_save_subscription_settings');

		bd($this->subscription);
		$interval = $this->subscription?->interval;
		$form->setDefaults([
			'firstname' => $this->subscription->firstname,
			'lastname' => $this->subscription->lastname,
			'street' => $this->subscription->street,
			'city' => $this->subscription->city,
			'zip' => $this->subscription->zip,
			'phone' => $this->subscription->phone,
			'state' => $this->subscription->state,
			'name' => $this->subscription?->name,
			//'interval' => $this->subscription?->interval,
			'intervalSelect' => $this->subscription?->interval,
			'transportInfoText' => $this->subscription?->transportInfoText,
			'transportType' => $this->subscription?->transportType,
			'paymentType' => $this->subscription->paymentType,
			'subPaymentType' => $this->subscription->subPaymentType,
			'pplParcelCode' => $this->subscription?->pplParcelCode,
		]);

		if ($this->subscriptionOrder !== null) {
			$form->setDefaults([
				'orderDate' => $this->subscription->openSubscriptionOrder->payOrderAfter,
			]);
		}

		if ($this->subscriptionOrder?->address instanceof SubscriptionOrderAddress) {
			$form->setDefaults([
				'firstname' => $this->subscriptionOrder->address->firstname,
				'lastname' => $this->subscriptionOrder->address->lastname,
				'street' => $this->subscriptionOrder->address->street,
				'city' => $this->subscriptionOrder->address->city,
				'zip' => $this->subscriptionOrder->address->zip,
				'phone' => $this->subscriptionOrder->address->phone,
				'state' => $this->subscriptionOrder->address->state,
				'transportType' => $this->subscriptionOrder->address->transportType,
				'transportInfoText' => $this->subscriptionOrder->address->transportInfoText,
				'pplParcelCode' => $this->subscriptionOrder->address->pplParcelCode,
				'zasilkovnaId' => $this->subscriptionOrder->address->zasilkovnaId,
				'dFirstname' => $this->subscriptionOrder->address->dFirstname,
				'dLastname' => $this->subscriptionOrder->address->dLastname,
				'dStreet' => $this->subscriptionOrder->address->dStreet,
				'dCity' => $this->subscriptionOrder->address->dCity,
				'dZip' => $this->subscriptionOrder->address->dZip,
				'dPhone' => $this->subscriptionOrder->address->dPhone,
				'dState' => $this->subscriptionOrder->address->dState,
				'dCompany' => $this->subscriptionOrder->address->dCompany,
				'company' => $this->subscriptionOrder->address->company,
				'ic' => $this->subscriptionOrder->address->ic,
				'dic' => $this->subscriptionOrder->address->dic,
			]);
		} else {
			$form->setDefaults([
				'firstname' => $this->subscription?->firstname,
				'lastname' => $this->subscription?->lastname,
				'street' => $this->subscription?->street,
				'city' => $this->subscription?->city,
				'zip' => $this->subscription?->zip,
				'phone' => $this->subscription?->phone,
				'state' => ($this->user && $this->user->state !== '') ? $this->user->state : $this->subscription->mutation->countryDefault,
				'transportType' => $this->subscription?->transportType,
				'transportInfoText' => $this->subscription?->transportInfoText,
				'pplParcelCode' => $this->subscription?->pplParcelCode,
				'zasilkovnaId' => $this->subscription?->zasilkovnaId,
				'dFirstname' => $this->subscription?->dFirstname,
				'dLastname' => $this->subscription?->dLastname,
				'dStreet' => $this->subscription?->dStreet,
				'dCity' => $this->subscription?->dCity,
				'dInfo' => $this->subscription?->dInfo,
				'dZip' => $this->subscription?->dZip,
				'dPhone' => $this->subscription?->dPhone,
				'dState' => ($this->user && $this->user->dState !== '') ? $this->user?->dState : $this->subscription->mutation->countryDefault,
				'dCompany' => $this->subscription?->dCompany,
				'company' => $this->subscription?->company,
				'ic' => $this->subscription?->ic,
				'dic' => $this->subscription?->dic,
			]);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];
		//$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}


	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

//	public function formValidate(UI\Form $form, ArrayHash $values): void
//	{
//		if (isset($values->intervalSelect)) {
//			if ($values->intervalSelect !== 0 && $values->intervalSelect !== null) {
//				$values->interval = $values->intervalSelect;
//			} else {
//				$values->interval = (int) $values->interval;
//				$values->intervalSelect = (int) $values->interval;
//			}
//		}
//
//		if ($values->orderNow !== true &&  in_array($values->interval, $this->allowedFutureIntervals) === false) {
//			$this->template->showOrderNowCheckbox = true;
//			$form->addError('subscription_new_interval_is_in_the_past');
//			$form['orderNow']->setRequired();
//			if ($this->presenter->isAjax()) {
//				$this->redrawControl();
//			}
//		}
//	}

	private function processIntervalsForChange(): array
	{
		$intervals = [];
		foreach (Subscription::INTERVALS as $interval => $translation) {
			if ($this->subscription->status != Subscription::STATUS_ACTIVE || $this->subscriptionOrderModel->isNewIntervalDateInFuture($this->subscription->openSubscriptionOrder, $interval)) {
				$intervals[] = $interval;
			}
		}

		return $intervals;

	}

	/**
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			if ($this->subscriptionOrder instanceof SubscriptionOrder) {
				$this->processNextOrderSettings($form, $values);
			} else {
				if (empty($values->interval) && $values->intervalSelect === null) {
					$values->interval = $this->subscription->interval;
				}
				if (isset($values->intervalSelect)) {
					if ($values->intervalSelect !== 0 && $values->intervalSelect !== null) {
						$values->interval = $values->intervalSelect;
					} else {
						$values->interval = (int) $values->interval;
						$values->intervalSelect = (int) $values->interval;
					}
				}
				$this->processSubscriptionSettings($form, $values);
			}
			$this->flashMessage('subscription_settings_saved', 'success');

		} catch (Throwable $e) {
			$this->flashMessage('Error', 'error');
			Tracy\Debugger::log($e, Tracy\ILogger::ERROR);
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->redrawControl();
		}
	}

	private function processSubscriptionSettings(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		if ($this->subscription && $this->subscription->interval !== (int) $values->interval) {
			$this->subscriptionModel->changeDatesForSubscription($this->subscription, (int) $values->interval);
			$this->flashMessage('subscription_interval_has_been_changed', 'ok');
		}

		if (!$values->deliveryTab) {
			unset($valuesAll['dStreet']);
			unset($valuesAll['dName']);
			unset($valuesAll['dCity']);
			unset($valuesAll['dInfo']);
			unset($valuesAll['dZip']);
			unset($valuesAll['dState']);
			unset($valuesAll['dCompany']);
			unset($valuesAll['dPhone']);
		}

		$subscription = $this->subscription
			? $this->subscriptionModel->editSubscription($this->subscription, $values->name, (int)$values->interval, userAddressData: $valuesAll)
			: $this->subscriptionModel->createSubscription($values->name, (int)$values->interval, Subscription::STATUS_DRAFT, $this->user);

		$this->subscriptionModel->editSubscriptionPayment($this->subscription, $valuesAll);
		$this->subscriptionModel->editSubscriptionTransport($this->subscription, $valuesAll);

		$this->subscription = $subscription;
	}

	private function processNextOrderSettings(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();

		if (isset($values->orderData) && $values->orderData !== null) {
			$this->subscriptionOrderModel->processChangeDate($this->subscriptionOrder, $values->orderDate);
		}

		$this->subscriptionOrderModel->addAddressToSubscriptionOrder($this->subscriptionOrder, addressData: $valuesAll);
	}

	public function handleDeactivateSubscription(): void
	{
		try {
			if (
				$this->subscription instanceof Subscription &&
				$this->subscription->user->id == $this->user->id) {
				$this->subscriptionModel->deactivateSubscription($this->subscription);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->presenter->redirect('UID|subscriptionDetail', ['hash' => $this->subscription->hash]);
	}

	public function createComponentSubscriptionCancelForm(): SubscriptionCancelForm
	{
		return $this->subscriptionCancelFormFactory->create($this->user);
	}

}

interface ISubscriptionFormFactory
{

	public function create(Mutation $mutation, Subscription|null $subscription = null, User|null $user = null, SubscriptionOrder|null $subscriptionOrder = null): SubscriptionForm;

}
