{snippet form}

	{define #orderDate}
		<div class="grid grid--top grid--y-0">
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>orderDate, label=>$form['orderDate']->label, form=>$form, noP=>false}
			</div>
		</div>
	{/define}

	{define #personalInfo}
		<div class="grid grid--top grid--y-0">
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>firstname, label=>$form['firstname']->label, form=>$form, noP=>false}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>lastname, label=>$form['lastname']->label, form=>$form, noP=>false}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>phone, label=>$form['phone']->label, form=>$form, noP=>false}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>street, label=>$form['street']->label, form=>$form, noP=>false}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>city, label=>$form['city']->label, form=>$form, noP=>false}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>zip, label=>$form['zip']->label, form=>$form, noP=>false, type=>'number'}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>state, label=>$form['state']->label, form=>$form, noP=>false, type=>'select'}
			</div>
		</div>
	{/define}

	{define #subscription}
		<div class="grid grid--top grid--y-0">
			<div class="grid__cell size--6-12@sm">
				{include '../../components/inp.latte', name=>name, label=>$form['name']->label, form=>$form, noP=>false, pClass=>'u-mb-0'}
				<span class="inp-help">
					{_'subscription_name_of_subscription'}
				</span>
			</div>
			<div class="grid__cell size--6-12@sm">
				{dump $allowedFutureIntervals}
				<div class="b-subscription-interval js-subscription-interval" data-allowed-future-intervals="{implode(',', $allowedFutureIntervals)}" data-confirm-popup-id="popup-allowed-future-intervals">
					<div class="b-subscription-interval__inner">
						<p class="b-subscription-interval__label">
							{_'subscription_interval'} *
						</p>
						<div class="b-subscription-interval__items">
							<label class="b-subscription-interval__item">
								<input type="radio" name="intervalSelect" value="2" {if $subscription->interval == 2}checked{/if}>
								<span>
									2 {_'weeks'}
								</span>
							</label>
							<label class="b-subscription-interval__item">
								<input type="radio" name="intervalSelect" value="4" {if $subscription->interval == 4}checked{/if}>
								<span>
									1 {_'month'}
								</span>
							</label>
							<label class="b-subscription-interval__item">
								<input type="radio" name="intervalSelect" value="other" id="other-subscription" {if !in_array($subscription->interval, [2,4])}checked{/if}>
								<span>.</span>
							</label>
						</div>
						{include '../../components/inp.latte', name=>intervalSelect, label=>$form['intervalSelect']->label, form=>$form, noP=>true, type=>'select'}
						<span class="inp-help">
							{_'change_of_subscription_interval_will_impacted_next_order'}
						</span>
						<input type="text" name="interval" class="b-subscription-interval__input-hidden js-subscription-interval__hidden">
					</div>
				</div>
			</div>
		</div>
	{/define}

	{define #billingInfo}
		<div class="grid grid--top grid--y-0">
			<div class="grid__cell">
				{include '../../components/inp.latte', name=>company, label=>$form['company']->label, form=>$form, noP=>false}
			</div>
		</div>
	{/define}

	{define #deliveryInfo}
		{* chci zboží doručit na jinou než fakturační adresu *}
		<div class="js-open js-validate__optional u-mb-xs">
			<p class="u-mb-xs">
				<label class="inp-item inp-item--checkbox">
					<input n:name="deliveryTab" value="1" class="js-open__inp js-validate__optional"/>
					<span>
				{('check')|icon}
				{_title_delivery_form}
			</span>
				</label>
			</p>
			<div class="js-open__content u-mb-xs">
				<div class="">
					<div class="">
							<div class="grid grid--top">
								<p class="grid__cell size--6-12@sm {if $form['dFirstname']->hasErrors()}has-error{/if}">
									{label dFirstname class=>"inp-label" /} <span class="inp-required">*</span><br />
									<span class="inp-fix">
									{input dFirstname class => 'inp-text', data-validate=>name}
								</span>
								</p>
								<p class="grid__cell size--6-12@sm {if $form['dLastname']->hasErrors()}has-error{/if}">
									{label dLastname class=>"inp-label" /} <span class="inp-required">*</span><br />
									<span class="inp-fix">
									{input dLastname class => 'inp-text', data-validate=>name}
								</span>
								</p>
								<p class="grid__cell size--6-12@md {if $form['dCompany']->hasErrors()}has-error{/if}">
									{label dCompany class=>"inp-label" /} <span class="inp-required" n:if="$form['dCompany']->isRequired()">*</span><br />
									<span class="inp-fix">
									{input dCompany class => 'inp-text', data-validate=>company}
								</span>
								</p>
								<p class="grid__cell size--6-12@sm {if $form['dPhone']->hasErrors()}has-error{/if}">
									{label dPhone class=>"inp-label" /} <span class="inp-required" n:if="$form['dPhone']->isRequired()">*</span><br />
									<span class="inp-fix">
									{input dPhone class => 'inp-text', data-validate=>phone}
								</span>
								</p>
								<p class="grid__cell size--6-12@sm {if $form['dStreet']->hasErrors()}has-error{/if}">
									{label dStreet class=>"inp-label" /} <span class="inp-required">*</span><br />
									<span class="inp-fix">
									{input dStreet class => 'inp-text', data-validate=>street}
								</span>
								</p>
								<p class="grid__cell size--6-12@sm {if $form['dCity']->hasErrors()}has-error{/if}">
									{label dCity class=>"inp-label" /} <span class="inp-required">*</span><br />
									<span class="inp-fix">
									{input dCity class => 'inp-text', data-validate=>street}
								</span>
								</p>
								<p class="grid__cell size--6-12@sm {if $form['dZip']->hasErrors()}has-error{/if}">
									{label dZip class=>"inp-label" /} <span class="inp-required">*</span><br />
									<span class="inp-fix">
									{input dZip class => 'inp-text', data-validate=>street}
								</span>
								</p>
								<p class="grid__cell size--6-12@sm {if $form['dState']->hasErrors()}has-error{/if}">
									{label dState class=>"inp-label" /} <span class="inp-required" n:if="$form['dState']->isRequired()">*</span><br />
									<span class="inp-fix inp-fix--select">
									{input dState class => 'inp-select', data-validate=>state}
								</span>
								</p>
								<p class="grid__cell {if $form['dInfo']->hasErrors()}has-error{/if}">
									{label dInfo class=>"inp-label" /} <span class="inp-required" n:if="$form['dInfo']->isRequired()">*</span><br />
									<span class="inp-fix">
									{input dInfo class => 'inp-text', data-validate=>street}
								</span>
								</p>
							</div>
			</div>
		</div>
			</div>
		</div>
	{/define}


	{form form class => '', autocomplete => 'off', novalidate=>"novalidate"}
		<div class="u-max-width--7-12 u-mx-auto">
			{control messageForForm, $flashes, $form}

			{if $subscriptionOrder !== null}
				<p>{_'subscription_next_order_edit_instructions'}</p>
				{if $subscriptionOrder->address === null}
					<p>{_'subscription_next_order_edit_address_instructions'}</p>
				{else}
					<p>{_'subscription_next_order_edit_address_filled'}</p>
				{/if}
			{/if}

			<div class="u-mb-md">
				<h2>
					{_subscription_settings}
				</h2>
				{if $subscriptionOrder === null}
					{include #subscription}
				{*{else}
					{include #orderDate}*}
				{/if}
			</div>

			<div class="b-accordeon js-accordeon u-mb-sm">
				<div class="b-accordeon__item js-accordeon__item">
					<div class="b-accordeon__head js-accordeon__head">
						Doručení a platba
						{('angle-right')|icon}
					</div>
					<div class="b-accordeon__content js-accordeon__content">
						<div class="grid grid--top grid--y-0">
							<div class="grid__cell size--7-12@sm">
								{*{include '../../components/inp.latte', name=>transportType, label=>$form['transportType']->label, form=>$form, noP=>false, type=>'select'}*}
								{var $transportType = $form['transportType']}
								<div class="f-items f-items--w-date u-mb-sm">
									<ul class="f-items__list">
										{foreach $transportType->items as $k=>$i}
											<li class="f-items__item">
												<label class="f-items__label inp-item inp-item--radio">
													<input
													type="radio"
													name="transportType"
													value="{$k}"
													class="{$k} {if $k == 'zasilkovna'}zasilkovnaInput{/if} {if $k == 'ppl_parcel'}pplInput{/if} js-link-subscription"
													{if $k === $chosenTransportType}checked{/if}
													data-toggle-checked
													data-selector=".{$k}" />
													<span class="f-items__content">
														<span class="f-items__text">
															<span class="f-items__name">
																<span>
																	{if $k == App\Model\Order::TRANSPORT_DPD}
																		{translate}transport_name_delivery{/translate}
																	{elseif $k == App\Model\Order::TRANSPORT_ZASILKOVNA}
																		{translate}transport_name_{$k}{/translate}&nbsp;{ifset $order->transportData['maxWeight']}({translate}transport_max_weight_{$order->transportData['maxWeight']}{/translate})&nbsp;{/ifset}
																		{include '../../templates/part/box/zasilkovnaInput.latte', country=>Nette\Utils\Strings::lower($mutation->langMenu), form=>$form, firstTransportSet=>$firstTransportSet}
																	{elseif $k == App\Model\Order::TRANSPORT_PPL_PARCEL}
																		<span class="f-items__wrapper">
																			<span class="f-items__parcel f-items__parcel--name">
																				{translate}transport_name_{$k}{/translate}
																			</span>
																			<span class="f-items__parcel f-items__parcel--btn">
																				<span class="btn btn--small">
																					<span class="btn__text">
																						{translate}transport_btn_{$k}{/translate}
																					</span>
																				</span>
																			</span>
																		</span>
																		<br />
																		<span class="u-font-xs u-last-m0" id="pplParcelInput">{$form['transportInfoText']->value}</span>
																	{else}
																		{translate}transport_name_{$k}{/translate}
																	{/if}
																</span>
															</span>
														</span>
													</span>
												</label>
											</li>
										{/foreach}
									</ul>
								</div>
							</div>
							<div class="grid__cell size--5-12@sm">
								{include '../../components/inp.latte', name=>paymentType, label=>$form['paymentType']->label, form=>$form, noP=>false, type=>'select'}
								{* to show card, apple pay or google pay *}
								{if $subscription->cashOnDelivery === false}
									{include '../../components/inp.latte', name=>subPaymentType, label=>$form['subPaymentType']->label, form=>$form, noP=>false, type=>'select'}
								{/if}
							</div>
						</div>
					</div>
				</div>
				<div class="b-accordeon__item js-accordeon__item">
					<div class="b-accordeon__head js-accordeon__head">
						{_billing_info}
						{('angle-right')|icon}
					</div>
					<div class="b-accordeon__content js-accordeon__content">
						{include #personalInfo}
						{include #billingInfo}
						{include #deliveryInfo}
					</div>
				</div>
			</div>

			<p class="u-mb-xs u-text-right">
				<button type="submit" class="btn btn--arrow btn--sm" name="save">
					<span class="btn__text">
						<span class="item-icon item-icon--after">
							{('angle-right')|icon}
							{* {if $subscriptionOrder !== null}{_btn_save_subscription_next_order_settings}{else}{_btn_save_subscription_settings}{/if} *}
							{_'subscription_confirm_changes'}
						</span>
					</span>
				</button>
			</p>

			<p class="u-mb-xs u-text-right">
				<a href="#popup-deactivate-subscription" class="btn btn--arrow btn--border btn--sm" data-fancybox-deactivate data-subscription-hash="{$subscription->hash}">
					<span class="btn__text">
						<span class="item-icon item-icon--before">
							{('deactivate')|icon}
							{_'deactivate'}
						</span>
					</span>
				</a>
			</p>
		</div>

		<div id="popup-allowed-future-intervals" class="b-subscription-popup u-hide">
			<div class="b-subscription-popup__inner">
				<h2>
					{_'subscription_allowed_future_intervals_title'}
				</h2>
				<p>
					{_'subscription_allowed_future_intervals_text'|noescape}
				</p>
				<a href="#" class="btn btn--arrow btn--sm" data-fancybox-close>
					<span class="btn__text">
						<span class="item-icon item-icon--after">
							{_'subscription_allowed_future_intervals_btn'}
							{('angle-right')|icon}
						</span>
					</span>
				</a>
			</div>
		</div>
	{/form}

	<div id="popup-deactivate-subscription" class="b-subscription-popup u-hide">
		<div class="b-subscription-popup__inner">
			<h2>
				{_'subscription_deactivate_title'}
			</h2>
			<p>
				{_'subscription_deactivate_text'|noescape}
			</p>

			{control subscriptionCancelForm}

		</div>
	</div>
{/snippet}
