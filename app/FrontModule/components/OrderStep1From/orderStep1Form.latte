{default $class = 'u-mb-spec'}
{default $firstTransportSet = false}

<form n:name="form" class="f-basket {$class} js-ajax-form" data-alert="{_branch_alert}">
	{control messageForForm, $flashes, $form}

	<div class="u-pt-sm">
		<div class="f-basket__content grid grid--x-sm">
			<div class="grid__cell size--7-12@lg">
				<h2 class="h3 u-font-regular u-mb-xs">
					1. {_title_transport_form}
				</h2>
				<div class="f-items f-items--w-date u-mb-sm">
					<ul class="f-items__list">
						{foreach $basket->transports as $k=>$i}
						{*{continueIf preg_match('/^personal-/', $k)}*}
							<li class="f-items__item {if $k == $form['transport']->value} is-checked {/if} {if $basket->isOversizedBasket() && !in_array($k, App\Model\MutationTransports::ALLOWED_FOR_OVERSIZE)}is-disabled{/if}">
								<label class="f-items__label inp-item inp-item--radio">
									<input
									type="radio"
									name="transport"
									value="{$k}"
									class="js-transport__inp {$k} {if $k == 'zasilkovna'}zasilkovnaInput{/if} {if $k == 'ppl_parcel'}pplInput{/if} tranportInputs js-link-ajax"
									{if $k == $form['transport']->value} checked="checked"{/if}
									data-toggle-checked
									data-selector=".{$k}"
									data-refresh-url="{link changeTransport!}" />
									<span class="f-items__content">
										<span class="f-items__text">
											<span class="f-items__name">
												<span>
{*													{$i['name']}*}
													{if $k == App\Model\Order::TRANSPORT_DPD}
														{translate}transport_name_delivery{/translate}
													{elseif $k == App\Model\Order::TRANSPORT_ZASILKOVNA}
														{translate}transport_name_{$k}{/translate}&nbsp;{ifset $order->transportData['maxWeight']}({translate}transport_max_weight_{$order->transportData['maxWeight']}{/translate})&nbsp;{/ifset}

{*														{if $k == $form['transport']->value}*}
{*															{if $form['transport']->value == 'zasilkovna' }*}
																{include '../../templates/part/box/zasilkovnaInput.latte', country=>Nette\Utils\Strings::lower($mutation->langMenu), form=>$form, firstTransportSet=>$firstTransportSet}
{*															{/if}*}
{*														{/if}*}
													{elseif $k == App\Model\Order::TRANSPORT_PPL_PARCEL}
														<span class="f-items__wrapper" data-ppl-url="{link setPplParcel}">
															<span class="f-items__parcel f-items__parcel--name">
																{translate}transport_name_{$k}{/translate}
															</span>
															<span class="f-items__parcel f-items__parcel--btn">
																<span class="btn">
																	<span class="btn__text">
																		{translate}transport_btn_{$k}{/translate}
																	</span>
																</span>
															</span>
														</span>
														<span class="u-font-xs u-last-m0" id="pplParcelInput">{$form['transportInfoText']->value}</span>
													{else}
														{translate}transport_name_{$k}{/translate}
													{/if}

													<span class="u-font-xs u-last-m0" n:if="$i->tooltip">
														{$i->tooltip}
													</span>
												</span>
											</span>

											{* posteRestante *}
											<span class="f-items__desc" n:if="$k == 'posteRestante'">
												<a href="{plink 'UID|posteRestante'}" class="js-fancybox-ajax" data-snippetname="snippet--posteRestanteForm">{_post_open_poste_restante}</a>
												{php $post = $basket->getPosteRestante()}
												{if !empty($post)}
													<span>
														{$basket->getPosteRestante()->NAZ_PROV} - {$basket->getPosteRestante()->ADRESA}
													</span>
												{/if}
											</span>


										</span>
										<span class="f-items__side">
											<span class="f-items__price">
												{first}
													<span class="f-items__col-name f-items__col-name--price">
														{_filter_title_price} {_price_tax}
													</span>
												{/first}
												{if $basket->getProductsPriceDPH() > $i['freeFrom'] || $i['priceDPH'] == 0}
													{_free}
												{else}
													{$i['priceDPH']|priceFormat}
												{/if}
											</span>
										</span>
									</span>
								</label>
							</li>
						{/foreach}
					</ul>
				</div>

				<h2 class="h3 u-font-regular u-mb-xs">
					2. {_title_payment_form}
				</h2>
				<div class="f-items u-mb-sm">
					<ul class="f-items__list">
						{var $transportNotSet = $basket->getTransport() == null}
						{if $basket->isSubscriptionEnabled && $basket->containsSubscriptionProducts()}
							{var $payments = $basket->paymentsForSubscription}
						{else}
							{var $payments = $basket->payments}
						{/if}
						{foreach $payments as $k=>$i}
							{var $codDisable = false}
							{continueIf ($k == 'installment' &&  $basket->getBothPriceDPH() < 5000)}
							{var $checked = ($k == $form['payment']->value && $basket->getSubPaymentType() == null) || (App\Model\Order::isOnlinePayment($k) && $basket->getSubPaymentType() == $k) }
							{if $form['transport']->value == 'ppl_parcel' && $k == 'onDelivery' && $codAllowed === false}
								{var $codDisable = true}
								{var $checked = false}
							{/if}


						<li class="f-items__item {if $checked} is-checked {/if} {if $transportNotSet}is-disabled{/if} {if $codDisable}is-cod-disabled{/if}" {if $k == 'onlineApplePay'}id="apple-pay-li" style="display: none;"{/if}>
								<label class="f-items__label inp-item inp-item--radio">
									<input
											type="radio"
											name="payment"
											value="{$k}"
											class="js-payment__inp {$i['transports']} js-link-ajax"
											{if $checked}checked="checked"{/if}
											data-refresh-url="{link changePayment!}"
											data-toggle-checked />
									<span class="f-items__content">
										<span class="f-items__text">
											<span class="f-items__name">
												<span>
{*													{$i['name']}*}
													{translate}payment_name_{$k}{/translate}
												</span>

											</span>

											{* TODO: APPLE PAY dostylovat *}
											<span class="f-items__desc" n:if="$k == 'onlineApplePay'">
												<span class="f-items__icons grid">
													<span class="f-items__icon grid__cell size--auto">
														<span  style="-webkit-appearance: -apple-pay-button; -apple-pay-button-type: plain;"></span>
													</span>
												</span>
											</span>

											{* gopay *}
											 <span class="f-items__desc" n:if="$k == 'online'">
												<span class="f-items__icons grid">
													<span class="f-items__icon grid__cell size--auto">
														{('gopay')|icon}
													</span>
													<span class="f-items__icon grid__cell size--auto">
														{('visa-verified')|icon}
													</span>
													<span class="f-items__icon grid__cell size--auto">
														{('mcsc')|icon}
													</span>
													<span class="f-items__icon grid__cell size--auto">
														{('visa')|icon}
													</span>
													<span class="f-items__icon grid__cell size--auto">
														{('visa-electron')|icon}
													</span>
													<span class="f-items__icon grid__cell size--auto">
														{('mastercard')|icon}
													</span>
													<span class="f-items__icon grid__cell size--auto">
														{('mce')|icon}
													</span>
													<span class="f-items__icon grid__cell size--auto">
														{('maestro')|icon}
													</span>
												</span>
											</span>

										</span>
										<span class="f-items__side">
											<span class="f-items__price">
												{first}
													<span class="f-items__col-name f-items__col-name--price">
														{_filter_title_price} {_price_tax}
													</span>
												{/first}
												{if $order->productsPriceDPH > $i['freeFrom']}
													{_free}
												{else}
													{$i['priceDPH']|priceFormat}
												{/if}
											</span>
										</span>
									</span>
								</label>
							</li>

							{if $k == 'onlineApplePay'}
								<script>
									if (window.ApplePaySession && window.ApplePaySession.canMakePayments()) {
										document.querySelector("#apple-pay-li").style.display = "block"; }
								</script>
							{/if}
						{/foreach}
					</ul>
				</div>
			</div>
			<div class="grid__cell size--5-12@lg size--4-12@xl">
				{snippetArea cartSummaryArea}
					{snippet cartSummary}
						{include '../../templates/part/box/cart-summary.latte', showDeliveryInfoText=>false, showRemoveVouchersBtns=>true}
						{include FE_TEMPLATE_DIR . '/part/box/coupon.latte'}
					{/snippet}
				{/snippetArea}
				<div class="b-coupon__inner">
					{control messageForForm, $flashes, $form}
				</div>
			</div>
		</div>
		<div class="grid grid--middle">
			<div class="grid__cell size--autogrow@md order--2 order--1@md">
				<p class="f-basket__prev">
					<a href="{$pages->basket->alias}">
						<span class="item-icon item-icon--sm">
							{('angle-left')|icon}
							{_btn_back}
						</span>
					</a>
				</p>
			</div>
			<div class="grid__cell size--auto@md order--1 order--2@md">
				<p class="f-basket__next">
					<button type="submit" n:name="next" class="btn btn--big btn--secondary btn--arrow">
						<span class="btn__text">
							<span class="item-icon item-icon--after">
								{_btn_order_continue}
								{('angle-right')|icon}
							</span>
						</span>
					</button>
				</p>
			</div>
		</div>
	</div>
</form>
