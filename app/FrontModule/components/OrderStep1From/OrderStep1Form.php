<?php

namespace SuperKoderi\Components;


use App\Model\Mutation;
use App\Model\MutationTransports;
use App\Model\Order;
use App\Model\Orm;
use App\Model\Voucher;
use App\Model\VoucherCode;
use App\Model\VoucherCodeException;
use App\Model\VoucherCodeModel;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\Template;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use SuperKoderi\Basket;
use SuperKoderi\EasyMessages;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\hasRemoveVoucherTrait;
use SuperKoderi\ImageResizer;
use SuperKoderi\IPagesFactory;
use SuperKoderi\Pages;
use SuperKoderi\TranslatorDB;

/**
 * @property-read Template $template
 */
class OrderStep1Form extends UI\Control
{
	use hasMessageForFormComponentTrait;
	use hasRemoveVoucherTrait;

	private TranslatorDB $translator;

	private Basket $basket;

	private Pages $pages;

	private EasyMessages $easyMessages;

	private ImageResizer $imageResizer;

	private Mutation $mutation;

	private Orm $orm;

	private VoucherCodeModel $voucherCodeModel;

	public function __construct(
		Mutation $mutation,
		TranslatorDB $translator,
		Basket $basket,
		IPagesFactory $pagesFactory,
		EasyMessages $easyMessages,
		ImageResizer $imageResizer,
		Orm $orm,
		VoucherCodeModel $voucherCodeModel,
	)
	{
		$this->translator = $translator;
		$this->basket = $basket;
		$this->basket->setMutation($mutation);
		$this->pages = $pagesFactory->create($mutation);
		$this->easyMessages = $easyMessages;
		$this->imageResizer = $imageResizer;
		$this->mutation = $mutation;
		$this->orm = $orm;
		$this->voucherCodeModel = $voucherCodeModel;
		$this->basket->setIsOversizedBasket();
	}


	public function render(): void
	{
		$cardErrorMsg = $this->easyMessages->get(EasyMessages::KEY_CARD);
		foreach ($cardErrorMsg as $msg) {
			$this->flashMessage($msg->text, $msg->type);
		}

		// specialni hlasky pro poukazy
		$cardErrorMsgVoucher = $this->easyMessages->get('voucher');
		$this->template->voucherMsg = $cardErrorMsgVoucher;

		$this->template->imageResizer = $this->imageResizer;
		$this->basket->setOnlyValidTransports();
		$this->template->basket = $this->basket;
		$this->template->order = $this->basket->getFutureOrder();
		$this->template->lg = $this->mutation->langCode;
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->pages;
		$this->template->hasOversizedProducts = $this->basket->isOversizedBasket();

		$this->template->codAllowed = !empty($this->basket->getPplParcel()) && array_key_exists('codAllowed', $this->basket->getPplParcel()) && $this->basket->getPplParcel()['codAllowed'];

		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . "/orderStep1Form.latte");
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);
		$form->addText('transport')
			->setRequired('messageErrorStep1');

		if ($this->basket->getTransportKey() !== NULL) {
			$form['transport']->value = $this->basket->getTransportKey();
		} elseif (count($this->basket->transports) == 1) {

			$form['transport']->value = array_key_first((array)$this->basket->transports);
		}

		$form->addText('payment')->setRequired('messageErrorStep1');
		$form->addText('zasilkovnaId');
		$form->addText('pplParcelCode');
		$form->addText('transportInfoText');

		$this->refreshZasilkovna($form);
		$this->refreshPplParcel($form);

		if ($this->basket->getPaymentKey() !== NULL) {
			$form['payment']->value = $this->basket->getPaymentKey();
		}

		$form->addText('voucherCode', 'voucher');
		$form->addButton('voucherCheck', 'voucherCheck');

		$form->addButton('next', 'btn_order_continue');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}


	public function formError(UI\Form $form): void
	{
		$firstTransportSet = true;
		$transport = $this->basket->getTransport();
		if(isset($transport)) {
			$firstTransportSet = false;
		}

		$this->refreshZasilkovna($form);
		$this->refreshPplParcel($form);

		$this->template->firstTransportSet = $firstTransportSet;

		$form->addError($this->translator->translate('pleaseChooseZasilkovnaPlace'));

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	/**
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		if ($this->basket->isOversizedBasket() && !in_array($values['transport'], MutationTransports::ALLOWED_FOR_OVERSIZE)) {
			$form->addError($this->translator->translate('change_transport_not_allowed_for_oversized_products'));
			return;
		}

		$valuesAll = $form->getHttpData();

		$step2Go = true;

		// VOUCHER CODE START
		if (isset($valuesAll['voucherCode']) && (bool)$valuesAll['voucherCode']) {
			// overim kod jestli existuje
			/** @var ?VoucherCode $voucherCode */
			$voucherCode = $this->orm->voucherCode->getByCode($valuesAll['voucherCode'], $this->mutation);
			$step2Go = false;

			if (isset($voucherCode)) {
//				if (!$voucherCode->voucher) {
//					//					$form->addError("error_voucher_bad");
//					//					$vMessageError = "error_voucher_bad";
//					$this->easyMessages->send(EasyMessages::KEY_VOUCHER, 'error_voucher_bad', EasyMessages::TYPE_ERROR);
//
//					$form['voucherCode']->setValue("");
//					$this->template->vcodeIsError = TRUE;
//				}

				try {
					$this->voucherCodeModel->isValid($voucherCode, $this->basket);

					$transportIsFree = $this->basket->getProductsPriceDPH() >= $this->basket->getTransportFreeFrom();
					if ($voucherCode->voucher->application == Voucher::APPLICATION_SERVICE && $transportIsFree) {
						$this->basket->removeVoucher($voucherCode);
						$this->easyMessages->send(EasyMessages::KEY_VOUCHER, VoucherCodeModel::VOUCHER_TRANSPORT_NO_EFFECT, EasyMessages::TYPE_OK);
					} else {
						$this->basket->addVoucherCode($voucherCode);
						$this->easyMessages->send(EasyMessages::KEY_VOUCHER, 'voucher_added', EasyMessages::TYPE_OK);
					}



				} catch (VoucherCodeException $e) {
					//					$form->addError($e->getMessage());
					$vMessageError = $e->getMessage();
					$this->easyMessages->send(EasyMessages::KEY_VOUCHER, $vMessageError, EasyMessages::TYPE_ERROR);


					$form['voucherCode']->setValue("");
					$this->template->vcodeIsError = TRUE;
				}
			} else {
				// kod nenalezen
				//				$form->addError("error_voucher_bad");
				$vMessageError = 'error_voucher_bad';
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, 'error_voucher_bad', EasyMessages::TYPE_ERROR);

				$this->template->vcodeIsError = TRUE;
			}

			if (isset($vMessageError) && (bool)$vMessageError) {
				$this->template->vc = $valuesAll['voucherCode'];
			}

		} elseif (isset($valuesAll['voucherCheck'])) { // situace: kliknu na vlozit voucher, ale pole pro voucher je prazdne = nejdu na step2 a ani nechci validovat dopravu a platbu
			$this->easyMessages->send(EasyMessages::KEY_VOUCHER, 'error_voucher_bad', EasyMessages::TYPE_ERROR);
			$this->template->vcodeIsError = TRUE;
			$step2Go = false;
		}

		if (isset($values['next'])) {

			if (isset($vMessageError) && (bool)$vMessageError) {
//				$this->flashMessage($vMessageError, 'error');
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, $vMessageError, EasyMessages::TYPE_ERROR);

				$this->presenter->redirect('UID|step1');
			}

		} elseif (isset($_POST['voucherCheck']) && $_POST['voucherCheck']) {

			if (isset($vMessageError) && (bool)$vMessageError) {
////				$this->flashMessage($vMessageError, 'error');
//
				$this->easyMessages->send(EasyMessages::KEY_VOUCHER, $vMessageError, EasyMessages::TYPE_ERROR);
			}
//
			if ($this->presenter->isAjax() && isset($this->template->vcodeIsError)) { // hack
				$this->presenter->redrawControl();

			} else {
				$this->presenter->redirect('UID|step1');
			}

		} else {
			if ($this->presenter->isAjax()) {
				$this->presenter->redrawControl();
			} else {
				$this->presenter->redirect('UID|step1');
			}
		}

		// VOUCHER CODE END

		if ($step2Go) {
			if (!$form->values->transport || !$form->values->payment) {
				$form->addError('messageErrorStep1');
			} else {
				$posteRestanteTmp = $this->basket->getPosteRestante();
				if (isset($form->values->transport) && $form->values->transport == 'posteRestante' && $posteRestanteTmp === NULL) {
					$form->addError($this->translator->translate('post_pleaseChoosePost'));
				}

				if (!in_array($form->values->transport, explode(' ', $this->basket->payments[$form->values->payment]['transports']))) {
					$form->addError($this->translator->translate('messageErrorBadCombinaton'));
				}

				if ($form->values->payment === Order::PAYMENT_ONDELIVERY && !empty($this->basket->getPplParcel()) && $this->basket->getPplParcel()['codAllowed'] === false) {
					$form->addError($this->translator->translate('pplParcelNotCashOnDelivery'));
				}

				if (!$form->hasErrors()) {
//			$this->basket->setCustomerService($form->getHttpData($form::DATA_TEXT, 'service[]'));
					$this->basket->setTransport($form->values->transport);
					$this->refreshZasilkovna($form);
					$this->refreshPplParcel($form);
					$this->basket->setPayment($form->values->payment);

					// check if zasilkovna dependecies are OK
					if (!$this->basket->isValidTransport()) {
						$form->addError($this->translator->translate('pleaseChooseZasilkovnaPlace'));
						$this->formError($form);
						$ok = false;
					}

					// no error
					if ($ok ?? true) {
						// kontrola zda jsou věci k pujcení stále k diposzici
						$test = $this->basket->validateData();
						if ($test !== TRUE) {
							$this->easyMessages->send("rentInfo", $test['message'], EasyMessages::TYPE_ERROR);
//				$this->flashMessage($test['message'], 'error');
							$this->presenter->redirect('UID|basket');
						}

						$this->presenter->redirect('UID|step2');
					}
				}
			}
		}
	}


	public function refreshZasilkovna(UI\Form $form): void
	{
		if ($this->basket->getTransport() === null || $this->basket->getTransport()->key !== Order::TRANSPORT_ZASILKOVNA) {
			$this->basket->setZasilkovna();
		}

		$zasilkovna = $this->basket->getZasilkovna();

		if ($zasilkovna !== []) {
			$form['zasilkovnaId']->value = $zasilkovna['id'];
			$form['transportInfoText']->value = $zasilkovna['infoText'];
		} else {
			$form['zasilkovnaId']->value = null;
			$form['transportInfoText']->value = null;
		}
	}

	public function refreshPplParcel(UI\Form $form): void
	{
		if ($this->basket->getTransport() === null || $this->basket->getTransport()->key !== Order::TRANSPORT_PPL_PARCEL) {
			$this->basket->setPplParcel();
		}

		$pplParcel = $this->basket->getPplParcel();

		if ($pplParcel !== []) {
			$form['pplParcelCode']->value = $pplParcel['code'];
			$form['transportInfoText']->value = $pplParcel['infoText'];
		} else {
			$form['pplParcelCode']->value = null;
			$form['transportInfoText']->value = null;
		}
		$this->template->codAllowed = $pplParcel['codAllowed'] ?? true;
	}


	public function handleSetZasilkovna(): void
	{
		$data = $this->presenter->request->getPost();
		$this->basket->setZasilkovna($data['id'], $data['desc']);
		$this->basket->setPplParcel();

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleSetPplParcel(): void
	{
		$data = $this->presenter->request->getPost();
		$this->basket->setPplParcel($data['code'], $data['name'], $data['codAllowed'] === 'true');
		$this->basket->setZasilkovna();

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function handleChangeTransport(): void
	{
		if ($this->presenter->isAjax()) {
			$data = $this->presenter->request->getPost();
			$this->basket->setTransport($data['type']);

			if ($this->basket->isOversizedBasket() && !in_array($data['type'], MutationTransports::ALLOWED_FOR_OVERSIZE)) {
				$this->basket->setTransport();
			}

			$this->presenter->redrawControl();
		}
	}


	public function handleChangePayment(): void
	{
		$data = $this->presenter->request->getPost();
		$this->basket->setPayment($data['type']);

		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl();
		}
	}
}


interface IOrderStep1FormFactory
{
	function create(Mutation $mutation): OrderStep1Form;
}
