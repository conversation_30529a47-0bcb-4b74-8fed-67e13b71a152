
{snippet form}
	{default $hideValidationMsgs = true}
	<div class="u-max-width--10-12 u-mx-auto">
		{include '../../templates/part/box/annot.latte', class=>'b-annotation--md u-mb-xs', type=>false}
		{include '../../templates/part/box/content.latte'}
		{* {form form class => 'f-common js-validate', autocomplete => 'off', novalidate=>"novalidate"} *}
		{form form class => 'f-login', novalidate=>"novalidate"}
			{control messageForForm, $flashes, $form}
			<div class="grid grid--y-0">
				<div class="grid__cell size--6-12@lg">
					<h2 class="f-register__title">
						{_title_register_info}
					</h2>
					<div class="f-register__section f-register__section--dark u-last-m0 u-mb-xs">
						{include '../inp.latte', name=>email, label=>$form['email']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
						<p class="{if $form['password']->hasErrors()}has-error{/if} f-register__item">
							{label password class=>"inp-label" /} <span class="inp-required">*</span><br/>
							{* {input password class => 'inp-text', data-validate=>password} *}
							<span class="inp-fix inp-fix--icon-after">
								<a href="#" class="js-show-password">
									{('eye')|icon}
									<span class="u-vhide">
										{_show_password}
									</span>
								</a>
								<input type="password" class="inp-text" name="password">
							</span>
						</p>
						<p class="{if $form['passwordVerify']->hasErrors()}has-error{/if} f-register__item">
							{label passwordVerify class=>"inp-label" /} <span class="inp-required">*</span><br/>
							{* {input passwordVerify class => 'inp-text', data-validate=>passwordVerify} *}
							<span class="inp-fix inp-fix--icon-after">
								<a href="#" class="js-show-password">
									{('eye')|icon}
									<span class="u-vhide">
										{_show_password}
									</span>
								</a>
								<input type="password" class="inp-text" name="passwordVerify">
							</span>
						</p>
						{include '../inp.latte', name=>phone, label=>$form['phone']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
					</div>
				</div>
				<div class="grid__cell size--6-12@lg">
					<h2 class="f-register__title">
						{_delivery_address}
					</h2>
					<div class="f-register__section u-last-m0 u-mb-xs">
						<div class="grid grid--y-0">
							<div class="grid__cell size--6-12@sm">
								{include '../inp.latte', name=>dFirstname, label=>$form['dFirstname']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
							</div>
							<div class="grid__cell size--6-12@sm">
								{include '../inp.latte', name=>dLastname, label=>$form['dLastname']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
							</div>
						</div>
						{include '../inp.latte', name=>dStreet, label=>$form['dStreet']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
						{include '../inp.latte', name=>dCity, label=>$form['dCity']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
						<div class="grid grid--y-0">
							<div class="grid__cell size--6-12@sm">
								{include '../inp.latte', name=>dZip, label=>$form['dZip']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
							</div>
							<div class="grid__cell size--6-12@sm">
								{include '../inp.latte', name=>dState, label=>$form['dState']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
							</div>
						</div>

						{* Fakturační adresa se liší od dodací *}
						<div class="js-open">
							<p class="u-mb-xs">
								<label class="inp-item inp-item--checkbox">
									{* {input invoiceTab} *}
									<input n:name="invoiceTab" value="1" class="js-open__inp"/>
									<span>
										{('check')|icon}
										{_label_different_delivery_address}
									</span>
								</label>
							</p>
							<div class="js-open__content u-mb-xs">
								<div class="grid grid--y-0">
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>firstname, label=>$form['firstname']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>lastname, label=>$form['lastname']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
								</div>
								{include '../inp.latte', name=>street, label=>$form['street']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
								{include '../inp.latte', name=>city, label=>$form['city']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
								<div class="grid grid--y-0">
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>zip, label=>$form['zip']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>state, label=>$form['state']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
								</div>
							</div>
						</div>

						{if $mutation->langCode != 'cs' && $mutation->langCode != 'sk' && $mutation->langCode != 'pl'}
						{* Vyplnit firemní údaje *}
						<div class="js-open">
							<p class="u-mb-xs">
								<label class="inp-item inp-item--checkbox">
									{* {input firmTab} *}
									<input n:name="firmTab" value="1" class="js-open__inp"/>
									<span>
										{('check')|icon}
										{_title_company_form}
									</span>
								</label>
							</p>
							<div class="js-open__content u-mb-xs">
								{include '../inp.latte', name=>company, label=>$form['company']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
								<div class="grid grid--y-0">
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>ic, label=>$form['ic']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>dic, label=>$form['dic']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
								</div>
							</div>
						</div>
						{/if}


						{* Mám registrovanou chovatelskou stanici *}
						{if !in_array($mutation->langCode, ['sk','cs', 'pl'])} {*skryto*}
						<div class="js-open">
							<p class="u-mb-xs">
								<label class="inp-item inp-item--checkbox">
									<input n:name="breedingTab" value="1" class="js-open__inp"/>
									<span>
										{('check')|icon}
										{_title_station_form}
									</span>
								</label>
							</p>

							<div class="js-open__content u-mb-xs">
								{include '../inp.latte', name=>breedingName, label=>$form['breedingName']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
								<div class="grid grid--y-0">
									{*
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>breedingIc, label=>ic, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
									<div class="grid__cell size--6-12@sm">
										{include '../inp.latte', name=>breedingDic, label=>dic, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
									 *}
									<div class="grid__cell size--6-12@md size--12-12@lg size--6-12@xl">
										{include '../inp.latte', name=>breedingNumber, label=>$form['breedingNumber']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
									<div class="grid__cell size--6-12@md size--12-12@lg size--6-12@xl">
										{include '../inp.latte', name=>breedingCompany, label=>$form['breedingCompany']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
									<div class="grid__cell size--6-12@md size--12-12@lg size--6-12@xl">
										{include '../inp.latte', name=>breedingAmount, label=>$form['breedingAmount']->label, form=>$form, labelClass=>inp-label, pClass=>'f-register__item'}
									</div>
								</div>
								{* {input breedingBreed} *}

								<div class="js-add-item js-suggest js-suggest--custom" data-suggest="/suggest/breed?" data-multi-breed="true">
									{label breedingBreed class=>"inp-label" /} <span class="inp-required" n:if="$form['breedingBreed']->isRequired()">*</span>
									<div class="js-add-item__items u-mb-xxs">
										{input breedingBreed class => 'js-add-item__hidden u-vhide'}
										{ifset $formDataExtend['breedingBreed']}
											{foreach $formDataExtend['breedingBreed'] as $itemId => $item}
												<a id="{$itemId}" href="#" class="flag item-icon item-icon--after js-add-item__item">
													{$item}
													{('cross')|icon}
												</a>
											{/foreach}
										{/ifset}
									</div>
									<p class="{if $form['breedingBreed']->hasErrors()}has-error{/if}">
										<span class="inp-fix">
											<input type="text" class="inp-text js-suggest__inp js-add-item__inp" name="q" placeholder="{_placeholder_add_breed}" autocomplete="off">
										</span>
									</p>
								</div>
							</div>

						</div>
						{/if}

						{* @todo jk diety klinika možná nebude if $isClinic} {* bool false = když mutace nemá žádné kliniky }
							{input clinicTab} {* checkbox Veterinární klinika }
							{input clinic} {* select vet. klinika }
							{* text pole když veterinu ručně }
							{input clinicName}
							{input clinicStreet}
							{input clinicCity}
						{/if*}

					</div>
					{*ANTISPAM*}
					<p class="u-js-hide js-antispam{if $form['antispamNoJs']->hasErrors()} has-error{/if}">
						<label n:name="antispamNoJs" class="">
							{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
						</label>
						<span class="inp-fix">
							<input n:name="antispamNoJs" class="inp-text">
						</span>
					</p>
					{*/ANTISPAM*}
				</div>
			</div>
			<p class="f-register__btn">
				<button type="submit" class="btn btn--arrow">
					<span class="btn__text">
						<span class="item-icon item-icon--after">
							{('angle-right')|icon}
							{_btn_register}
						</span>
					</span>
				</button>
			</p>

			{* <p class="js-validate__item {if $form['phone']->hasErrors()}has-error{/if}">
			<span class="grid grid--middle">
				<span class="grid__cell {if $hideValidationMsgs}size--4-12@md u-text-nowrap{else}size--3-12@md size--4-12@lg size--3-12@xl{/if}">
					{label phone class=>"inp-label" /}: <span
							class="inp-required" n:if="$form['phone']->isRequired()">*</span><br/>
				</span>
				<span class="grid__cell {if $hideValidationMsgs}size--8-12@md{else}size--5-12@md size--7-12@lg size--5-12@xl{/if}">
					<span class="inp-fix">
						{input phone class => 'inp-text', data-validate=>phone}
					</span>
				</span>
				{if !$hideValidationMsgs}
					<span class="grid__cell size--11-12@sm size--4-12@xl">
						<span class="item-icon js-validate__message">
							{('check-polygon')|icon}
						</span>
					</span>
				{/if}
			</span>
			</p>
			<p class="js-validate__item {if $form['email']->hasErrors()}has-error{/if}">
			<span class="grid grid--middle">
				<span class="grid__cell {if $hideValidationMsgs}size--4-12@md{else}size--3-12@md size--4-12@lg size--3-12@xl{/if}">
					{label email class=>"inp-label" /}: <span
							class="inp-required" n:if="$form['email']->isRequired()">*</span><br/>
				</span>
				<span class="grid__cell {if $hideValidationMsgs}size--8-12@md{else}size--5-12@md size--7-12@lg size--5-12@xl{/if}">
					<span class="inp-fix">
						{input email class => 'inp-text', data-validate=>email}
					</span>
				</span>
				{if !$hideValidationMsgs}
					<span class="grid__cell size--11-12@sm size--4-12@xl">
						<span class="item-icon js-validate__message">
							{('check-polygon')|icon}
						</span>
					</span>
				{/if}
			</span>
			</p>
			<p class="js-validate__item {if $form['password']->hasErrors()}has-error{/if}">
			<span class="grid grid--middle">
				<span class="grid__cell {if $hideValidationMsgs}size--4-12@md{else}size--3-12@md size--4-12@lg size--3-12@xl{/if}">
					{label password class=>"inp-label" /}: <span class="inp-required">*</span><br/>
				</span>
				<span class="grid__cell {if $hideValidationMsgs}size--8-12@md{else}size--5-12@md size--7-12@lg size--5-12@xl{/if}">
					<span class="inp-fix">
						{input password class => 'inp-text', data-validate=>password}
					</span>
				</span>
				{if !$hideValidationMsgs}
					<span class="grid__cell size--11-12@sm size--4-12@xl">
						<span class="item-icon js-validate__message">
							{('check-polygon')|icon}
						</span>
					</span>
				{/if}
			</span>
			</p>
			<p class="js-validate__item {if $form['passwordVerify']->hasErrors()}has-error{/if}">
			<span class="grid grid--middle">
				<span class="grid__cell {if $hideValidationMsgs}size--4-12@md{else}size--3-12@md size--4-12@lg size--3-12@xl{/if}">
					{label passwordVerify class=>"inp-label" /}: <span class="inp-required">*</span><br/>
				</span>
				<span class="grid__cell {if $hideValidationMsgs}size--8-12@md{else}size--5-12@md size--7-12@lg size--5-12@xl{/if}">
					<span class="inp-fix">
						{input passwordVerify class => 'inp-text', data-validate=>passwordVerify}
					</span>
				</span>
				{if !$hideValidationMsgs}
					<span class="grid__cell size--11-12@sm size--4-12@xl">
						<span class="item-icon js-validate__message">
							{('check-polygon')|icon}
						</span>
					</span>
				{/if}
			</span>
			</p> *}
			{* <div class="f-common__wrap">
				<p>
					{_registration_agree} <a href="{plink 'UID|personalData'}">{_sign_link}</a>.
				</p>
				<p class="f-common__btn">
					<button type="submit" class="btn btn--secondary btn--block btn--sm">
							<span class="btn__text">
								{_btn_register}
							</span>
					</button>
				</p>
			</div> *}
			{*include '../SignInForm/footer.latte', onlyAdvantages=>true*}
		{/form}
	</div>
{/snippet}
