<?php declare(strict_types = 1);

namespace SuperKoderi\Components;

use App\Model\Orm;
use App\Model\Subscription;
use App\Model\SubscriptionModel;
use App\Model\User;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\Translator;
use SuperKoderi\TranslatorDB;

class SubscriptionCancelForm extends Control
{
	use hasMessageForFormComponentTrait;

	const CANCEL_REASON = [
		'interval' => 'interval_not_suitable',
		'order_settings' => 'order_settings_difficult',
		'shipping_payment' => 'shipping_or_payment_issue',
		'price' => 'price_unsatisfactory',
		'mistake' => 'registered_by_mistake',
		'dont_want_say' => 'dont_want_say',
		'other_reason' => 'other_reason',
	];

	private User $userEntity;

	public function __construct(
		User $userEntity,
		protected readonly TranslatorDB $translator,
		protected readonly Orm $orm,
		protected readonly SubscriptionModel $subscriptionModel
	)
	{
		$this->userEntity = $userEntity;
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/subscriptionCancelForm.latte');
	}

	public function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$reasons = ['' => $this->translator->translate('select_reason')] + self::CANCEL_REASON;
		$form->addSelect('reason', 'reason_cancel', $reasons)
			->setRequired($this->translator->translate('select_reason'));

		$form->addText('otherReason', 'other_reason_cancel')
			->setRequired(false)
			->addConditionOn($form['reason'], Form::EQUAL, 'other')
			->setRequired('Please specify the reason.');

		$form->addHidden('subscriptionHash')
			->setHtmlId('cancelFormSubscriptionHash');

		$form->addSubmit('send', 'subscription_deactivate_btn');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		try {
			$subscription = $this->orm->subscription->getByHash($values->subscriptionHash);
			if (
				$subscription instanceof Subscription &&
				$subscription->user->id == $this->userEntity->id) {
					$this->subscriptionModel->deactivateSubscription(
						subscription: $subscription,
						reason: $values->reason,
						otherReason: $values->otherReason
					);

					$this->presenter->flashMessage('subscription_cancel_success', 'ok');
					$this->presenter->redirect('this');
			}
		} catch (\Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

	}

}

interface ISubscriptionCancelFormFactory
{
	public function create(User $userEntity): SubscriptionCancelForm;
}
