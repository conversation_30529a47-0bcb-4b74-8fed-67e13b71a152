{form form autocomplete=>"off", novalidate=>"novalidate", class=>"js-validate"}
    {control messageForForm, $flashes, $form}

    <div class="js-open">
    {include '../inp.latte' name=>reason, type=>'select', label=>$form['reason']->label, form=>$form, className=>'js-open__inp js-validate__reason'}
        <div class="js-open__content" style="display: none;">
            {include '../inp.latte' name=>otherReason, label=>$form['otherReason']->label, form=>$form, pClass=>'u-mb-xxs'}
        </div>
    </div>

    {input subscriptionHash}
    <button n:name="send" class="btn btn--arrow">
        <span class="btn__text">
            <span class="item-icon item-icon--after">
                {_'subscription_deactivate_btn'}
                {('angle-right')|icon}
            </span>
        </span>
    </button>


{/form}