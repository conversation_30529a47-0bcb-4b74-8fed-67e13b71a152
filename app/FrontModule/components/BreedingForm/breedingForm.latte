{default $class = 'u-mb-xxl'}
{form form class => "f-breeding $class", novalidate=>"novalidate"}
	{control messageForForm, $flashes, $form}
	<div class="f-breeding__section">
		{include '../inp.latte', name=>name, label=>$form['name']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>stationReg, label=>$form['stationReg']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>stationName, label=>$form['stationName']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>breed, label=>$form['breed']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>amount, label=>$form['amount']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>phone, label=>$form['phone']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>email, label=>$form['email']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>ic, label=>$form['ic']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>dic, label=>$form['dic']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
		</div>
	</div>
	<div class="f-breeding__section u-mb-sm">
		<h2 class="f-breeding__title">
			{_billing_address} *
		</h2>
		{include '../inp.latte', name=>street, label=>$form['street']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>city, label=>$form['city']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
			</div>
			<div class="grid__cell size--6-12@md">
				{include '../inp.latte', name=>zip, label=>$form['zip']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item', type=>'number'}
			</div>
		</div>
		<div n:class="js-open"> {* , !$form['deliveryAddress']->value ? is-open*}
			<p class="f-breeding__item">
				<label class="inp-item inp-item--checkbox">
					<input n:name="deliveryAddress" class="js-open__inp">
					<span>
						{*{_label_billing_same}*}
						{_label_billing_diff}
						{('check')|icon}
					</span>
				</label>
			</p>
			<div n:class="js-open__content, $form['deliveryAddress']->value ? is-open">
				<h2 class="f-breeding__title">
					{_delivery_address}
				</h2>
				{include '../inp.latte', name=>dName, label=>$form['dName']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}

				{include '../inp.latte', name=>dStreet, label=>$form['dStreet']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
				<div class="grid grid--y-0">
					<div class="grid__cell size--6-12@md">
						{include '../inp.latte', name=>dCity, label=>$form['dCity']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
					</div>
					<div class="grid__cell size--6-12@md">
						{include '../inp.latte', name=>dZip, label=>$form['dZip']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item', type=>'number'}
					</div>
				</div>
			</div>
		</div>
		{include '../inp.latte', agreeLabel=>true, type=>'checkbox', name=>agree, label=>$form['agree']->label, form=>$form, showStar=>true, noP=>false, pClass=>'f-breeding__item'}
	</div>
	<p class="f-breeding__btn">
		<button n:name="send" class="btn btn--loader btn--sm">
			<span class="btn__text">
				<span class="item-icon item-icon--after">
					{_btn_register}
					{('angle-right')|icon}
				</span>
			</span>
		</button>
	</p>

	{*ANTISPAM*}
	{if isset($form['antispamNoJs'])}
		<p class="u-js-hide js-antispam{if $form['antispamNoJs']->hasErrors()} has-error{/if}">
			<label n:name="antispamNoJs" class="">
				{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
			</label>
			<span class="inp-fix">
				<input n:name="antispamNoJs" class="inp-text">
			</span>
		</p>
	{/if}
	{*/ANTISPAM*}

{/form}

