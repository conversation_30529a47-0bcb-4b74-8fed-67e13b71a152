{default $className = false}
{default $cols = false}
{default $rows = false}
{default $type = false}
{default $pClass = 'u-mb-xs'}
{default $labelClass = false}
{default $labelReplace = false}
{default $if = true}
{default $noP = false}
{default $disabled = false}
{default $inputLink = false}
{default $showStar = true}
{default $id = false}
{default $required = false}
{default $agreeLabel = false}
{default $consentLabel = false}

{if $if}
	{if $label}
		{capture $label}{$label}{/capture}
	{/if}
	{if !$noP}<p class="{$pClass}{if $form[$name]->errors} has-error{/if}">{/if}
		{if $type == 'checkbox'}
			<label class="inp-item inp-item--checkbox {$labelClass}">
				<input type="checkbox" name="{$name}" value="1"{if $form[$name]->value == 1} checked="checked"{/if}>
				<span>
					{if $labelReplace}
						{$label|replace:"%link%",(string)$labelReplace|noescape}
					{elseif $agreeLabel}
						{_label_agree_1} <a href="{plink 'UID|personalData'}" target="_blank">{_label_agree_2}</a>.
					{elseif $consentLabel}
						{_'_competition_consent'|noescape}
					{else}
						{$label|noescape}
					{/if}
					{('check')|icon}
				</span>
			</label>
		{elseif $type == 'radios'}
			<span class="inp-label u-block">{$label|noescape}</span>
			<span class="items">
				{foreach $form[$name]->items as $k=>$i}
					<label class="items__item inp-item inp-item--radio">
						<input type="radio" name="{$name}" value="{$k}"{if $form[$name]->value == $k} checked="checked"{/if}>
						<span>
							{translate}{$i|noescape}{/translate}
						</span>
					</label>
				{/foreach}
			</span>
		{elseif $type == 'textarea'}
			<label for="{$name}" class="inp-label">
				{$label|noescape}
			</label>
			<span class="inp-fix">
				<textarea id="{$id}" name="{$name}" class="inp-text" cols="40" rows="8">{$form[$name]->value}</textarea>
			</span>
		{else}
			{if $inputLink}
				<span class="inp-flex">
			{/if}

			<label n:if="$label" n:name="{$name}" class="inp-label {if $labelClass}{$labelClass}{/if}">
				{$label|noescape}
			</label>
			{if $required && $required->isRequired() && $showStar} <span class="inp-required">*</span>
			{elseif $form[$name]->isRequired() && $showStar} <span class="inp-required">*</span>{/if}

			{if $inputLink}
					{$inputLink}
				</span>
			{/if}

			{if $type == 'number'}
				<span class="inp-fix">
					{php $className = 'inp-text '.$className}
					{input $name class=>$className, cols=>$cols, rows=>$rows, disabled=>$disabled, type=>number}
				</span>
			{elseif $type == 'file'}
				{capture $dataText}{_select_file}{/capture}
				{input $name class=>'inp-file', data-text=>$dataText}
			{elseif $type == 'select'}
				<span class="inp-fix inp-fix--select">
					{input $name class=>['inp-select', $className]}
				</span>
			{else}
				<span class="inp-fix">
					{php $className = 'inp-text '.$className}
					{input $name class=>$className, cols=>$cols, rows=>$rows, disabled=>$disabled}
				</span>
			{/if}
		{/if}
	{if !$noP}</p>{/if}
{/if}
