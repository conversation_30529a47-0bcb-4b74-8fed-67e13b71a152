<?php declare(strict_types = 1);

namespace FrontModule;

use App\Model\ProductVariant;
use App\Model\Subscription;
use App\Model\SubscriptionItem;
use App\Model\SubscriptionItemType;
use App\Model\SubscriptionModel;
use App\Model\SubscriptionOrder;
use App\Model\SubscriptionPayment;
use Nette\Http\Session;
use SuperKoderi\Components\ISubscriptionActivationFormFactory;
use SuperKoderi\Components\ISubscriptionCancelFormFactory;
use SuperKoderi\Components\ISubscriptionFormFactory;
use SuperKoderi\Components\SubscriptionActivationForm;
use SuperKoderi\Components\SubscriptionCancelForm;
use SuperKoderi\Components\SubscriptionForm;
use SuperKoderi\EasyMessages;
use SuperKoderi\Heureka;
use SuperKoderi\IFilterCacheFactory;
use Throwable;
use Tracy\Debugger;
use Tracy\Ilogger;

class SubscriptionPresenter extends BasePresenter
{

	/** @inject */
	public Session $session;

	/** @inject */
	public ISubscriptionFormFactory $subscriptionFormFactory;

	/** @inject */
	public ISubscriptionActivationFormFactory $subscriptionActivationFormFactory;

	/** @inject */
	public Heureka $heureka;

	/** @inject */
	public EasyMessages $easyMessages;

	private Subscription|null $subscription = null;

	private SubscriptionOrder|null $subscriptionOrder = null;

	/** @inject */
	public IFilterCacheFactory $filterCacheFactory;

	/** @inject */
	public ISubscriptionCancelFormFactory $subscriptionCancelForm;

	public function startup(): void
	{
		parent::startup();

		if (!$this->mutation->isEshop && !isset($_COOKIE['CLIENT_TESTING'])) {
			$this->redirect('UID|404');
		}

		$this->template->resizer = $this->imageResizer;
	}

	public function actionDefault(): void
	{
		$this->setObject($this->orm->tree->getByUid('userSubscriptions'));
	}

	public function renderDefault(): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		$this->template->subscriptions = $this->orm->subscription->findBy(['user' => $this->userEntity, 'status!=' => [Subscription::STATUS_DRAFT]]);
	}

	public function actionDetail(?string $hash = null): void
	{
		$this->setObject($this->orm->tree->getByUid('subscriptionDetail'));

		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		if ($hash === null) {
			$this->redirect('UID|userSubscriptions');
		}
		$subscription = $this->orm->subscription->getByHash($hash);

		if ($subscription->status === Subscription::STATUS_ACTIVE && ($subscription->nextSubscriptionOrder === null && $subscription->reservedSubscriptionOrder === null)) {
			Debugger::log(sprintf('Subscription ID %s has no next order and it is active', $subscription->id), ILogger::ERROR);
			$this->subscriptionModel->logSubscriptionError($subscription, 'Subscription has no next order and it is active');
		}

		if ($subscription instanceof Subscription && $subscription->user->id == $this->userEntity->id) {

			$this->setSubscription($subscription);
			$this->setSubscriptionOrder(null);
			$this->template->subscription = $subscription;
		} else {
			$this->redirect('UID|userSubscriptions');
		}

		$filterCache = $this->filterCacheFactory->create($this->mutation);
		$filterParams = $filterCache->decoratePost([], $this->userEntity);
		$filter = $filterCache->getByPost($filterParams, true, null, true);

		$this->template->products = $this->orm->product->findFilteredProducts(array_slice($filter->content->products, 0, 10));
	}

	public function actionActivate(?string $hash = null): void
	{
		$this->setObject($this->orm->tree->getByUid('subscriptionActivation'));

		if ($hash === null) {
			$this->redirect('UID|userSubscriptions');
		}
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		$subscription = $this->orm->subscription->getByHash($hash);
		if ($subscription instanceof Subscription && $subscription->user->id == $this->userEntity->id && $subscription->status === Subscription::STATUS_DEACTIVATED) {
			$this->setSubscription($subscription);
			$this->template->subscription = $subscription;
		} else {
			$this->redirect('UID|userSubscriptions');
		}
	}

	public function handleSkipNextOrder(string|null $subscriptionHash): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			if (
				$subscription instanceof Subscription &&
				$subscription->user->id == $this->userEntity->id) {
				$this->subscriptionModel->skipNextOrder($subscription);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('UID|userSubscriptions');
	}

	public function handleDeactivateSubscription(string $subscriptionHash): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			if (
				$subscription instanceof Subscription &&
				$subscription->user->id == $this->userEntity->id) {
				$this->subscriptionModel->deactivateSubscription($subscription);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('UID|userSubscriptions');
	}

	public function handleCreateRecurringPayment(string $subscriptionHash): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			if (
				$subscription instanceof Subscription &&
				$subscription->user->id == $this->userEntity->id) {
				$subscriptionPayment = $this->subscriptionModel->activateRecurringPayment($subscription);

				if ($subscriptionPayment instanceof SubscriptionPayment) {
					$this->redirectUrl($subscriptionPayment->paymentUrl);
				}
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('UID|userSubscriptions');
	}

	public function handleCancelPayments(string $subscriptionHash): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			if (
				$subscription instanceof Subscription &&
				$subscription->user->id == $this->userEntity->id) {
				$this->subscriptionModel->cancelRecurringPayment($subscription);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('UID|userSubscriptions');
	}

	public function handleRemoveFromSubscription(int $id, string $subscriptionHash, ?int $nextOrderId) : void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			$subscriptionItem = $this->orm->subscriptionItem->getById($id);
			$subscriptionOrder = null;
			if ($nextOrderId) {
				$subscriptionOrder = $this->orm->subscriptionOrder->getById($nextOrderId);
			}

			if ($subscriptionItem instanceof SubscriptionItem && $subscription instanceof Subscription && $subscription->user->id == $this->userEntity->id) {

				if ($subscriptionItem->type == SubscriptionItemType::SUBSCRIPTION->value && count($subscription->subscriptionItems) === 1) {
					throw new \Exception('subscription_cannot_remove_last_item');
				}
				$this->subscriptionModel->removeItemFromSubscription($subscriptionItem->type, $subscription, $subscriptionItem->productVariant, $subscriptionOrder);
			}

			$this->template->message = 'subscription_item_successfully_removed';
			$this->template->isError = false;
		} catch (Throwable $e) {
			$this->template->message = $e->getMessage();
			$this->template->isError = true;
			$this->redrawControl();
			$this->flashMessage($e->getMessage(), 'error');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleIncreaseAmountOnItem(string $subscriptionHash, int $itemId, int $amount): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			$subscriptionItem = $this->orm->subscriptionItem->getById($itemId);

			if ($subscriptionItem instanceof SubscriptionItem && $subscription instanceof Subscription && $subscription->user->id == $this->userEntity->id) {
				$this->subscriptionModel->setAmountForItemToSubscription($subscriptionItem, $amount + 1);
			}

			$this->template->message = '';
		} catch (Throwable $e) {
			$this->template->message = $e->getMessage();
			$this->redrawControl();
			$this->flashMessage($e->getMessage(), 'error');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleDecreaseAmountOnItem(string $subscriptionHash, int $itemId, int $amount): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			$subscriptionItem = $this->orm->subscriptionItem->getById($itemId);

			if ($subscriptionItem instanceof SubscriptionItem && $subscription instanceof Subscription && $subscription->user->id == $this->userEntity->id) {
				$this->subscriptionModel->setAmountForItemToSubscription($subscriptionItem, $amount - 1);
			}

			$this->template->message = '';
		} catch (Throwable $e) {
			$this->template->message = $e->getMessage();
			$this->redrawControl();
			$this->flashMessage($e->getMessage(), 'error');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function setSubscription(Subscription|null $subscription): void
	{
		$this->subscription = $subscription;
	}

	public function setSubscriptionOrder(SubscriptionOrder|null $subscriptionOrder): void
	{
		$this->subscriptionOrder = $subscriptionOrder;
	}

	protected function createComponentSubscriptionForm(): SubscriptionForm
	{
		return $this->subscriptionFormFactory->create($this->mutation, $this->subscription, $this->userEntity, $this->subscriptionOrder);
	}

	protected function createComponentSubscriptionNextOrderForm(): SubscriptionForm
	{
		return $this->subscriptionFormFactory->create($this->mutation, $this->subscription, $this->userEntity, $this->subscription->nextSubscriptionOrder);
	}

	protected function createComponentSubscriptionActivationForm(): SubscriptionActivationForm
	{
		return $this->subscriptionActivationFormFactory->create($this->subscription, $this->userEntity);
	}

	protected function createComponentSubscriptionCancelForm(): SubscriptionCancelForm
	{
		return $this->subscriptionCancelForm->create($this->userEntity);
	}

}
