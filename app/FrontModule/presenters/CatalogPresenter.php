<?php

namespace FrontModule;

use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\SetupCreator\Catalog\BasicElasticItemListFactory;
use App\Model\BucketFilter\SetupCreator\Catalog\BoxListFactory;
use App\Model\BucketFilter\SetupCreator\Catalog\ElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\Parameter;
use App\Model\ParameterModel;
use App\Model\ParameterValueModel;
use App\Model\Product;
use App\Model\ProductVariant;
use App\Model\SeolinkModel;
use App\Model\Tree;
use App\Model\UserModel;
use App\Model\Voucher;
use Curl\ArrayUtil;
use Nette\Application\UI;
use Nette\Utils\Arrays;
use Nette\Utils\Strings;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use SuperKoderi\Components\IMessageForFormFactory;
use SuperKoderi\FilterCache;
use SuperKoderi\GtmPageDataEvent;
use SuperKoderi\IFilterCacheFactory;
use function Sodium\compare;

/**
 * @method ProductVariant|Tree getObject()
 */
class CatalogPresenter extends BasePresenter
{

	/** @inject */
	public ParameterModel$parameterModel;

	/** @inject */
	public ParameterValueModel $parameterValueModel;

	public array $get = array();

	public array $seo = array();

	/** @inject */
	public IMessageForFormFactory $messageForFormFactory;

	/** @inject */
	public IFilterCacheFactory $filterCacheFactory;

	/** @inject */
	public SeolinkModel $seolinkModel;

	private FilterCache $filterCache;

	protected ?array $filterParams = null;

	protected ?array $filterAnimals = null;

	protected ?int $filterAnimal;

	private $cleanFilterParam;

	private $filter;

	/** @persistent */
	public array $sIds = [];

	/** @inject */
	public BucketFilterFactory $bucketFilterFactory;

	/** @inject */
	public BasicElasticItemListFactory $basicElasticItemListFactory;

	/** @inject */
	public ElasticItemListFactory $elasticItemListFactory;

	/** @inject */
	public  BoxListFactory $boxListFactory;

	/** @inject */
	public  SortCreator $sortCreator;

	public function startup()
	{
		parent::startup();

		$filterParams = $this->request->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}

		$this->cleanFilterParam = $this->filterParams;

		if (isset($this->params['filter']) && is_array($this->params['filter'])) {
			$this->seolink = $this->seolinkModel->getByFilter($this->mutation, $this->params['filter'], $this->params);
		} else {
			$cond = [
				'isDefault' => 1,
				'isActive' => 1,
				'mutation' => $this->mutation,
			];
			if (isset($this->params['show'])) {
				unset($cond['isActive']);
			}
			$this->seolink = $this->orm->seolink->getBy($cond);
		}
		$this->template->seolink = $this->seolink;
	}

	public function actionDefault($object, array $filter, array $pets, int $pet = 0)
	{
		$this->setObject($object);

		$this->filterAnimals = $this->request->getParameter('pets');
		$this->filterAnimal = $this->request->getParameter('pet');
		$this->pushPageDataEvent('category', $this->userEntity);
	}


	public function renderDefault(Tree $object, string $order = "sort", $seoLink = null)
	{
		// for filter by animal
		$this->template->animalsInfo = $this->getAnimalInfo();

		if (isset($this->filterParams['order'])) {
			$order = $this->filterParams['order'];
		}

		$basicElasticItemListGenerator = $this->basicElasticItemListFactory->create($this->object);
		$elasticItemListGenerator = $this->elasticItemListFactory->create($this->object, $this->filterParams, $this->mutation);
		$boxListGenerator = $this->boxListFactory->create($this->object);

		$bucketFilter = $this->bucketFilterFactory->create(
			$basicElasticItemListGenerator,
			$elasticItemListGenerator,
			$boxListGenerator,
			$this->mutation,
		);
		$sort = $this->sortCreator->create($order, $this->mutation, $this->userEntity);

		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $this->getObject();
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('shop', 'productsPaging');

		$filter = $bucketFilter->getFilter($this->filterParams);
		$itemsObject = $bucketFilter->getItems($paginator->itemsPerPage, $paginator->offset, $sort);

		$paginator->itemCount = $itemsObject->totalCount;


		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;

		/** @var $sIds array Specific Ids to show specified in URL */
		$sIds = $this->request->getParameter('sIds') ?? [];

		if (is_string($sIds)) {
			$sIds = explode(',', $sIds);
		}

//		if (!empty($sIds)) {
//			$products = $this->orm->product->findFilteredProducts($sIds);
//			$sIds = $products->findBy(['public' => 1, 'mutation' => $this->mutation])->fetchPairs(null, 'id');
//		}

//		$this->prepareFilter($this->mutation, $this['pager'],null, $sIds);

		$flagEntities = $this->orm->flag->findBy([
			'mutation' => $this->mutation,
			'virtualFlag!=' => null,
		])->fetchAll();

		$flagEntitiesByVirtualFlagId = [];
		foreach ($flagEntities as $entity) {
			$flagEntitiesByVirtualFlagId[$entity->virtualFlag] = $entity;
		}

		$this->template->flagEntitiesByVirtualFlagId = $flagEntitiesByVirtualFlagId;

		$this->template->cleanFilterParam = $this->cleanFilterParam;
		$this->template->filter = $filter;

		$this->template->catalogOrder = $order;
		$this->template->categoriesProductCount = $filter->categories ?? [];
		$this->template->products = $itemsObject->items;
		$this->template->productCount = $itemsObject->totalCount;
		$this->template->seoLink = $seoLink;
		$this->template->linkSeo = $this->linkSeo;


		$this->template->setFile(FE_TEMPLATE_DIR . '/Catalog/default.latte');

		$this->template->currentPage = $paginator->getPage();
		$this->template->pagesCount = $paginator->getPageCount();

		// user registration views
		$userRegistrationThankYou = $this->request->getParameter('registrationThankYou');
		$this->template->userRegistrationThankYou = in_array($userRegistrationThankYou, ['user', 'breeder']) ? $userRegistrationThankYou : null;
		$userRegistrationActivated = $this->request->getParameter('registrationActivated');
		$this->template->userRegistrationActivated = in_array($userRegistrationActivated, ['user', 'breeder']) ? $userRegistrationActivated : null;

		if (isset($_COOKIE['redirectToId']) && $userRegistrationActivated) {
			$redirectToId = $this->presenter->getHttpRequest()->getCookie('redirectToId');
			$this->presenter->getHttpResponse()->deleteCookie('redirectToId');

			$variant = $this->orm->productVariant->getById($redirectToId);
			$this->presenter->redirect('ALIAS|' . $variant->alias);
		}


		$this->template->allParameters = $this->orm->parameter->findAll()->fetchPairs('uid', null);
//		$this->template->tooltips = $this->orm->parameter->findAll()->fetchPairs('uid', 'tooltip');
		$this->template->uidToUnitMap = $this->parameterModel->getUidToUnitMap();
		$this->template->uidToName = $this->parameterModel->getUidToNameMap();
//		$this->template->forceOpen = $this->parameterModel->getForceOpen();
		$this->template->variantParameterValues = $this->parameterValueModel->getVariantParameterValues();

		if (!$this->seolink) {
			$this->template->robots = 'noindex, follow';
		}

		if ($this->isAjax()) {
			if (isset($this->cleanFilterParam)) {
				$this->payload->newUrl = urldecode(htmlspecialchars_decode($this->link('//this', ['filter' => $this->filterParams, 'pets' => $this->filterAnimals])));
			}


			if (isset($this->params['more']) || isset($this->filterParams['order'])) {
				$this->redrawControl('productsInner');
				$this->redrawControl('productsPagerTop');
				$this->redrawControl('productsPagerBottom');
				$this->redrawControl('productList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl();
				}
			}
//


		}
	}


	public function actionVisited()
	{
		$this->setObject($this->orm->tree->getByUid('lastVisited'));

		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $this->getObject();

		if (isset($this->params['more'])) {
			$this['pager']->setStartPage($this->params['more']);
		}

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->getParam('shop', 'productsPaging');

		$products = $this->visitedProduct->getAll($this->presenter->getName(), $this->params);

		if (!$products->count()) {
			$this->redirect('UID|eshop');
		}

		$paginator->itemCount = $products->count();

		// kontrola prekroceni limitu strankovani
		if ($paginator->isBadLink($this->getRequest()->getParameters())) {
			$this->redirect("this", ['pager-page' => $paginator->getLastPage()]);
			$this->terminate();
		}

		$products = new \LimitIterator($products, $paginator->offset, $paginator->itemsPerPage);
		$this->template->products = $products;

		if (isset($this->params['more'])) {
			$this->redrawControl('productsInner');
			$this->redrawControl('productsPagerTop');
			$this->redrawControl('productsPagerBottom');
			$this->redrawControl('productList');
		} else {
			$this->redrawControl('visitedList');
		}
	}


	public function beforeRender()
	{
		$this->template->isOpenInCookie = function ($name) {
			$cookie = $this->httpRequest->getCookie('isOpen' . $name, 0);
			return $cookie;
		};

		$specialUid = ['discounts', 'catalog', 'news'];
		if ($this->getObject() && !in_array($this->getObject()->uid, $specialUid) && $this->getObject()->template == 'Catalog:default' && isset($this->filter->dials)) {

			$params = [];
			foreach ($this->filter->dials as $dialName => $dialValues) {

				if (in_array($dialName, $this->configService->getParam('importantParams'))) {
					$activeValues = [];
					foreach ($dialValues as $dialValue) {
						if ($dialValue->isActive) {
							$activeValues[] = $dialValue->value;
						}
					}
					$params[$dialName] = $activeValues;
				}
			}

			if (!in_array($this->getObject()->uid, ['eshopRental', 'catalog', 'discounts', 'news'])) {

				if ($this->cleanFilterParam && $this->getObject()->seoTitleFilter) {
					$this->getObject()->name = $this->getObject()->getSeoTitleFilterWithReplace($this->cleanFilterParam);
					$this->getObject()->nameTitle = $this->getObject()->getSeoTitleFilterWithReplace($this->cleanFilterParam);
					$this->getObject()->nameAnchor = $this->getObject()->getSeoTitleFilterWithReplace($this->cleanFilterParam);

				} else {
//					$this->getObject()->name = $this->makeSeoName($params, $this->getObject()->name);
//					$this->getObject()->nameTitle = $this->makeSeoName($params, $this->getObject()->nameTitle);
//					$this->getObject()->nameAnchor = $this->makeSeoName($params, $this->getObject()->nameAnchor);
				}

				if ($this->cleanFilterParam && $this->getObject()->seoAnnotationFilter) {
					$this->getObject()->annotation = $this->getObject()->getSeoAnnotationFilterWithReplace($this->cleanFilterParam);
				}


				if ($this->cleanFilterParam && $this->getObject()->seoTitleFilter) {
					$this->getObject()->description = $this->getObject()->getSeoDescriptionFilterWithReplace($this->cleanFilterParam);
				}
			}
		}

		parent::beforeRender();

		if ($this->userEntity) {
			$this->template->animals = $this->userEntity->animals->toCollection()->fetchAll();
		}
	}


	private function prepareFilter($mutation, $paginator, $category = null, $sIds = [])
	{
		$this->filterCache = $this->filterCacheFactory->create($mutation);

		if (!$this->filterParams) {
			$this->filterParams = [];
		}

		$this->animalFilterParamsPrepare();
		$this->cleanFilterParam = $this->filterParams;

		$this->filterCache->cleanFilterCache();
		$this->filterParams = $this->filterCache->decoratePost($this->filterParams, $this->userEntity, $category);

		$filter = $this->filterCache->getByPost($this->filterParams, false, $this->userEntity);

		if (!empty($sIds)) {
			$filter->content->products = $sIds;
			$filter->content->count = count($sIds);
		}

		if (isset($filter->content)) {

			if (isset($this->params['more'])) {
				$this['pager']->setStartPage($this->params['more']);
			}

			$paginator = $this['pager']->getPaginator();
			$this['pager']->enableNofollow($this->linkSeo->hasNofollow($this->link('this'), ['filter' => $this->cleanFilterParam]));

			$paginator->setItemCount(count($filter->content->products));

			$paginator->setItemsPerPage($this->configService->get('shop', 'productsPaging'));

			$filter->content->products = array_slice($filter->content->products, $paginator->offset, $paginator->itemsPerPage);

			if (isset($filter->content->products) && $filter->content->products) {
				if ($filter->content->products) {
//					$filter->content->products = $this->orm->productVariant->findFilteredVariants($filter->content->products);
					// TODO
					$filter->content->products = $this->orm->product->findFilteredProducts($filter->content->products);
				} else {
					$filter->content->products = new \ArrayIterator();
				}
			} else {
				$filter->content->products = new \ArrayIterator();
			}

			$this->template->currentPage = $paginator->getPage();

			if ($this->userEntity) {
				$userAnimalTypes = $this->userEntity->animalTypeList;
				$animalActive = $animalDisabled = [];

				if (is_array($this->filterAnimals)) {
					$animalActive = $this->filterAnimals;
					if (in_array(array_key_first($this->filterAnimals), $userAnimalTypes['dog']['ids'])) { // podle prvniho poznam, jestli jsou checked jen psi nebo kocky
						$animalDisabled = $userAnimalTypes['cat']['ids'];
					} else {
						$animalDisabled = $userAnimalTypes['dog']['ids'];
					}
				}

				$filterAnimalsData = [
					'active' => $animalActive,
					'disabled' => array_flip($animalDisabled),
					'data' => $userAnimalTypes,
				];

				$filter->content->animals = $filterAnimalsData;
			}
			$this->filter = $filter->content;
		}

		// kontrola prekroceni limitu strankovani
		if ($paginator->isBadLink($this->getRequest()->getParameters())) {
			$this->redirect("this", ['pager-page' => $paginator->getLastPage()]);
			$this->terminate();
		}
	}





}
