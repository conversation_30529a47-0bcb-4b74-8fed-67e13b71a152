<?php

namespace FrontModule;

use App\Model\OrderItem;
use App\Model\ProductVariant;
use App\Model\Subscription;
use App\Model\SubscriptionItemType;
use App\Model\SubscriptionModel;
use App\Model\SubscriptionOrder;
use App\Model\SubscriptionOrderModel;
use App\Model\User;
use App\Model\UserAnimal;
use App\Model\UserHash;
use App\Model\UserHashModel;
use App\Model\UserModel;
use Nette\Application\AbortException;
use Nette\Application\ForbiddenRequestException;
use Nette\Application\UI\InvalidLinkException;
use Nette\Http\Session;
use Nette\Security\AuthenticationException;
use Nette\Security\SimpleIdentity;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi;
use SuperKoderi\Components;
use SuperKoderi\EasyMessages;
use Tracy;
use VisualPaginator;
use App\FrontModule\Components\LotteryForm\ILotteryFormFactory;

class UserPresenter extends BasePresenter
{

	/** @inject */
	public Components\IRegistrationFormFactory $registrationFormFactory;

	/** @inject */
	public Components\IRegistrationAnimalFormFactory $registrationAnimalFormFactory;

	/** @inject */
	public Components\IRegistrationAgreementFormFactory $registrationAgreementFormFactory;

	/** @inject */
	public Components\IProfileFormFactory $profileFormFactory;

	/** @inject */
	public Components\ILostPasswordFormFactory $lostPasswordFormFormFactory;

	/** @inject */
	public Components\IAnimalEditFormFactory $animalEditFormFactory;

	/** @inject */
	public UserModel $userModel;

	/** @inject */
	public UserHashModel $userHashModel;

	/** @inject */
	public Session $session;

	/** @persistent */
	public string $backlink = '';

	/** @inject */
	public Components\IMessageForFormFactory $messageForFormFactory;

	/** @inject */
	public VisualPaginator $visualPaginator;

	/** @inject */
	public EasyMessages$easyMessages;

	/** @inject */
	public ILotteryFormFactory $lotteryFormFactory;

	private ?UserAnimal $animal = null;

	private ?User $newUser = null;

	/** @inject */
	public Components\ISubscriptionCancelFormFactory $subscriptionCancelForm;

	public function startup()
	{
		$idref = $this->params['idref'];

		// TODO: do routeru?
		if (!$this->mutation->isEshop && !isset($_COOKIE['CLIENT_TESTING'])) {
			$this->redirect("UID|404");
		}

		$object = $this->orm->tree->getById($idref);
		parent::startup();

		if ($this->user->loggedIn) {
			$this->hideInSideMenu = array('userLogin', 'lostPassword', 'registration');

			$this->userEntity = $this->orm->user->getById($this->user->getId());

			if ($this->userEntity === NULL) {
				$this->userEntity = new User();
			}

			$showInSideMenu = ['invoices', 'userOrderHistory', 'userProfil', 'resetPassword'];

			$logOut = new \stdClass();
			$logOut->page = new \stdClass();
			$logOut->page->id = 1;
			$logOut->page->alias = $object->alias . '?do=logout';
			$logOut->page->nameAnchor = "Odhlásit se"; //
			$logOut->active = 0;
			$logOut->selected = 0;

			$this->addToSideMenu = $logOut;
		} else {
			$this->hideInSideMenu = array('userFavourite', 'userProfil', 'newsletterLogOut', 'userReview');
			$showInSideMenu = [];
		}


		$this->setObject($object);

		if ($this->getObject() && is_array($this->getObject()->path)) {
			$parentID = $this->getObject()->path[0];
			if ($parentID) {
				$parent = $this->orm->tree->getByUid("userSection");

				$this->menu->side = $this->menuService->getMenu($parent->id, $this->getObject(), true, $showInSideMenu, $this->hideInSideMenu);

				if (is_object($this->addToSideMenu)) {
					$this->menu->side[] = $this->addToSideMenu;
				} elseif (is_array($this->addToSideMenu)) {
					foreach ($this->addToSideMenu as $item) {
						$this->menu->side[] = $item;
					}
				}
			}
			$this->menu->breadcrumb = $this->menuService->getBreadcrumb($this->getObject());
		}
	}


	public function actionDefault()
	{
		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl();
			}

			// objednavky
			$this->template->orders = $this->orm->order->findBy([
				'user' => $this->userEntity,
				'isDeleted' => 0
			])->resetOrderBy()->orderBy('created', ICollection::DESC)->limitBy(5);

			$this->template->ordersAll = $this->orm->order->findBy([
				'user' => $this->userEntity,
				'isDeleted' => 0
			])->resetOrderBy()->orderBy('created', ICollection::DESC);

			// mazlicci
			$this->template->animals = $this->orm->userAnimal->findBy(['user' => $this->userEntity]);

			// predplatne
			$this->template->subscriptions = $this->userEntity->activeSubscriptions;

			// TODO
			// oblibene produkty

			foreach ($this->easyMessages->get(EasyMessages::KEY_USER) as $msg) {
				$this->flashMessage($msg->text, $msg->type);
			}
		} else {
			$this->redirectToLoginPage();
		}

		$this->pushPageDataEvent('my-account', $this->userEntity);
	}

	/**
	 * @throws AbortException
	 */
	public function actionLogin(string $hash = null): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		$this->template->isUserActivation = false;

		if (isset($hash)) {
			$this->template->isUserActivation = true;
			$this->handleActivationRegistration($hash);
		}

		if ((bool)$this->getParameter('ret')) {
			$this->getHttpResponse()->setCookie(SocialPresenter::COOKIE_LOGIN_RETURN_NAME, 'order', 0);
		}
	}


	public function renderLogin(): void
	{
		$this->pushPageDataEvent('login', $this->userEntity);
		$this->addSocialEasyMessage();
	}


	public function actionFavourite(): void
	{
		if (!$this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		$this->pushPageDataEvent('favourite', $this->userEntity);
	}


	public function actionLostPassword(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		$this->pushPageDataEvent('forgotten_password', $this->userEntity);
	}

	public function renderLostPassword(): void
	{
//        if ($this->isAjax()) {
//            $this->redrawControl();
//        }
	}


	public function actionProfil()
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}

		// mame zatrhnout priznak newsletter
		if ($this->user) {
			//dump($this->user->identity->data);
			//$this->template->isNewsletter = $this->newsletterService->isInNewsletter($this->user->identity->data['Multiplier']);

		}

		$this->pushPageDataEvent('my-account', $this->userEntity);
	}


	public function actionOrderHistory()
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}
		$this->template->orders = $this->orm->order->findBy([
			'user' => $this->userEntity,
			'isDeleted' => 0
		])->resetOrderBy()->orderBy('created', ICollection::DESC);


		if (isset($this->params['repeatOrder']) && $this->params['repeatOrder']) {
			$order = $this->orm->order->getById($this->params['repeatOrder']);
			if (!$order || ($order && $order->user->id != $this->userEntity->id)) {
				$this->redirect("this");
			}

			$this->basket->cleanAll();

			$error = 0;
			$replacement = 0;
			$ok = 0;
			foreach ($order->products as $p) {
				$product = $p->product;
				$variant = $p->variant;

				if (isset($product) && $product !== false && (bool) $product->isSale
					&& isset($variant) && $variant !== false && (bool) $variant->totalSupplyCount
				) {
					$this->basket->addProductVariant($variant, $p->amount);
					$ok++;
				} else {
					$error++;
					if (isset($variant) && $variant !== false && !$variant->isRealSale) {
						// produkt se jiz neprodava

						if ($variant->compensationReal !== null) { // varianta ma prodejnou nahradu
							$this->basket->addProductVariant($variant->compensationReal, $p->amount);
							$ok++;
							$replacement++;
						}
					}
				}
			}

			foreach ($order->subscriptions as $orderItem) {
				$this->basket->addProductVariant(variant: $orderItem->variant, amount: $orderItem->amount, type: OrderItem::TYPE_SUBSCRIPTION);
			}

			$repeatState = $this->session->getSection("basketRepeat");
			if ($ok == 0) {
				// nic se nevlozilo
				// zustavam na historii
			} else {
				if ($error == 0) {
					// Všechny produkty se korektně vložily do košíku
					$repeatState->state = "ok";

				} else {
					if ($replacement) {
						// Část produktů byla nahrazena novějšími náhradami
						$repeatState->state = "replace";

					} else {
						// Pouze část produktů se vložila do košíku a některé byly vyřazeny (už je nelze koupit)
						$repeatState->state = "part";

					}
				}
				$this->redirect("UID|basket");
				exit();
			}
		}

		$this->pushPageDataEvent('order_history', $this->userEntity);
	}


	public function actionRegistration()
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		if (!empty($this->session->getSection(Components\RegistrationForm::SESSION_SECTION_REGISTRATION)->values['id'])) {
			$this->redirect('UID|registrationAgreement');
		}

		$this->pushPageDataEvent('registration', $this->userEntity);
	}

	public function renderRegistration()
	{
		$this->addSocialEasyMessage();
	}

	public function actionRegistrationAnimal()
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		$this->checkSessionRegistration();
	}

	public function actionRegistrationAgreement()
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		$this->checkSessionRegistration();
	}

	/**
	 * @throws AbortException
	 */
	private function checkSessionRegistration()
	{
		if (empty($this->session->getSection(Components\RegistrationForm::SESSION_SECTION_REGISTRATION)->values)
			|| empty($this->session->getSection(Components\RegistrationForm::SESSION_SECTION_REGISTRATION)->values['id'])) {
			$this->redirect('UID|registration');
		}
		// new User ID z prev kroku
		$this->newUser = $this->orm->user->getBy([
			'id' => $this->session->getSection(Components\RegistrationForm::SESSION_SECTION_REGISTRATION)->values['id'],
			'unfinishedRegistration' => 1
		]);

		if (!$this->newUser) {
			$this->session->getSection(Components\RegistrationForm::SESSION_SECTION_REGISTRATION)->remove();
			$this->redirect('UID|registration');
		}
	}

	/**
	 * @param null $id
	 * @throws ForbiddenRequestException
	 */
	public function actionAnimalEdit($id = null)
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}

		$this->template->animal = null;

		if ($id) {
			$this->animal = $this->userEntity->animals->toCollection()->getById($id);

			if (!$this->animal) {
				throw new ForbiddenRequestException(sprintf('Unknown Animal ID %s', $id));
			}

			if ($this->animal && $this->animal->user->id != $this->presenter->getUser()->id) {
				throw new ForbiddenRequestException(sprintf('Editing Animal ID %s is not allowed for user ID %s', $this->animal->user->id, $this->presenter->getUser()->id));
			}
		}

		$this->template->animal = $this->animal;
	}

	public function createComponentRAnimalEditForm()
	{
		return $this->animalEditFormFactory->create($this->getObject(), $this->animal);
	}

	/**
	 * @throws AbortException
	 * @throws InvalidLinkException
	 */
	public function actionResetPassword(string $hashToken = ''): void
	{
		if ((bool)$hashToken) {
			$userHash = $this->orm->userHash->getBy(["hash" => $hashToken]);

			if (!(isset($userHash) && $userHash->isValid())) {
				$this->getComponent('lostPasswordForm')->flashMessage("reset_password_expired_link", "error");
				$this->redirect("UID|lostPassword");
			}

		} else {
			$this->getComponent('lostPasswordForm')->flashMessage("reset_password_no_valid_link", "error");
			$this->redirect(301, "UID|lostPassword");
		}

	}


	public function createComponentRegistrationForm()
	{
		return $this->registrationFormFactory->create($this->getObject());
	}

	public function createComponentRegistrationAnimalForm()
	{
		return $this->registrationAnimalFormFactory->create($this->getObject(), $this->pages, $this->newUser);
	}

	public function createComponentRegistrationAgreementForm()
	{
		return $this->registrationAgreementFormFactory->create($this->getObject(), $this->pages, $this->newUser);
	}

	public function createComponentProfileForm()
	{
		return $this->profileFormFactory->create($this->getObject(), $this->userEntity);
	}


	public function createComponentLostPasswordForm(): Components\LostPasswordForm
	{
		$hash = '';
		$params = $this->getParameters();
		if (isset($params['hashToken'])) {
			$hash = $params['hashToken'];
		}
		return $this->lostPasswordFormFormFactory->create($this->getObject(), $this->pages, $hash);
	}


	public function createComponentVp()
	{
		return $this->visualPaginator;
	}


	public function handleRepeatOrder($orderId)
	{
		$order = $this->orm->order->getById($orderId);

		if ($order->user->id == $this->userEntity->id) {
			foreach ($order->products as $orderItem) {
				$this->basket->addProductVariant(variant: $orderItem->variant, amount: $orderItem->amount);
			}
		}
		$this->redirect('UID|step1');
	}


	private function redirectToLoginPage()
	{
		// presmerovani na prihlasovaci stranku, v pripade pristupu do zabezpecene sekce

		// kdyz je ajax a fancybox=true - JS presmerovani
		if ($this->isAjax()) {
			if (isset($_GET['fancybox'])) {
//				$this->presenter->setLayout(false);
//				$this->setView("extra");
				$url = $this->link('UID|userLogin');
//				echo '<meta http-equiv="refresh" content="0; url='.$url.'" />';
				echo '<script>
                            window.location = "' . $url . '"
						</script>';

				$this->terminate();
			}
		}

		//, array('backlink' => $this->storeRequest())
		$this->redirect('UID|userLogin');
	}


	private function redirectToProfilePage(): void
	{
		// presmerovani na detail uziv. sekce, pri pristupu na registraci, zapomenute heslo a prihlaseni

		// kdyz je ajax a fancybox=true - JS presmerovani
		if ($this->isAjax()) {
			if (isset($_GET['fancybox'])) {
//				$this->presenter->setLayout(false);
//				$this->setView("extra");
				$url = $this->link('UID|userSection');
				//echo '<meta http-equiv="refresh" content="0; url='.$url.'" />';
				echo '<script>
                            window.location = "' . $url . '"
						</script>';

				$this->terminate();
			}
		}

		$this->redirect('UID|userSection');
	}


	/**
	 * @param string $hash
	 * @throws AbortException
	 */
	private function handleActivationRegistration(string $hash): void
	{
		$userHash = $this->userHashModel->getHash($hash, UserHash::HASH_TYPE_REGISTRATION);

		try {
			if (!isset($userHash)) {
				throw new \Exception('Unknown UserHash');
			}

			if (!$userHash->isValid()) {
				throw new SuperKoderi\LogicException('user_is_already_active');
			}

			$user = $this->orm->user->getByEmail($userHash->data['email']);

			if (!isset($user)) {
				throw new \Exception('Unknown User');
			}

			$now = new DateTimeImmutable();

			$user->isActive = 1;
			$user->activatedTime = $now;
			$user->lastLogin = $now;
			$this->orm->user->persistAndFlush($user);

			$this->userHashModel->useHash($userHash);

			$identity = new SimpleIdentity($user->id, $user->role);
			$this->presenter->getUser()->login($identity);

			$contentType = $user->isBreeding ? 'breeder' : 'user';
			$this->presenter->redirect('UID|eshop', ['registrationActivated' => $contentType]);

		} catch (SuperKoderi\LogicException $e) {
			$this->flashMessage($e->getMessage());

		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');

		} catch (AbortException $e) {
			throw $e;

		} catch (\Throwable $e) {
			Tracy\Debugger::log($e, Tracy\ILogger::ERROR);
			$this->flashMessage('registration_activated_error', 'error');
		}
	}

	public function handleDeactivateSubscription(string $subscriptionHash): void
	{
		if (!$this->user->loggedIn) {
			$this->redirect('UID|userLogin');
		}

		try {
			$subscription = $this->orm->subscription->getByHash($subscriptionHash);
			if (
				$subscription instanceof Subscription &&
				$subscription->user->id == $this->userEntity->id) {
				$this->subscriptionModel->deactivateSubscription($subscription);
			}
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->redirect('UID|userSubscriptions');
	}


	protected function createComponentMessageForForm(): Components\MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentLotteryForm()
	{
		return $this->lotteryFormFactory->create($this->getObject());
	}


	protected function addSocialEasyMessage(): void
	{
		foreach ($this->easyMessages->get(EasyMessages::KEY_SOCIAL) as $msg) {
			$this->flashMessage($msg->text, $msg->type);
		}
	}

	public function createComponentSubscriptionCancelForm(): Components\SubscriptionCancelForm
	{
		return $this->subscriptionCancelForm->create($this->userEntity);
	}

}
