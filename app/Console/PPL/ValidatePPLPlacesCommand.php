<?php declare(strict_types=1);

namespace App\Console\PPL;

use App\Console\BaseCommand;
use App\Model\Orm;
use App\Model\Subscription;
use SuperKoderi\Model\PPL\PacketNotFoundException;
use SuperKoderi\Model\PPL\PplApiClient;
use SuperKoderi\DeliveryStatusService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Tracy\Debugger;

class ValidatePPLPlacesCommand extends BaseCommand
{
    public const DEBUG_LOG_LEVEL = 'ppl-places-validation';

    /** @var string|null */
    protected static $defaultName = 'ppl:validate:places';

    public function __construct(
        protected readonly DeliveryStatusService $deliveryStatusService,
        protected readonly PplApiClient $pplApiClient,
       Orm $orm
    ) {
        parent::__construct($orm);
    }

    protected function configure(): void
    {
        $this->setDescription('Validate if PPL places exist for active subscriptions')
             ->addOption('debug', 'd', \Symfony\Component\Console\Input\InputOption::VALUE_NONE, 'Enable debug mode');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        Debugger::log('---- START PPL PLACES VALIDATION -----', self::DEBUG_LOG_LEVEL);
        $output->writeln('START PPL PLACES VALIDATION');

        $subscriptions = $this->orm->subscription->findBy([
            'status' => Subscription::STATUS_ACTIVE,
            'transportType' => 'ppl_parcel',
            'pplParcelCode!=' => null,
        ]);

        $msg = sprintf('Total subscriptions to validate: %d', $subscriptions->count());
        Debugger::log($msg, self::DEBUG_LOG_LEVEL);
        $output->writeln($msg);

        $invalidCount = 0;

        $debugMode = $input->getOption('debug');

        /** @var Subscription $subscription */
        foreach ($subscriptions as $subscription) {
            if ($debugMode) {
                $output->writeln('');
                $output->writeln("=== DEBUG: Testing subscription ID {$subscription->id} ===");
                $output->writeln("PPL Code: {$subscription->pplParcelCode}");

                // Test the PPL API directly
                $this->testPPLCode($subscription->pplParcelCode, $output);
            }

            try {
                $this->deliveryStatusService->validatePPLPlace($subscription->pplParcelCode);
                $msg = sprintf('OK | Subscription ID: %d | PPL Place code: %s is valid',
                    $subscription->id,
                    $subscription->pplParcelCode
                );
                Debugger::log($msg, self::DEBUG_LOG_LEVEL);
                $output->writeln($msg);
            } catch (PacketNotFoundException $e) {
                $invalidCount++;
                $msg = sprintf('INVALID | Subscription ID: %d | PPL Place code: %s does not exist | %s',
                    $subscription->id,
                    $subscription->pplParcelCode,
                    $e->getMessage()
                );
                Debugger::log($msg, self::DEBUG_LOG_LEVEL);
                $output->writeln($msg);
            }
        }

        $msg = sprintf('Total invalid PPL places: %d', $invalidCount);
        Debugger::log($msg, self::DEBUG_LOG_LEVEL);
        $output->writeln($msg);

        return self::SUCCESS;
    }

    private function testPPLCode(string $code, OutputInterface $output): void
    {
        try {
            $apiKeys = $this->pplApiClient->getApiKeys();
            $langCodes = ['cs', 'sk', 'pl'];

            foreach ($langCodes as $langCode) {
                if (!isset($apiKeys[$langCode])) continue;

                $output->writeln("Testing with language: {$langCode}");

                try {
                    $token = $this->pplApiClient->login($langCode);
                    $soap = $this->pplApiClient->getSoapClient();

                    // Clean code (remove KM prefix if present)
                    $cleanCode = str_starts_with($code, 'KM') ? substr($code, 2) : $code;
                    $output->writeln("  Original code: {$code}");
                    $output->writeln("  Cleaned code: {$cleanCode}");

                    // Test different parameter combinations using correct Filter structure
                    $parameterSets = [
                        ['Filter' => ['Code' => $cleanCode]],
                        ['Filter' => ['Code' => $code]], // Try with original code too
                        ['Filter' => ['CountryCode' => $this->getCountryCode($langCode), 'Code' => $cleanCode]],
                        ['Filter' => ['CountryCode' => $this->getCountryCode($langCode), 'ZipCode' => '']],
                    ];

                    foreach ($parameterSets as $i => $params) {
                        $output->writeln("  Test " . ($i + 1) . ": " . json_encode($params));

                        try {
                            $requestParams = array_merge(['Auth' => ['AuthToken' => $token]], $params);
                            $result = $soap->GetParcelShops($requestParams);

                            $output->writeln("    SUCCESS - Got response");

                            // Check response structure
                            if (isset($result->GetParcelShopsResult)) {
                                $resultData = $result->GetParcelShopsResult;

                                // Look for data (based on WSDL structure)
                                $data = $resultData->ResultData->MyApiParcelShop ??
                                       $resultData->ResultData ??
                                       null;

                                if ($data) {
                                    $shops = is_array($data) ? $data : [$data];
                                    $output->writeln("    Found " . count($shops) . " shops");

                                    foreach ($shops as $shop) {
                                        $shopCode = $shop->ParcelShopCode ?? $shop->Code ?? 'unknown';
                                        $shopName = $shop->Name ?? 'unknown';
                                        $output->writeln("      Shop: {$shopCode} - {$shopName}");
                                    }
                                    return; // Found something, exit
                                }

								$output->writeln("    No shop data found in response");
							}

                        } catch (\SoapFault $e) {
                            $output->writeln("    SOAP Error: " . $e->getMessage());
                        }
                    }

                } catch (\Exception $e) {
                    $output->writeln("  Error with {$langCode}: " . $e->getMessage());
                }
            }

        } catch (\Exception $e) {
            $output->writeln("Debug error: " . $e->getMessage());
        }
    }

    private function getCountryCode(string $langCode): string
    {
        $mapping = ['cs' => 'CZ', 'sk' => 'SK', 'pl' => 'PL'];
        return $mapping[$langCode] ?? 'CZ';
    }
}
