SET FOREIGN_KEY_CHECKS=0;

ALTER TABLE `search_log_detail`
DROP FOREIGN KEY `search_log_detail_ibfk_1`,
ADD CONSTRAINT `search_log_detail_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE `search_log_detail`
	CHANGE `mutationId` `mutationId` int(11) NOT NULL,
	CHANGE `searchTime` `searchTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CHANGE `productCount` `productCount` int(11) NOT NULL DEFAULT '0',
	<PERSON><PERSON><PERSON> `pageCount` `pageCount` int(11) NOT NULL DEFAULT '0',
	<PERSON><PERSON><PERSON> `seoCount` `seoCount` int(11) NOT NULL DEFAULT '0';

ALTER TABLE `search_log`
	CHANGE `mutationId` `mutationId` int(11) NOT NULL,
	<PERSON>ANGE `lastSearchTime` `lastSearchTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP;

SET FOREIGN_KEY_CHECKS=1;
