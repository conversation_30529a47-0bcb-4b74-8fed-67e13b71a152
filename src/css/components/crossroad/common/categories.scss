.c-categories {
	$s: &;
	display: flex;
	border-radius: $radius;
	overflow: hidden;
	box-shadow: 0 2px 10px rgba(black, 0.1);
	&__title {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100px;
		margin-bottom: 0;
		padding: 10px 0 10px 20px;
		background: $colorPrimary;
		color: white;
		font-size: $fontSizeMobile;
	}
	&__title &__svg {
		position: absolute;
		top: -1px;
		right: -34px;
		z-index: -1;
		width: 35px;
		height: calc(100% + 2px);
		color: $colorPrimary;
	}
	&__main {
		flex: 1;
		padding: 0 10px 0 30px;
	}
	&__list {
		@extend %reset-ul;
		align-items: flex-end;
		margin: 0;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		flex: 0 0 auto;
		width: auto;
		border-width: 0;
	}
	&__link {
		display: block;
		font-weight: bold;
		font-size: $fontSizeMobile;
		text-align: center;
	}
	&__item-icon {
		padding: 0;
		.icon-svg {
			width: 42px;
			height: 27px;
			color: $colorBlack;
		}
	}

	// MODIFICATIONS
	&--lg {
		#{$s} {
			&__title {
				width: 80px;
			}
			&__item {
				flex: 1 1 auto;
			}
			&__item:not(:first-child)::before {
				content: '';
				position: absolute;
				top: 0;
				left: -8px;
				width: 1px;
				height: 100%;
				background: $colorGrayLight;
				pointer-events: none;
			}
			&__link {
				padding: 8px 0 3px;
				font-size: 20px;
				text-align: center;
			}
		}
	}
	&--search &__item:first-child {
		flex: 1 1 100%;
		max-width: none;
	}
	&__link--diet {
		color: #0093c9;
	}

	// STATES
	&__link.is-active {
		color: $colorHover;
	}

	// MEDIA QUERIES
	@media ($xlDown) {
		&:not(&--lg) &__item-icon .icon-svg {
			position: relative;
			display: block;
			width: 30px;
			margin: 0 auto;
			transform: none;
		}
	}
	@media ($mdDown) {
		&__item {
			width: 25%;
		}
		&__title {
			line-height: 1em;
		}
		&--lg {
			#{$s} {
				&__item::before {
					display: none;
				}
			}
		}
	}
	@media ($xlDown) {
		&__item-icon .icon-svg {
			position: relative;
			display: block;
			width: 30px;
			margin: 0 auto;
			transform: none;
		}
	}
	@media ($smDown) {
		&__item {
			width: 33.33%;
		}
		&:not(&--lg) {
			display: block;
			#{$s} {
				&__main {
					padding: 20px 10px 15px;
				}
				&__title {
					width: 100%;
					padding: 14px;
				}
				&__title #{$s}__svg {
					display: none;
				}
				&__list {
					margin: 0;
				}
				&__item {
					border-width: 0;
				}
				&__link {
					font-size: $fontSizeMobile;
				}
			}
		}
	}
	@media ($smUp) {
		&__main {
			padding: 9px 10px 3px 45px;
		}
		&--lg &__title {
			width: 150px;
		}
		&--lg &__main {
			padding: 0 10px 0 30px;
		}
		&__list--single &__link {
			text-align: left;
		}
	}
	@media (min-width: 640px) {
		&--search &__item,
		&--search &__item:first-child {
			flex-basis: calc(33.33% - 15px);
			max-width: calc(33.33% - 15px);
		}
	}
	@media ($mdUp) {
		&__title {
			min-height: 70px;
		}
		&__main {
			padding: 0 25px 0 55px;
		}
		&__item {
			width: auto;
		}
		&__link {
			padding: 12px 0 6px;
		}
		&--lg {
			#{$s} {
				&__title {
					width: 225px;
					font-size: 20px;
				}
			}
		}
	}
	@media ($lgUp) {
		&__title {
			width: 225px;
			font-size: 20px;
		}
		&__list {
			margin: 0;
		}
		&__item {
			border-width: 0;
		}
		&__link {
			padding: 10px 0 3px;
			font-size: 20px;
		}
		&--search &__item,
		&--search &__item:first-child {
			flex: 0 0 auto;
			max-width: none;
		}
	}
	@media ($xlUp) {
		&__list {
			align-items: center;
			margin: 0 0 -15px -34px;
		}
		&--search &__list {
			margin: 0;
		}
		&__item {
			width: auto;
			border-width: 0 0 15px 34px;
		}
		&--search &__item {
			width: 25%;
			margin: 0;
		}
		&__link {
			padding: 15px 0;
			font-size: 22px;
			text-align: left;
		}
		&__item-icon {
			padding-left: 56px;

			// line-height: 38px;
			.icon-svg {
				width: 100%;
				max-width: 40px;
				height: auto;
				max-height: 38px;
			}
		}
		&--lg {
			#{$s} {
				&__item:not(:first-child)::before {
					left: -17px;
				}
				&__link {
					padding: 4px 0;
					font-size: 38px;
				}
				&__item-icon {
					padding-left: 60px;

					// line-height: 38px;
					.icon-svg {
						width: 100%;
						max-width: 40px;
						height: auto;
						max-height: 38px;
					}
				}
			}
		}
	}
}
