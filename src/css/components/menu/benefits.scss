.m-benefits {
	$s: &;
	width: 100%;
	padding-top: 10px;
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		align-items: center;
		margin-left: -20px;
		color: $colorBlack;
		font-weight: bold;
		font-size: 14px;
	}
	&__item {
		@extend %reset-ul-li;
		display: none;
		flex: 0 1 auto;
		padding: 5px 0;
		border-left: 20px solid transparent;
		&:first-child {
			display: block;
			flex: 0 1 100%;
			text-align: center;
		}
	}
	&__link {
		display: block;
		color: $colorBlack;
	}

	// States
	#{$s}__link {
		.hoverevents &:hover {
			color: $colorPrimary;
		}
	}

	// Media
	@media ($mdUp) {
		display: block;
		#{$s}__list {
			justify-content: space-between;
		}
		#{$s}__item {
			display: block;
			&:first-child {
				display: block;
				flex: 0 1 auto;
				text-align: left;
			}
		}
	}
	@media ($lgUp) {
		padding-top: 30px;
		#{$s}__list {
			font-size: 17px;
		}
		#{$s}__item {
			padding: 10px 0;
		}
	}
}
