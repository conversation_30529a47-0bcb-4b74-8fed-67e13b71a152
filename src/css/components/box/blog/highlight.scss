.b-highlight {
	@extend %box-shadow;
	position: relative;
	border-radius: $radius;
	overflow: hidden;
	&__holder {
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		padding: 20px;
	}
	&__img {
		min-height: 380px;
		max-height: 680px;
	}
	&__flags {
		margin-bottom: 15px;
	}
	&__flags .flags {
		margin: 0 0 -5px -10px;
	}
	&__flags .flag {
		position: relative;
		z-index: 2;
		min-width: 88px;
		margin-left: 10px;
	}
	&__author {
		margin-bottom: 18px;
		color: $colorText;
		font-weight: bold;
		font-size: $fontSizeMobile;
		letter-spacing: 0.07em;
		text-transform: uppercase;
	}
	&__date {
		display: block;
		margin-bottom: 5px;
		color: $colorText;
		font-size: 13px;
	}
	&__title {
		position: relative;
		margin: 0 0 25px;
		color: $colorBlack;
		font-size: 24px;
		letter-spacing: 0;
		&::after {
			content: '';
			position: absolute;
			bottom: -14px;
			left: 0;
			width: 17px;
			height: 3px;
			background: $colorPrimary;
		}
	}

	// HOVERS
	.hoverevents &__link:hover &__btn .btn__text {
		background: $colorHover;
	}

	// MEDIA QUERIES
	@media ($mdUp) {
		&__holder {
			padding: (110/1300 * 100 * 1vw) (65/1300 * 100 * 1vw);
		}
		&__title {
			font-size: (42/1300 * 100 * 1vw);
		}
	}
	@media ($xlUp) {
		&__holder {
			padding: 65px;
		}
		&__title {
			font-size: 42px;
		}
	}
}
