.b-carousel {
	$s: &;
	position: relative;
	&__inner {
		position: relative;
	}
	&__holder {
		display: flex;
		flex: 1 1 auto;
		flex-direction: column;
		justify-content: center;
		padding: 15px 20px 30px;
	}
	&__title,
	&__annot {
		color: $colorWhite;
	}

	// &__video {
	// 	position: absolute;
	// 	top: 0;
	// 	right: 0;
	// 	left: 0;
	// }
	&__prev {
		left: 5px;
		color: $colorWhite;
	}
	&__next {
		right: 5px;
		color: $colorWhite;
	}
	.slick-dots {
		position: absolute;
		top: calc(50vw - 18px);
		right: 0;
		left: 0;
	}

	// MEDIA QUERIES
	@media ($mdDown) {
		margin-right: -$rowMainGutter;
		margin-left: -$rowMainGutter;
		&__inner {
			display: flex;
			flex-direction: column;
			height: 100%;
			> * {
				width: 100%;
			}
		}
		&__holder {
			// background: $colorBlack;
			text-align: center;
		}
		&__title {
			margin-top: auto;
			margin-bottom: 4px;
			letter-spacing: -0.06em;
		}
		&__annot {
			margin-bottom: 15px;
		}
		&__prev,
		&__next {
			top: calc(28vw - 15px);
			transform: none;
		}
		&__btn .link-mask::after {
			content: none;
		}
		.slick-slide {
			max-width: 100vw;
		}
	}
	@media (min-width: 640px) {
		margin-bottom: $spacingMd;
	}
	@media ($mdUp) {
		.slick-list,
		.slick-track,
		.slick-slide,
		&__list,
		&__item,
		&__img,
		&__video,
		&__inner {
			height: 100%;
		}
		&__video {
			position: static;
		}
		&,
		.slick-list {
			border-radius: $radius;
			overflow: hidden;
		}
		.slick-dots {
			top: auto;
			bottom: 22px;
		}
		&__holder {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			padding: 60px;
		}
		&__title {
			max-width: 240px;
			margin-bottom: 22px;
			font-size: 40px;
			line-height: (50/60);
			text-shadow: 0 0 20px rgba($colorBlack, 0.7);
		}
		&__annot {
			max-width: 230px;
			line-height: 1em;
			letter-spacing: -0.06em;
		}

		// STATES
		&__inner.js-video {
			background: black;
			#{$s} {
				&__img {
					display: none;
				}
				&__holder {
					opacity: 0;
					pointer-events: none;
				}
				&__btn .btn {
					pointer-events: auto;
				}
			}
		}

		// MODIFICATIONS
		&__holder--right {
			align-items: flex-end;
			text-align: right;
		}
		@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
			&__title {
				line-height: 1em;
			}
		}
	}
	@media ($lgUp) {
		margin-bottom: $spacingLg;
		&__title {
			max-width: 370px;
			font-size: 60px;
		}
	}
}
