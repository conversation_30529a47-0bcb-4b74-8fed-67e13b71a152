.f-pet-filter {
	font-weight: bold;
	&__title {
		display: inline-block;
		margin-bottom: 12px;
		font-size: 20px;
		letter-spacing: -0.05em;
	}
	&__item {
		line-height: 1em;
	}
	&__label {
		@extend %box-shadow;
		padding: 8px 10px 8px 45px;
		color: $colorBlack;
	}
	&__inp + span {
		&::before {
			top: 50%;
			left: 12px;
			width: 25px;
			height: 25px;
			transform: translateY(-50%);
		}
		& > .icon-svg {
			top: 50%;
			left: 17px;
			width: 14px;
			transform: translateY(-50%);
		}
	}
	&__products {
		position: relative;
		transition: opacity $t;
		&::before {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 20px;
			height: 20px;
			margin: -10px 0 0 -10px;
			border: 2px solid $colorBlack;
			border-radius: 50%;
			border-top-color: transparent;
			visibility: hidden;
			opacity: 0;
			transition: opacity $t, visibility $t;
			animation: animation-rotate 0.8s infinite linear;
		}
	}
	&__msg {
		margin: 0;
		padding-top: $spacingXxs;
		font-weight: 400;
		font-size: 14px;
	}

	// STATES
	&__title.js-open__link .item-icon {
		padding-right: 26px;
		.icon-svg {
			width: 16px;
		}
	}
	.is-open &__title .icon-svg {
		transform: rotate(180deg) translateY(50%);
	}
	&.is-loading &__products {
		opacity: 0.5;
		&::before {
			visibility: visible;
			opacity: 1;
		}
	}
	& &__inp:checked + span {
		&::before {
			border-color: $colorText;
			background: transparent;
		}
		& > .icon-svg {
			color: $colorText;
		}
	}
	& &__inp:focus + span,
	.hoverevents &__inp:hover + span {
		&::before {
			border-color: $colorBlack;
		}
		& > .icon-svg {
			color: $colorBlack;
		}
	}

	// MEDIA QUERIES
	@media ($mdDown) {
		&__list {
			margin-left: -12px;
		}
		&__item {
			border-left-width: 12px;
		}
	}
	@media (min-width: 640px) {
		&__filter:not(.js-open) {
			display: flex;
			margin-left: -16px;
		}
		&__title {
			margin-bottom: 0;
			margin-left: 16px;
		}
		&__items {
			flex: 1;
			margin-left: 16px;
		}
		&__items.js-open__content {
			margin: -10px -10px -10px 6px;
			padding: 10px;
		}
		&__title:not(.js-open__link) {
			line-height: 54px;
		}
	}
	@media ($mdUp) {
		// &__title:not(.js-open__link) {
		// 	line-height: 70px;
		// }
		&__label {
			padding: 10px 20px 10px 55px;
		}
		&__inp + span {
			&::before {
				left: 20px;
			}
			& > .icon-svg {
				left: 25px;
			}
		}
	}
	@media ($lgUp) {
		&__title:not(.js-open__link) {
			min-width: 210px;
			text-align: right;
		}
	}
}
