.u-text-left {
	text-align: left;
}
.u-text-right {
	text-align: right;
}
.u-text-center {
	text-align: center;
}
.u-ta-c {
	display: block;
	margin-right: auto;
	margin-left: auto;
}
.u-text-justify {
	text-align: justify;
}
.u-text-nowrap {
	white-space: nowrap;
}
.u-text-lowercase {
	text-transform: lowercase;
}
.u-text-uppercase {
	text-transform: uppercase;
}
.u-text-capitalize {
	text-transform: capitalize;
}
.u-text-underline {
	text-decoration: underline;
}

.u-text-truncate {
	@include text-truncate();
}
.u-text-hide {
	@include text-hide();
}

.u-font-light {
	font-weight: 300;
}
.u-font-book {
	font-weight: 400;
}
.u-font-regular {
	font-weight: normal;
}
.u-font-bold {
	font-weight: bold;
}

.u-font-italic {
	font-style: italic;
}

.u-font-xs {
	font-size: 14px;
}
.u-font-sm {
	font-size: $fontSizeMobile;
}
.u-font-md {
	font-size: $fontSize;
}
.u-font-xl {
	font-size: 22px;
}
.u-font-small {
	font-size: 18px;
}
.u-font-smaller {
	font-size: 14px;
}
@media ($mdUp) {
	.u-font-lg {
		font-size: 22px;
	}
}
@media ($lgUp) {
	.u-font-xl {
		font-size: 30px;
	}
}
