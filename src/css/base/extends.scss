%reset-ul {
	margin: 0;
}

%reset-ul-li {
	margin: 0;
	padding: 0;
	background: none;
}

%reset-ol {
	@extend %reset-ul;
	counter-reset: none;
}

%reset-ol-li {
	@extend %reset-ul-li;
	position: static;
	&::before {
		content: normal;
		counter-increment: none;
		position: static;
		top: auto;
		left: auto;
	}
}

%grid {
	display: flex;
	flex-wrap: wrap;
}

%grid__cell {
	flex: 1 1 100%;
	max-width: 100%;
}

%grid--scroll {
	position: relative;
	display: flex;
	flex-wrap: nowrap;
	overflow: hidden;
	overflow-x: auto;
	.js & {
		overflow-x: hidden;
	}
	.grid__cell {
		flex-shrink: 0;
	}
	.slick-slide {
		.grid__cell {
			max-width: none;
		}
	}
}

%table-condensed {
	font-size: 14px;
	th {
		vertical-align: top;
		padding: 8px 14px 10px;
		font-weight: bold;
	}
	th,
	td {
		vertical-align: top;
		padding: 4px 16px;
		border: 1px solid $colorBd;
		text-align: center;
		&.u-text-left {
			text-align: left;
		}
		&.u-text-right {
			text-align: right;
		}
	}
	thead th {
		padding-top: 8px;
		border-top: none;
		border-bottom: 2px solid $colorBlack;
		color: $colorBlack;
		font-weight: 400;
	}

	// td:first-child,
	// th:first-child {
	// 	padding-left: 0;
	// 	text-align: left;
	// }
	// td:last-child,
	// th:last-child {
	// 	padding-right: 0;
	// 	text-align: right;
	// }
	tfoot {
		color: $colorBlack;
	}
}

%box-shadow {
	border-radius: $radius;
	background: $colorWhite;
	overflow: hidden;

	// box-shadow: 0 2px 10px rgba(black, 0.1);
	box-shadow: 0 2px 10px rgba(black, 0.21);
}

%menu-arrow {
	&::after {
		content: '';
		position: absolute;
		top: calc(100% + 5px);
		left: 50%;
		z-index: 1;
		width: 0;
		height: 0;
		border-width: 0 7px 7px 7px;
		border-style: solid;
		border-color: transparent transparent white transparent;
		visibility: hidden;
		opacity: 0;
		transform: translateX(-50%);
		transition: opacity $t, visibility $t;
	}
}
