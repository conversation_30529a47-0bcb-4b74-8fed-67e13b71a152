.js-suggest {
	$s: &;
	&--custom {
		position: relative;
		.f-suggest__results {
			@extend %box-shadow;
			position: absolute;
			top: 100%;
			right: 0;
			left: 0;
			z-index: 1;
			max-height: 185px;
			padding: 5px 20px;
			background: $colorWhite;
			overflow: hidden;
			overflow-y: auto;
			transition: opacity $t, visibility $t;
		}
		.f-suggest__results:not(.is-visible) {
			visibility: hidden;
			opacity: 0;
		}
		#{$s} {
			&__link {
				display: block;
				padding: 5px 0;
				border-bottom: 1px solid $colorGrayLight;
				&:last-child {
					border-bottom: none;
				}
			}
		}
	}
}
