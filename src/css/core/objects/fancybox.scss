/* stylelint-disable */
@import '@fancyapps/fancybox/dist/jquery.fancybox.min';

.fancybox-active.compensate-for-scrollbar {
	overflow: auto;
}
.fancybox-container.ppl-fancybox {
	// position: absolute;
}
.fancybox-bg {
	background: rgba(black, 0.54);
}
.fancybox-is-open .fancybox-bg {
	opacity: 1;
}
.ppl-fancybox .fancybox-toolbar {
	top: 21px;
	right: 21px;
	visibility: visible;
	opacity: 1;
	.fancybox-button {
		display: none;
		width: 20px;
		height: 20px;
		background: none;
		svg {
			position: absolute;
			top: 0;
			left: 0;
			width: 20px;
			height: 20px;
			&:hover {
				color: $colorBlack;
			}
		}
	}
	.fancybox-button--close {
		display: block;
	}
}
#ppl-parcelshop-map.fancybox-content {
	position: relative;
	vertical-align: top;
	width: 100%;
	height: 100%;
	overflow: hidden;
	.ppl-parcelshop-map-theme {
		max-height: 100%;
		overflow: hidden;
	}
}
#ppl-parcelshop-map .control-panel__content-wrapper {
	overflow: initial !important;
}

.fancybox-content {
	border-radius: 10px;
}

@media ($mdDown) {
	.fancybox-content {
		padding: 20px 20px 40px;
	}
}

@media (min-width: 765px) {
	#ppl-parcelshop-map {
		position: absolute !important;
		top: 6px !important;
		right: 6px !important;
		bottom: 6px !important;
		left: 6px !important;
		width: calc(100% - 12px) !important;
		height: calc(100vh - 12px) !important;
	}
	#ppl-parcelshop-map .control-panel__content {
		padding-bottom: 80px !important;
	}
	#ppl-parcel-shop-map {
		height: calc(100dvh - 100px) !important;
	}
}
