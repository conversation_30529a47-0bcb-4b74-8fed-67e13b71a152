import $ from 'jquery';
import './tools/svg4everybody';
// import { media } from './tools/MQ';

// Components
import * as ajaxForm from './components/ajaxForm';
import * as ajaxLink from './components/ajaxLink';
import * as cookieOld from './components/cookie-old';
import * as antispam from './components/antispam';
import * as fancybox from './components/fancybox';
import * as transportPayment from './components/transportPayment';
import * as suggest from './components/suggest';
import * as message from './components/message';
import * as objectfit from './components/objectfit';
import * as overlay from './components/overlay';
import * as toggleClass from './components/toggleClass';
import * as etarget from './components/etarget';
import * as open from './components/open';
import * as openTable from './components/open-table';
import * as touchopen from './components/touchopen';
import * as carousel from './components/carousel';
import * as linkSlide from './components/linkSlide';
import * as tabs from './components/tabs';
import * as guide from './components/guide';
import * as slider from './components/slider';
import * as gmap from './components/gmap';
import * as select from './components/select';
import * as gmapSimple from './components/gmapSimple';
import * as gtmClick from './components/gtmClick';
import * as gtmObserver from './components/gtmObserver';
import * as mobileMenu from './components/mobileMenu';
import * as maxLengthMessage from './components/maxLengthMessage';
import * as charCounter from './components/charCounter';
import * as scrollLock from './components/scrollLock';
import * as filter from './components/filter';
import * as popup from './components/popup';
import * as zoom from './components/zoom';
import * as autosubmit from './components/autoSubmit';
import * as avatar from './components/avatar';
import * as toggleChecked from './components/toggleChecked';
import * as showPassword from './components/showPassword';
import * as addItem from './components/addItem';
import * as petReg from './components/petReg';
import * as removeAddress from './components/removeAddress';
import * as formSubmit from './components/formSubmit';
import * as confirm from './components/confirm';
import * as objectfitFallback from './components/objectfitFallback';
import * as liteYoutube from './components/liteYoutube';
import * as numberInput from './components/numberInput';
import * as cookie from '@superkoders/cookie/src/js/components/cookie.js';
// import * as registrationEmail from './components/registrationEmail.js';
import * as accordeon from './components/accordeon.js';
import * as plzip from './components/plzip';
import * as validate from './components/validate';
import * as subscriptionInterval from './components/subscription-interval.js';
import * as tooltip from './components/tooltip.js';
import * as scroll from './components/scroll.js';
import * as branchAlert from './components/branchAlert.js';

// content load components
const componentsload = [antispam, carousel, slider, addItem, suggest, numberInput, tooltip, validate, plzip, subscriptionInterval, branchAlert];

// once delegated components
const components = [
	ajaxForm,
	ajaxLink,
	cookieOld,
	fancybox,
	transportPayment,
	message,
	objectfit,
	toggleClass,
	overlay,
	etarget,
	open,
	openTable,
	touchopen,
	linkSlide,
	tabs,
	guide,
	gmap,
	select,
	gmapSimple,
	gtmClick,
	gtmObserver,
	mobileMenu,
	maxLengthMessage,
	charCounter,
	scrollLock,
	filter,
	popup,
	zoom,
	autosubmit,
	toggleChecked,
	showPassword,
	removeAddress,
	petReg,
	avatar,
	formSubmit,
	confirm,
	objectfitFallback,
	liteYoutube,
	// registrationEmail,
	accordeon,
	tooltip,
	scroll,
].concat(componentsload);

window.App = {
	run(options) {
		// media('lgUp');
		this.options = options;

		const $target = $(document);
		components.forEach((component) => component.init($target));

		$(document).on('contentload', function(event) {
			// console.log('contentload');
			const $target = $(event.target);
			componentsload.forEach((component) => component.init($target));
		});

		if (document.documentElement.dataset.showCookie === 'true') {
			window.dataLayer.push({
				event: 'consentcookie_banner_impression',
			});
		}

		window.SKcookieAPI = cookie;
		cookie.init({ onUpdate: null, storeURL: 'https://cookie.superkoders.rocks/store' });

		window.dataLayer = window.dataLayer || [];
		const storages = ['ad_storage', 'analytics_storage'];

		cookie.on('update', (event, data) => {
			const isOptIn = storages.every((key) => data.storages[key] === 'granted');
			const isOptOut = storages.every((key) => data.storages[key] !== 'granted');

			window.dataLayer.push({
				event: isOptOut
					? 'consentcookie_banner_optout'
					: isOptIn
					? 'consentcookie_banner_optin_all'
					: data.storages['ad_storage'] === 'granted'
					? 'consentcookie_banner_optin_marketing'
					: 'consentcookie_banner_optin_statistics',
				consentID: data.id,
			});

			window.dataLayer.push({ event: 'consent-update' });
		});

		cookie.on('open', (event, data) => {
			window.dataLayer.push({
				event: 'consentcookie_banner_impression',
			});

			window.dataLayer.push({
				event: 'consentcookie_banner_open',
				consentID: data.id,
			});
		});
	},

	initComponent(component) {
		return component.init();
	},
};
