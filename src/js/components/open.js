import $ from 'jquery';

export const init = ($target) => {
	const $document = $(document);
	const selectors = {
		PARENT: 'js-open',
		PARENT_PERSONAL: 'js-open--personal',
		INPUT: 'js-open__inp',
		LINK: 'js-open__link',
		BOX: 'js-open__content',
		SELECTED_ADDRESS: 'f-items__item.is-checked',
		CLOSE_PERSONAL: '[data-close-personal]',
		SELECT_VAL: 'other_reason',
	};

	const handleChange = (event) => {
		const $toggle = $(event.target);
		const $parent = $toggle.closest('.' + selectors.PARENT);
		const $box = $($parent.find('.' + selectors.BOX)[0]);
		const animation = $parent.data('animation');

		if ($toggle.is(':checked')) {
			handleOpen($parent, $box, animation);
		} else {
			handleClose($parent, $box, animation);
		}
	};

	const handleSelect = (event) => {
		const $toggle = $(event.target);
		const $parent = $toggle.closest('.' + selectors.PARENT);
		const $box = $($parent.find('.' + selectors.BOX)[0]);
		const animation = $parent.data('animation');

		if ($toggle.val() == selectors.SELECT_VAL) {
			handleOpen($parent, $box, animation);
		} else {
			handleClose($parent, $box, animation);
		}
	};

	const handleToggle = (event) => {
		event.preventDefault();
		const $link = $(event.target);
		const $parent = $link.closest('.' + selectors.PARENT);
		const $box = $($parent.find('.' + selectors.BOX)[0]);
		const animation = $parent.data('animation');

		if ($box.is(':visible')) {
			handleClose($parent, $box, animation);
		} else {
			handleOpen($parent, $box, animation);
		}
	};

	const handleOpen = ($parent, $box, animation) => {
		$parent.addClass('is-open');
		if (animation == 'fade') {
			$box.fadeIn();
		} else {
			$box.slideDown();
		}
	};

	const handleClose = ($parent, $box, animation) => {
		$parent.removeClass('is-open');
		if (animation == 'fade') {
			$box.fadeOut();
		} else {
			$box.slideUp();
		}
	};

	const handleClosePersonal = () => {
		const $personal = $target.find('.' + selectors.PARENT_PERSONAL);
		const $box = $($personal.find('.' + selectors.BOX)[0]);
		const animation = $personal.data('animation');

		$personal.find('.' + selectors.INPUT).attr('checked', false);
		handleClose($personal, $box, animation);
	};

	$document
		.on('change', '.' + selectors.INPUT, handleChange)
		.on('click', '.' + selectors.LINK, handleToggle)
		.on('click', selectors.CLOSE_PERSONAL, handleClosePersonal)
		.on('change', 'select.' + selectors.INPUT, handleSelect);

	// trigger
	$('.js-open__inp').trigger('change');
};
