import $ from 'jquery';

export const init = ($target) => {
	const $elements = $target.find('.js-validate');

	$elements.each(function() {
		const $element = $(this);
		const streetMsg = $element.attr('data-validate-msg-street');
		const $street = $element.find('.js-validate__street');

		if ($street.length > 0) {
			$street.on('change', function() {
				const inputValue = $(this).val();
				const hasNumber = /\d/.test(inputValue);

				$(this)
					.next('.warning')
					.remove();

				if (!hasNumber) {
					$(this).after(`<span class="warning">${streetMsg}</span>`);
				}
			});
		}

		const $code = $element.find('.js-code');
		if ($code.length > 0) {
			$code.on('input', function() {
				const inputValue = $(this)
					.val()
					.trim();
				const isValid = /^[A-Z0-9]{5}$/i.test(inputValue);

				const $form = $(this).closest('form');
				const $h2 = $form.find('h2').first();

				$form.find('.message').remove();

				if (!isValid) {
					let errorMsg = 'Error.';
					const rawRules = $(this).attr('data-nette-rules');
					if (rawRules) {
						try {
							const rules = JSON.parse(rawRules);
							const patternRule = rules.find((rule) => rule.op === ':pattern');
							if (patternRule && patternRule.msg) {
								errorMsg = patternRule.msg;
							}
						} catch (e) {
							console.warn('Chyba při parsování data-nette-rules:', e);
						}
					}

					const html = `
						<p class="message message--error message--sm u-mb-xs">
							${errorMsg}
						</p>`;
					$h2.after(html);
				}
			});
		}

		const $reason = $element.find('.js-validate__reason');
		const $submit = $element.find('button[type="submit"], [type="submit"]');

		function checkReason() {
			if ($reason.length > 0 && $submit.length > 0) {
				if ($reason.val() === '') {
					$submit.prop('disabled', true);
				} else {
					$submit.prop('disabled', false);
				}
			}
		}

		if ($reason.length > 0 && $submit.length > 0) {
			checkReason();
			$reason.on('change', checkReason);
		}
	});
};
