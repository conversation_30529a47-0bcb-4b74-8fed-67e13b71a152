import loadGmapsApi from '../tools/loadGmapsApi';
import { styles } from '../constants/gmaps';

export const init = () => {
	const { options } = window.App;
	const mapElements = Array.from(document.querySelectorAll('.js-gmap-simple'));

	// kontrola elementu
	if (!mapElements || mapElements.length === 0) return;

	// kontrola options
	if (!options) {
		console.warn('V App.run() nejsou definovane options!');
		return;
	}

	// nactu API jen pokud je na strance element s mapou
	loadGmapsApi().then(() => {
		const gmaps = window.google.maps;

		const getIcon = () => ({
			url: `${options.assetsUrl}img/bg/map-point-calibra.svg`,
			origin: new gmaps.Point(0, -6),
			scaledSize: new gmaps.Size(75, 75),
		});

		mapElements.forEach((el) => {
			el.classList.remove('is-loading');
			const mapCenter = el.dataset.location ? JSON.parse(el.dataset.location) : null;

			if (!mapCenter) return;

			// options
			const mapOptions = {
				center: mapCenter,
				zoom: 9,
				disableDefaultUI: true,
			};

			// vytvoreni mapy
			const map = new gmaps.Map(el, mapOptions);

			// vytvoreni markeru
			const icon = getIcon();
			new gmaps.Marker({
				icon,
				map,
				position: mapCenter,
			});

			map.setOptions({ styles });
		});
	});
};
