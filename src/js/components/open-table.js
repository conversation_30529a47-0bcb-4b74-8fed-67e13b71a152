import $ from 'jquery';

export const init = () => {
	const $document = $(document);

	$document.on('click', '.js-open-table__link', function(event) {
		event.preventDefault();
		const $link = $(this);
		const $row = $link.closest('.js-open-table');
		const $detail = $row.next('.js-open-table__content');
		if ($detail.is(':visible')) {
			$detail.slideUp();
		} else {
			$detail.slideDown();
		}
	});
};
