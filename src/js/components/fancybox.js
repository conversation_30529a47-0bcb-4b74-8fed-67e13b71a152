import $ from 'jquery';
import '@fancyapps/fancybox';

/*!
 * Examples at http://fancyapps.com/fancybox/
 * License: www.fancyapps.com/fancybox/#license
 * License Name: <PERSON><PERSON><PERSON>
 * License key: 669ff49044a69167735d4c8de99d3a8a
 */

export const init = ($target) => {
	$('[data-fancybox][data-type="ajax"]').fancybox({
		afterShow(obj) {
			obj.current.$content.trigger('contentload');
		},
		touch: false,
	});

	// AJAX
	$target.on('click', '.js-fancybox-ajax', function(e) {
		e.preventDefault();
		const $this = $(this);

		$.fancybox.close(true);

		$.fancybox.open({
			src: $this.attr('href'),
			type: 'ajax',

			opts: {
				ajax: {
					settings: {
						dataType: 'html',
						dataFilter(data) {
							try {
								const resp = JSON.parse(data);

								if (resp.redirect) {
									window.location.replace(resp.redirect);
								} else {
									const fancySnippetName = `snippet--${$this.data('snippet')}`;
									// TODO REPLACE All SNIPPETs
									return `<div id="${fancySnippetName}">${resp.snippets[fancySnippetName]}</div>`;
								}
							} catch (e) {
								if (e instanceof SyntaxError) {
									return data;
								}
								// console.log(e);
							}
						},
					},
				},
				infobar: false,
				loop: false,
				arrows: false,
				toolbar: false,
				afterLoad(instance, current) {
					current.$content.trigger('contentload');
				},
			},
		});
	});

	// AFTER LOAD
	$target.find('.js-fancybox-open-after-load').each(function() {
		var $this = $(this);

		setTimeout(function() {
			$.fancybox.open({
				src: '#' + $this.attr('id'),
				type: 'inline',
				afterLoad: function(instance, current) {
					current.$content.trigger('contentload');
				},
			});
		}, 3000);
	});

	// CUSTOM
	$target.on('click', '[data-fancybox-custom]', function(e) {
		e.preventDefault();
		const $this = $(this);
		const href = $this.attr('href');
		const id = $this.attr('data-id');
		const nextId = $this.attr('data-next-subscription-order-id');
		const subHash = $this.attr('data-subscription-hash');

		const $element = $target.find(href);
		const $button = $element.find('a');

		if (nextId) {
			$button.attr('href', window.location.href + '&do=removeFromSubscription&hash=' + subHash + '&id=' + id + '&nextOrderId=' + nextId + '&subscriptionHash=' + subHash);
		} else {
			$button.attr('href', window.location.href + '&do=removeFromSubscription&hash=' + subHash + '&id=' + id + '&subscriptionHash=' + subHash);
		}

		$.fancybox.open({
			src: href,
			type: 'inline',
			afterLoad(instance, current) {
				current.$content.trigger('contentload');
			},
		});
	});

	// DEACTIVATE
	$target.on('click', '[data-fancybox-deactivate]', function(e) {
		e.preventDefault();
		const $this = $(this);
		const href = $this.attr('href');
		const subHash = $this.attr('data-subscription-hash');

		const $element = $target.find(href);
		const $button = $element.find('a');

		const $formInput = $("#cancelFormSubscriptionHash");
		$formInput.val(subHash);

		let link = null;

		if (!window.location.href.includes('?')) {
			link = window.location.href + '?do=deactivateSubscription&subscriptionHash=' + subHash;
		} else {
			link = window.location.href + '&do=deactivateSubscription&subscriptionHash=' + subHash;
		}

		$button.attr('href', link);

		$.fancybox.open({
			src: href,
			type: 'inline',
			afterLoad(instance, current) {
				current.$content.trigger('contentload');
			},
		});
	});

	// DEACTIVATE
	$target.on('click', '[data-fancybox-loggedin]', function(e) {
		e.preventDefault();
		const $this = $(this);
		const href = $this.attr('href');

		$.fancybox.open({
			src: href,
			type: 'inline',
			afterLoad(instance, current) {
				current.$content.trigger('contentload');
			},
		});
	});
};
