{"name": "sk/calibra", "type": "project", "require": {"php": ">= 8.2", "ext-iconv": "*", "ext-json": "*", "ext-soap": "*", "ext-zip": "*", "contributte/application": "^0.5.1", "contributte/console": "^0.9.2", "contributte/console-extra": "^0.7.2", "contributte/elastica": "^1.1", "contributte/logging": "^v0.6.3", "contributte/messenger": "^0.1.0", "contributte/monolog": "^0.5.2", "contributte/redis": "^0.6.0", "contributte/tracy": "^0.5.1", "cweagans/composer-patches": "^1.7", "dibi/dibi": "^4.0", "elasticsearch/elasticsearch": "^7.17", "facebook/graph-sdk": "^5.1", "google/apiclient": "^2.7", "gopay/payments-sdk-php": "1.6.1", "guzzlehttp/guzzle": "^7.8", "heureka/overeno-zakazniky": "^3.0", "jaybizzle/crawler-detect": "1.*", "kdyby/forms-replicator": "^2.0", "latte/latte": "^2.9", "league/csv": "^9.1", "mailchimp/marketing": "^3.0", "marc-mabe/php-enum": "^3.0", "mpdf/mpdf": "^8.0", "nette/application": "^3.1", "nette/bootstrap": "^3.1", "nette/caching": "^3.1", "nette/component-model": "^3.0", "nette/database": "^3.0", "nette/di": "^3.0", "nette/finder": "^2.5", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0", "nette/robot-loader": "^3.0", "nette/security": "^3.0", "nette/utils": "^3.0", "nextras/migrations": "^3.1", "nextras/orm": "^4.0", "pelago/emogrifier": "^v7.0.0", "php-curl-class/php-curl-class": "^9.0", "sentry/sdk": "^3.1", "symfony/lock": "^6.0", "symfony/redis-messenger": "^5.4", "texy/texy": "^3.0", "tracy/tracy": "^2.8", "ublaboo/datagrid": "^6.10", "venca-x/social-login": "^1.2.0"}, "require-dev": {"deployer/deployer": "^7.0@rc", "nette/tester": "^2.0", "nextras/orm-phpstan": "^1.0", "orisai/coding-standard": "^3.8.0", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8.2", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.1"}, "autoload": {"classmap": ["app/Bootstrap.php"]}, "config": {"platform": {"php": "8.2.3"}, "sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true, "cweagans/composer-patches": true, "php-http/discovery": true}}, "scripts": {"phpstan": ["phpstan analyse -c .phpstan.neon --memory-limit=2G"], "cs": ["phpcs --standard=phpcs.xml app/AdminModule/components/SettingsForm", "phpcs --standard=phpcs.xml app/Console app/Infrastructure", "phpcs --standard=phpcs.xml app/FrontModule/components/ContactForm app/FrontModule/components/CustomContentRenderer", "phpcs --standard=phpcs.xml app/FrontModule/components/OrderStep2Form", "phpcs --standard=phpcs.xml app/model/CustomContent app/model/CustomField", "phpcs --standard=phpcs.xml app/model/Document app/model/Orm/Document", "phpcs --standard=phpcs.xml app/model/NewsletterCampaign app/model/Orm/NewsletterCampaign app/model/Orm/NewsletterCampaignFilter", "phpcs --standard=phpcs.xml app/model/PriceLogs app/model/Orm/ProductVariantPriceLog", "phpcs --standard=phpcs.xml app/model/Orm/File app/model/Orm/MutationSetting app/model/Orm/SearchLog app/model/Orm/SearchLogDetail"], "lint": ["parallel-lint --blame app tests --exclude tests/var"], "latte-lint": ["latte-lint app"], "cs-fix": ["phpcbf --standard=phpcs.xml app/AdminModule/components/SettingsForm", "phpcbf --standard=phpcs.xml app/Console app/Infrastructure", "phpcbf --standard=phpcs.xml app/FrontModule/components/ContactForm app/FrontModule/components/CustomContentRenderer", "phpcbf --standard=phpcs.xml app/FrontModule/components/OrderStep2Form", "phpcbf --standard=phpcs.xml app/model/CustomContent app/model/CustomField", "phpcbf --standard=phpcs.xml app/model/Document app/model/Orm/Document", "phpcbf --standard=phpcs.xml app/model/NewsletterCampaign app/model/Orm/NewsletterCampaign app/model/Orm/NewsletterCampaignFilter", "phpcbf --standard=phpcs.xml app/model/PriceLogs app/model/Orm/ProductVariantPriceLog", "phpcbf --standard=phpcs.xml app/model/Orm/File app/model/Orm/MutationSetting app/model/Orm/SearchLog app/model/Orm/SearchLogDetail"], "qc": ["@lint", "@cs", "@phpstan"]}, "extra": {"phpstan": {"includes": ["extension.neon"]}, "patches": {"contributte/elastica": {"Missing init for $totalTime (https://github.com/contributte/elastica/pull/10)": "patches/contributte-elastica-total-time.patch"}}}}