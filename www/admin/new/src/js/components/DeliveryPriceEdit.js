import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';

export default class DeliveryPriceEdit extends Controller {
	static targets = ['price', 'tag', 'name', 'freeFrom'];
	static values = {
		id: String,
	};

	priceState = {
		id: null,
		name: undefined,
		price: undefined,
		tag: undefined,
		freeFrom: null,
	};

	connect() {
		useDispatch(this);
		this.priceState.id = this.idValue;
	}

	edit() {
		if (this.hasNameTarget) {
			this.priceState.name = this.nameTarget.options[this.nameTarget.selectedIndex].text;
		}
		if (this.hasPriceTarget) {
			const item = this.priceTarget;
			this.priceState.price = `${item.dataset.currency} ${item.value}`;
		}
		if (this.hasTagTarget) {
			this.priceState.tag = this.tagTarget.options[this.tagTarget.selectedIndex].text;
		}

		if (this.hasFreeFromTarget && this.freeFromTarget.value.length > 0) {
			const item = this.freeFromTarget;
			this.priceState.freeFrom = `${item.value}`;
		}

		this.dispatch('updateValues', { values: this.priceState });
	}
}
