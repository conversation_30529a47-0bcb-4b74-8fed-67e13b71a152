# Apache configuration file (see httpd.apache.org/docs/current/mod/quickreference.html)

# !!!!!!!!!!!
# pri zmene obsahu, nezapomenout pregenerovat modul presmerovani - staci preulozit jedno pravidlo, a vse se pregeneruje
# PS. mělo by se dít automaticky při deploy
# !!!!!!!!!!!

# STAGE HTTP Basic auth
CGIPassAuth On
<IfModule mod_setenvif.c>
	# f**k you apache (https://support.deskpro.com/en/kb/articles/missing-authorization-headers-with-apache)
	SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1
</IfModule>


<FilesMatch "cachetool-.*\.php$">
    Satisfy Any
    Allow from all
</FilesMatch>

# PHP settings
#php_flag magic_quotes_gpc off 
#php_flag register_globals off

# disable directory listing
#Options -Indexes

# vypnuti gzip
#SetEnv no-gzip 1

# dalsi zpusob vypnuti gzip
#RewriteRule ^(.*)$ $1 [NS,E=no-gzip:1,E=dont-vary:1]

# zapnuti brotli
#SetOutputFilter BROTLI_COMPRESS
#SetEnvIfNoCase Request_URI \.(?:gif|jpe?g|png)$ no-brotli

# enable cool URL
<IfModule mod_rewrite.c>
	RewriteEngine On

##MARKER_START##
##MARKER_FINISH##


	# prevents files starting with dot to be viewed by browser
#	RewriteRule /\.|^\. - [F]
	RewriteRule /\.|^\. /404 [L]


# ****** temporary domains ******
# *******************************
	RewriteCond %{HTTP_HOST} ^calibra.www6.superkoderi.cz$
	RewriteRule ^(.*)$ https://www.mojecalibra.cz/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-prod.vs3.superkoderi.cz$
	RewriteRule ^(.*)$ https://www.mojecalibra.cz/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra2.www6.superkoderi.cz$
	RewriteRule ^(.*)$ https://www.mycalibra.eu/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-sk.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mojacalibra.sk/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-ae.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.ae/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-hu.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.hu/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-it.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.it/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-hr.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.hr/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-sl.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.si/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-es.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.es/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-uk.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.uk/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-ie.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.ie/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-in.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.in/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-cl.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.cl/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-lt.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.lt/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-pl.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.pl/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-gr.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.gr/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-nl.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.nl/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-at.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.at/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-de.www6.superkoderi.cz$
	RewriteRule ^(.*)$ https://www.mycalibra.de/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-be-fr.www6.superkoderi.cz$
	RewriteRule ^(.*)$ https://fr.mycalibra.be/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-be-nl.www6.superkoderi.cz$
	RewriteRule ^(.*)$ https://www.mycalibra.be/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^calibra-fr.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.fr/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-ch-fr.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://fr.mycalibra.ch/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-ch-de.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.ch/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^calibra-ro.www6.superkoderi.cz$
    RewriteRule ^(.*)$ https://www.mycalibra.ro/$1  [R=301,L]


# ****** EU ******
# ****************
	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.dk$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.is$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.ru$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.cn$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

#	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.si$
#	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=301,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.us$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.rs$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.lv$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.pt$
    RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=307,L]

	RewriteCond %{HTTP_HOST} ^(.+\.)?mycalibra.lv$
	RewriteRule ^(.*)$ https://www.mycalibra.eu  [R=301,L]


# ****** misc. domains ******
# ***************************
 	RewriteCond %{HTTP_HOST} ^(.+\.)?paniceksnu.cz$
	RewriteRule ^(.*)$ https://www.mojecalibra.cz  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.cz$
    RewriteRule ^(.*)$ https://www.mojecalibra.cz/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^www.mycalibra.cz$
    RewriteRule ^(.*)$ https://www.mojecalibra.cz/$1  [R=301,L]

	RewriteCond %{HTTP_HOST} ^mycalibra.sk$
    RewriteRule ^(.*)$ https://www.mojacalibra.sk/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^www.mycalibra.sk$
    RewriteRule ^(.*)$ https://www.mojacalibra.sk/$1  [R=301,L]


# ****** without www -> with www ******
# *************************************
	RewriteCond %{HTTP_HOST} ^mojecalibra.cz
	RewriteRule (.*) https://www.mojecalibra.cz/$1 [R=301,QSA,L]

	RewriteCond %{HTTP_HOST} ^mycalibra.eu
	RewriteRule (.*) https://www.mycalibra.eu/$1 [R=301,QSA,L]

	RewriteCond %{HTTP_HOST} ^mojacalibra.sk
    RewriteRule (.*) https://www.mojacalibra.sk/$1 [R=301,QSA,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.ae
    RewriteRule (.*) https://www.mycalibra.ae/$1 [R=301,QSA,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.hu
    RewriteRule ^(.*)$ https://www.mycalibra.hu/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.it
    RewriteRule ^(.*)$ https://www.mycalibra.it/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.hr
    RewriteRule ^(.*)$ https://www.mycalibra.hr/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.si
    RewriteRule ^(.*)$ https://www.mycalibra.si/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.es
    RewriteRule (.*) https://www.mycalibra.es/$1 [R=301,QSA,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.uk
    RewriteRule ^(.*)$ https://www.mycalibra.uk/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.ie
    RewriteRule ^(.*)$ https://www.mycalibra.ie/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.in
    RewriteRule ^(.*)$ https://www.mycalibra.in/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.cl
    RewriteRule ^(.*)$ https://www.mycalibra.cl/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.lt
    RewriteRule ^(.*)$ https://www.mycalibra.lt/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.pl
    RewriteRule ^(.*)$ https://www.mycalibra.pl/$1  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.gr
    RewriteRule ^(.*)$ https://www.mycalibra.gr  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.nl
    RewriteRule ^(.*)$ https://www.mycalibra.nl  [R=301,L]

	RewriteCond %{HTTP_HOST} ^mycalibra.at
    RewriteRule ^(.*)$ https://www.mycalibra.at  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.de
    RewriteRule ^(.*)$ https://www.mycalibra.de  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.be
    RewriteRule ^(.*)$ https://www.mycalibra.be  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.fr
    RewriteRule ^(.*)$ https://www.mycalibra.fr  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.ch
    RewriteRule ^(.*)$ https://www.mycalibra.ch  [R=301,L]

    RewriteCond %{HTTP_HOST} ^mycalibra.ro
    RewriteRule ^(.*)$ https://www.mycalibra.ro  [R=301,L]


	#edit: je to potreba? resi si aplikace sama
    #odstraneni lomitka na konci
#	RewriteCond %{REQUEST_URI} !/superadmin/.*
#	RewriteCond %{REQUEST_URI} ^(.+)/$
#   RewriteRule (.*)/$ /$1 [R=301,QSA,L]

    # odstraneni ?
    RewriteCond %{THE_REQUEST} ^[^\s]+\s+[^?]*?\?
    RewriteCond %{QUERY_STRING} =""
    # For any version of Apache:
    RewriteRule .? %{REQUEST_URI}? [R=301,L]
    # For Apache 2.4+:
    # RewriteRule .? %{REQUEST_URI} [R=301,L,QSD]

	# index.php
	RewriteCond %{THE_REQUEST} ^[A-Z]{3,9}\ /index\.php
    RewriteRule ^index\.php$ / [L,R=301]

	RewriteBase /

	# front controller
	RewriteCond %{REQUEST_FILENAME} !-f
#	RewriteCond %{REQUEST_FILENAME} !-d #adresare nebudou vracet 403 ale 404 (aplikacní)
	RewriteRule !\.(pdf|js|ico|gif|jpg|jpeg|png|webp|css|rar|zip|tar\.gz)$ index.php [L]
</IfModule>

# enable gzip compression
<IfModule mod_deflate.c>
	AddOutputFilterByType DEFLATE text/html application/x-javascript text/css application/javascript text/javascript text/plain text/xml application/json application/vnd.ms-fontobject application/x-font-opentype application/x-font-truetype application/x-font-ttf application/xml font/eot font/opentype font/otf image/svg+xml image/vnd.microsoft.icon
</IfModule>


#<IfModule mod_deflate.c>
#   <FilesMatch "\.(js|css|ico|txt|htm|html|php|ttf|woff|eot|svg)$">
#   SetOutputFilter DEFLATE
#   </FilesMatch>
#</IfModule>

# Speed up caching
FileETag MTime Size

# Expires
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresDefault "access plus 366 days"
    #ExpiresDefault "access plus 300 seconds"

    # Future Expires Headers
    <FilesMatch "\.(ico|pdf|flv|jpg|jpeg|png|webp|gif|js|css|swf)$">
    ExpiresDefault A31536000
    </FilesMatch>
</IfModule>
