# Production
# ==========

# zaloha DB
50 4 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/_dbbackup/backupDB.php > /dev/null 2>&1

# Noviko
# import produktů: 1x denně v noci
# cron: novikoProductImport-prod
50 5 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:product:importHD > /dev/null 2>&1

# update cen produktů: 1x denně večer (přeceňují většinou přes den)
# cron: novikoProductPrice-prod
30 18 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:product:priceHD > /dev/null 2>&1

# update skladu produktů CS, SK: kaž<PERSON>u půl hodinu přes den, pauza na oběd, pak večer po hodině -> limit volání API fce pro sklad je 19/den (20 - 1 volání pro import)
# cron: novikoProductStock-cs1-prod
0 8,9,10,11,12,13,14,15,16,17,18,19 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:product:stockHD -m cs > /dev/null 2>&1
# cron: novikoProductStock-cs2-prod
30 9,10,11,13,14,15,16 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:product:stockHD -m cs > /dev/null 2>&1

# update skladu produktů PL: každou půl hodinu přes den, pauza na oběd, pak večer po hodině -> limit volání API fce pro sklad je 19/den (20 - 1 volání pro import)
# cron: novikoProductStock-pl1-prod
0 8,9,10,11,12,13,14,15,16,17,18,19 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:product:stockHD -m pl > /dev/null 2>&1
# cron: novikoProductStock-pl2-prod
30 9,10,11,13,14,15,16 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:product:stockHD -m pl > /dev/null 2>&1


# Noviko order
# odeslani novych objednavek
# cron: novikoOrderExport-prod
*/5 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:order:export > /dev/null 2>&1

# zmena stavu nevyrizenych objednavek -> po treti a sedme hodine odpoledne (jindy nema smysl - zmeny v obj se dejou v zavislosti na expidici = balik zabali a predaji dopravci, coz se deje ve tri a v pet odpoledne
# cron: novikoOrderUpdateProgress-prod
7 15,19 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:order:updateprogress > /dev/null 2>&1

# ElasticSearch
# cron: elasticSynonymsUpdate-prod
*/5 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console elastic:synonyms:update > /dev/null 2>&1
# cron: elasticDataFill-prod
15 3 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console elastic:data:fill > /dev/null 2>&1

# Eshop
# cron: dpdOrderDelivered-prod
40 1 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console dpd:order:delivered > /dev/null 2>&1
# cron: sitemap-prod
0 */4 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console sitemap > /dev/null 2>&1
# cron: robots-prod
30 4 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console robots > /dev/null 2>&1
# cron: searchLog-prod
32 4 2 * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:searchLog:clean > /dev/null 2>&1

# v noci - report pripojenych darku co nejsou skladem
# cron: eshopPresentsCheck-prod
0 4 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:presents:check > /dev/null 2>&1

# mazlici - 1x za mesic prvniho prepocet parametru vek psa/kocky podle narozeni
40 4 1 */1 * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:animal:age > /dev/null 2>&1

#prepocet vsech cen
# cron: eshopVariantPrices-prod
0 0 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:variant:prices > /dev/null 2>&1

#prepocet po update skupiny
# cron: eshopVariantPrices-checkUpdate-prod
*/5 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:variant:prices checkUpdate > /dev/null 2>&1

# user - prepocet obratu objednavek za posledni rok
# cron: eshopUserOrder-prod
0 4 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:user:order > /dev/null 2>&1

# user - prepocet mazliku & score
# cron: productUserScore-prod
* * * * * ( php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console product:user:score > /dev/null 2>&1 )
# cron: productUserScore-1-prod
* * * * * ( sleep 15 ; php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console product:user:score > /dev/null 2>&1 )
# cron: productUserScore-2-prod
* * * * * ( sleep 30 ; php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console product:user:score > /dev/null 2>&1 )
# cron: productUserScore-3-prod
* * * * * ( sleep 45 ; php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console product:user:score > /dev/null 2>&1 )

# user - smazani nedokoncenych registraci
# cron: eshopUserUnfinished-prod
30 4 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:user:unfinished > /dev/null 2>&1

# bestseller
# cron: eshopProductSoldcount-prod
15 4 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console eshop:product:soldcount > /dev/null 2>&1


# feed
# cron: feedXmlHeureka-prod
30 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console feed:xml:heureka > /dev/null 2>&1
# cron: feedXmlGoogle-prod
35 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console feed:xml:google > /dev/null 2>&1

# task processor - ruzné tasky z fronty tasků, např. export faktur
# cron: task-prod
*/5 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console task -l 5 > /dev/null 2>&1

# awaiting payment - zasilani mailu u objednavek ve stavu CREATED a starsich 3 dnu
# cron: awaitingPayment-prod
00 01 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console awaitingpayment > /dev/null 2>&1

# send retry payment mail - pokud objednávka nebyla zaplacena při prvním pokusu online, pošle se e-mail (template orderUnpaid)
# cron: sendRetryPayment-prod
* * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console SendRetryPaymentMail > /dev/null 2>&1

# storno objednavky po zaslani opakování platby a je nezaplacena
# cron: stornoOrderAfterRetryPayment-prod
* * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console StornoOrderAfterRetryPayment > /dev/null 2>&1

# generate invoice number
# cron: generateOrderInvoiceNumber-prod
* * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console GenerateOrderInvoiceNumber > /dev/null 2>&1

# subscription - zpracovani objednavek s predplatnym
# cron: subscriptionOrder-prod
*/5 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console subscription:order > /dev/null 2>&1

# subscription - admin report
# cron: subscriptionReport-prod
* 5 * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console subscription:report > /dev/null 2>&1

# kontrola zda byl balik dorucen pres PPL kuryra *
# cron: pplOrderDelivered-prod
*/30 * * * * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console ppl:order:delivered > /dev/null 2>&1

# Vypnout e-shop o pulnoci 31.12. a zapnout v 6:00 1.1.
#59 23 31 12 * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console foo --enable false > /dev/null 2>&1
#0 6 1 1 * php8.2 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console foo --enable true > /dev/null 2>&1

# extra spusteni prepoctu cen a DPH !!!!!
#0 5 1 1 * php8.1 /var/www/calibra-prod.vs3.superkoderi.cz/current/bin/console noviko:product:priceHD > /dev/null 2>&1
