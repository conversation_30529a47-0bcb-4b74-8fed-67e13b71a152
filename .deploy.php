<?php

declare(strict_types=1);

use function Deployer\{after, host, import, run, set, task, upload};

// naimportuji soubory Deployeru, ze kterých využiji definice
import('recipe/common.php');
import('contrib/cachetool.php');

// nadefinuji soubory a <PERSON>, kter<PERSON> se mají sd<PERSON>let napří<PERSON> verzemi
set('shared_files', [
	'app/config/config.local.neon',
	'www/sitemap.xml',
	'www/robots.txt',
	'nettelog/pay-response.log',
	'nettelog/pay-response-0.log',
	'nettelog/pay-response-1.log',
	'nettelog/dpd-order-delivered.log',
	'nettelog/gopay-chosen-payment-instrument.log',
	'nettelog/subscription.log',
	'nettelog/invoicenumber.txt',
	'nettelog/ppl-order-delivered.log',
]);

set('shared_dirs', [
	'www/data',
	'www/exports',
	'www/sitemaps',
	'nettelog/noviko',
	'temp/archive',
	'temp/invoice_export'
]);

// nadefinuji údaje o serveru, převez<PERSON> je přitom z proměnných prostředí nastavených u deploymentu
$env = getenv();
host('host')
	->set('hostname', $env['DEPLOYMENT_HOSTNAME'])
	->set('remote_user', $env['DEPLOYMENT_USER'])
	->set('deploy_path', $env['DEPLOYMENT_PATH'])
	->set('cachetool_args', '--fcgi=/run/php/php8.2-fpm.sock')
	->set('php_version', $env['DEPLOYMENT_PHP_VERSION']);

// definuji task, který nahraje aplikační soubory na server přes rsync
// Deployer ve výchozím nastavení natahuje soubory na server pomocí Gitu, což nechceme
const TASK_COPY_APPLICATION = 'copy-application';
task(TASK_COPY_APPLICATION, function (): void {
	$config = ['options' => [ "--exclude-from=excludeFromDeploy" ]];
	upload('.', '{{release_path}}', $config);
});

// definuji task, který předehřeje cache Latte šablon a DI kontejneru
const TASK_WARM_UP_CACHE = 'warm-up-cache';
task(TASK_WARM_UP_CACHE, function (): void {
	run('{{bin/php}} {{release_path}}/cmd.php contributte:cache:generate');
});

// generování redirectů do .htaccess
const TASK_GENERATE_REDIRECTS = 'generate-redirects';
task(TASK_GENERATE_REDIRECTS, function (): void {
	run('{{bin/php}} {{release_path}}/cmd.php redirects');
});

const TASK_RUN_MIGRATIONS = 'run-migrations';
task(TASK_RUN_MIGRATIONS, function (): void {
	run('{{bin/php}} {{release_path}}/bin/console migrations:continue');
});

// odtud se stáhne cachetool, což je nástroj, který po deployi správně pročistí opcache
// (pokud nemám na projektu zapnutou opcache, zapnu ji!)
set('cachetool_url', 'https://github.com/gordalina/cachetool/releases/download/8.4.0/cachetool.phar');

// tady už definuju celý proces deploye
task('deploy', [
	// udělám nezbytnou přípravu
	'deploy:info',
	'deploy:setup',
	'deploy:lock',
	'deploy:release',

	// nahraju zdrojový kód na server
	'deploy:copy_dirs',
	TASK_COPY_APPLICATION,

	// nastavím sdílené soubory a složky
	'deploy:shared',
	'deploy:writable',

	// todo maintenance mode?
	TASK_RUN_MIGRATIONS,
	TASK_WARM_UP_CACHE,
	TASK_GENERATE_REDIRECTS,

	// přepnu symlink na novou verzi
	'deploy:symlink',

	// uklidím po sobě
	'deploy:unlock',
	'deploy:cleanup',
	'deploy:success',
]);

// po přepnutí symlinku vyčistím PHP cache
after('deploy:symlink', 'cachetool:clear:opcache');
after('deploy:symlink', 'cachetool:clear:stat');

// pokud cokoliv selže, uklidím po sobě, abych nezablokoval následné deploye
after('deploy:failed', 'deploy:unlock');
